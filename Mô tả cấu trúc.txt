﻿src/
├── Core/                        # Domain thuần: Entity, Enum, ValueObject
│   ├── Entities/               # Tất cả entity dùng cho cả CRUD và logic nghiệp vụ
│   ├── Enums/                  # Trạng thái, lo<PERSON><PERSON> giao dịch, vai trò,...
│   ├── ValueObjects/           # Tiền tệ, địa chỉ, v.v.
│   ├── Interfaces/             # IAggregateRoot, IAuditableEntity, etc.
│   └── Events/                 # Domain event (TransactionCompletedEvent, ...)

├── Application/                # Dùng CQRS + service business logic
│   ├── Features/               # Tập trung các use-case phức tạp
│   │   ├── Transactions/
│   │   │   ├── Commands/
│   │   │   ├── Queries/
│   │   │   ├── Handlers/
│   │   │   ├── Validators/
│   │   │   ├── Events/
│   │   │   └── DTOs/
│   │   ├── Reconciliation/
│   │   │   └── GenerateDailyReportCommand.cs
│   │   ├── Payments/
│   │   │   └── VerifyPaymentCallbackCommand.cs
│   │   └── Tags/               # CRUD đơn giản → dùng base service
│   │       └── TagDto.cs
│
│   ├── Services/               # ✅ BaseService dùng cho entity đơn giản
│   │   ├── Interfaces/
│   │   │   ├── IBaseService<TDto>
│   │   │   └── ITagService.cs       # Service riêng nếu cần custom 1 phần
│   │   └── Implementations/
│   │       ├── BaseService<TEntity, TDto>
│   │       └── TagService.cs
│
│   ├── Interfaces/             # Interface cho tầng app: External service, filter
│   │   ├── ICurrentUserService.cs
│   │   ├── IAuditService.cs
│   │   └── IPaymentGatewayAdapter.cs
│
│   └── Behaviors/              # MediatR behaviors (Logging, Validation, Retry...)

├── Infrastructure/
│   ├── Persistence/            # Xử lý DB, repo, unit of work
│   │   ├── DbContext/
│   │   ├── Repositories/
│   │   │   ├── Interfaces/
│   │   │   │   └── IRepository<T>
│   │   │   └── Implementations/
│   │   │       └── BaseRepository<T>
│   │   ├── UnitOfWork/
│   │   │   └── IUnitOfWork.cs, UnitOfWork.cs
│   │   └── Interceptors/
│   │       └── AuditInterceptor.cs      # Tự động lưu audit log
│
│   ├── ExternalSystems/
│   │   ├── Napas/
│   │   │   └── NapasClient.cs, SignatureHelper.cs
│   │   └── Redis/
│
│   └── DependencyInjection/
│       └── ServiceRegistration.cs

├── Shared/
│   ├── Results/
│   │   ├── CommonResult.cs
│   │   └── PaginatedResult.cs
│   ├── Constants/
│   ├── Utils/
│   ├── Exceptions/
│   └── Extensions/

├── API/
│   ├── Controllers/
│   │   ├── BaseController<T, TDto>.cs   # ✅ Dùng cho CRUD đơn giản
│   │   ├── TagsController.cs            # CRUD dùng base
│   │   ├── TransactionsController.cs    # Sử dụng MediatR CQRS
│   │   └── CallbackController.cs        # Nhận callback Napas
│   ├── Models/                          # Request/Response models
│   ├── Filters/                         # ExceptionFilter, SignatureFilter
│   ├── Middlewares/
│   │   └── RequestTrackingMiddleware.cs
│   ├── Extensions/
│   └── Program.cs






Thực hiện Mifration bên ngoài solution 
dotnet ef migrations add UpdateCreateGardenCropRelationship -p Infrastructure/Infrastructure.csproj -s API/API.csproj