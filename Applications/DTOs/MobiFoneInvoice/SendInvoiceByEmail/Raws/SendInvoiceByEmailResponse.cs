using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.SendInvoiceByEmail.Raws;

/// <summary>
/// Response DTO cho API 4.8 Gửi mail phát hành hóa đơn cho người mua
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class SendInvoiceByEmailResponse
{
    /// <summary>
    /// Trạng thái thành công
    /// Kiểu: String
    /// Giá trị: "true" khi thành công
    /// </summary>
    [JsonPropertyName("oke")]
    public string oke { get; set; } = string.Empty;
}

/// <summary>
/// Response wrapper cho trường hợp có lỗi
/// </summary>
public class SendInvoiceByEmailErrorResponse
{
    /// <summary>
    /// Thông báo lỗi
    /// Kiểu: String
    /// Các lỗi có thể xảy ra:
    /// - "Hóa đơn không tồn tại": Lỗi này xảy ra khi hóa đơn với id được truyền vào không tìm thấy trong hệ thống
    /// - "Id không được bỏ trống": Thông báo này xuất hiện nếu trường id trong JSON input bị thiếu, có giá trị null, hoặc là một chuỗi rỗng
    /// - "Id không đúng định dạng": Lỗi này cho biết giá trị của trường id không phải là định dạng GUID hợp lệ
    /// - "Hóa đơn chưa được ký": Lỗi này xảy ra khi hóa đơn mà bạn đang cố gắng gửi email chưa được ký
    /// </summary>
    [JsonPropertyName("error")]
    public string error { get; set; } = string.Empty;
}
