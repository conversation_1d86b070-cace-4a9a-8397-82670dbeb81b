using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.SendInvoiceByEmail.Raws;

/// <summary>
/// Request DTO cho API 4.8 Gửi mail phát hành hóa đơn cho người mua
/// URL: {{base_url}}/api/Invoice68/AutoSendInvoiceByEmail
/// Method: POST
/// ContentType: application/json
/// Authorization: Bear Token;ma_dvcs
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class SendInvoiceByEmailRequest
{
    /// <summary>
    /// ID của hóa đơn cần gửi email thông báo cho người mua
    /// Kiểu: GUID
    /// Độ dài: 36 ký tự
    /// Bắt buộc: X
    /// Mô tả: Là ID của hóa đơn cần gửi email thông báo cho người mua
    /// Ghi chú: Giá trị này là một mã định danh duy nhất (GUID) của hóa đơn
    /// Ví dụ: "id_cua_hoa_don"
    /// </summary>
    [JsonPropertyName("id")]
    public string id { get; set; } = string.Empty;

    /// <summary>
    /// Email của người nhận hóa đơn
    /// Kiểu: String
    /// Bắt buộc: X
    /// Mô tả: Email của người nhận hóa đơn
    /// Ghi chú: Có thể là một hoặc nhiều địa chỉ email, được phân tách bằng dấu phẩy
    /// Ví dụ: "mail1,mail2,mail3,..."
    /// </summary>
    [JsonPropertyName("nguoinhan")]
    public string nguoinhan { get; set; } = string.Empty;
}
