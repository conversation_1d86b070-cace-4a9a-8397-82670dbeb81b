using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.GetListCertificatesFile68.Raws;

/// <summary>
/// Thông tin chi tiết về chứng thư số (CKS) - Raw DTO theo chuẩn MobiFone
/// </summary>
public class CertificateInfo
{
    /// <summary>
    /// ID chứng thư số
    /// </summary>
    [JsonPropertyName("pl_certificate_id")]
    public string pl_certificate_id { get; set; } = string.Empty;

    /// <summary>
    /// Thông tin nhà phát hành
    /// </summary>
    [JsonPropertyName("issuer")]
    public string issuer { get; set; } = string.Empty;

    /// <summary>
    /// Thông tin người ký
    /// </summary>
    [JsonPropertyName("signer")]
    public object? signer { get; set; }

    /// <summary>
    /// Tên chủ thể
    /// </summary>
    [JsonPropertyName("subject_name")]
    public string subject_name { get; set; } = string.Empty;

    /// <summary>
    /// Ng<PERSON>y bắt đầu hiệu lực
    /// </summary>
    [JsonPropertyName("begin_date")]
    public string begin_date { get; set; } = string.Empty;

    /// <summary>
    /// Ngày hết hiệu lực
    /// </summary>
    [JsonPropertyName("end_date")]
    public string end_date { get; set; } = string.Empty;

    /// <summary>
    /// Loại chứng thư
    /// </summary>
    [JsonPropertyName("cert_type")]
    public string cert_type { get; set; } = string.Empty;

    /// <summary>
    /// Mật khẩu
    /// </summary>
    [JsonPropertyName("pass")]
    public object? pass { get; set; }

    /// <summary>
    /// ID
    /// </summary>
    [JsonPropertyName("id")]
    public string id { get; set; } = string.Empty;

    /// <summary>
    /// Serial chứng thư
    /// </summary>
    [JsonPropertyName("cer_serial")]
    public string cer_serial { get; set; } = string.Empty;

    /// <summary>
    /// Serial số
    /// </summary>
    [JsonPropertyName("so_serial")]
    public string so_serial { get; set; } = string.Empty;
}
