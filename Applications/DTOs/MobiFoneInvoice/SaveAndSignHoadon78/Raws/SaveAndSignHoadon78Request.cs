using System.Text.Json.Serialization;
using Applications.DTOs.MobiFoneInvoice.Common;

namespace Applications.DTOs.MobiFoneInvoice.SaveAndSignHoadon78.Raws;

/// <summary>
/// Request DTO cho API 4.4 Hàm tạo mới và ký gửi hóa đơn bằng HSM nhà cung cấp khác, file mềm, Sim PKI
/// URL: {{base_url}}/api/Invoice68/SaveAndSignHoadon78
/// Method: POST
/// ContentType: application/json
/// Authorization: Bear Token;ma_dvcs
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class SaveAndSignHoadon78Request
{
    /// <summary>
    /// Chế độ chỉnh sửa
    /// Kiểu: Int, Độ dài: 1, Bắt buộc: X
    /// Giá trị: 1: <PERSON><PERSON><PERSON> mới, 2: <PERSON><PERSON><PERSON>, 3: <PERSON><PERSON><PERSON> hóa đơn
    /// </summary>
    [JsonPropertyName("editmode")]
    public int editmode { get; set; }

    /// <summary>
    /// Chữ ký số HSM MobiFone
    /// Kiểu: string, Bắt buộc: X
    /// Ghi chú: không cần truyền
    /// </summary>
    [JsonPropertyName("cer_serial")]
    public string? cer_serial { get; set; }

    /// <summary>
    /// Loại hóa đơn không mã hoặc có mã
    /// Kiểu: string, Bắt buộc: X
    /// Giá trị: Có mã: 200, Không mã: 203, Có mã từ MTT: 206
    /// </summary>
    [JsonPropertyName("type_cmd")]
    public string type_cmd { get; set; } = string.Empty;

    /// <summary>
    /// Có gửi CQT?
    /// Kiểu: string, Bắt buộc: X
    /// Giá trị: Notsend: Không gửi
    /// </summary>
    [JsonPropertyName("guiCQT")]
    public string guiCQT { get; set; } = string.Empty;

    /// <summary>
    /// Đơn vị gọi API
    /// Kiểu: string, Bắt buộc: X
    /// Giá trị: 1
    /// </summary>
    [JsonPropertyName("is_api")]
    public string is_api { get; set; } = string.Empty;

    /// <summary>
    /// Mảng dữ liệu chứa thông tin hóa đơn GTGT
    /// Sử dụng GTGTInvoiceFields từ Common
    /// </summary>
    [JsonPropertyName("data")]
    public List<GTGTInvoiceFields> data { get; set; } = new();
}
