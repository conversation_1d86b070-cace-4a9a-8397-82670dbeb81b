using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.SaveAndSignHoadon78.Raws;

/// <summary>
/// Response DTO cho API 4.4 Hàm tạo mới và ký gửi hóa đơn bằng HSM nhà cung cấp kh<PERSON>c, file mềm, Sim PKI
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class SaveAndSignHoadon78Response
{
    /// <summary>
    /// Mã phản hồi từ API
    /// </summary>
    [JsonPropertyName("code")]
    public string code { get; set; } = string.Empty;

    /// <summary>
    /// Thông báo từ API
    /// </summary>
    [JsonPropertyName("message")]
    public string message { get; set; } = string.Empty;

    /// <summary>
    /// Dữ liệu chi tiết response
    /// </summary>
    [JsonPropertyName("data")]
    public List<SaveAndSignHoadon78Data>? data { get; set; }

    /// <summary>
    /// Trạng thái thành công
    /// </summary>
    [JsonPropertyName("isSuccess")]
    public bool isSuccess { get; set; }
}

/// <summary>
/// Dữ liệu chi tiết response cho API tạo mới và ký gửi hóa đơn
/// </summary>
public class SaveAndSignHoadon78Data
{
    /// <summary>
    /// ID bản ghi
    /// </summary>
    [JsonPropertyName("id")]
    public string? id { get; set; }

    /// <summary>
    /// ID hóa đơn
    /// </summary>
    [JsonPropertyName("hdon_id")]
    public string? hdon_id { get; set; }

    /// <summary>
    /// ID dải ký hiệu hóa đơn
    /// </summary>
    [JsonPropertyName("cctbao_id")]
    public string? cctbao_id { get; set; }

    /// <summary>
    /// Trạng thái gửi CQT
    /// </summary>
    [JsonPropertyName("tthai")]
    public string? tthai { get; set; }

    /// <summary>
    /// Trạng thái hóa đơn
    /// </summary>
    [JsonPropertyName("tthdon")]
    public object? tthdon { get; set; }

    /// <summary>
    /// Ký hiệu hóa đơn
    /// </summary>
    [JsonPropertyName("khieu")]
    public string? khieu { get; set; }

    /// <summary>
    /// Số hóa đơn
    /// </summary>
    [JsonPropertyName("shdon")]
    public string? shdon { get; set; }

    /// <summary>
    /// Thời gian lập
    /// </summary>
    [JsonPropertyName("tdlap")]
    public string? tdlap { get; set; }

    /// <summary>
    /// Đơn vị tiền tệ
    /// </summary>
    [JsonPropertyName("dvtte")]
    public string? dvtte { get; set; }

    /// <summary>
    /// Tỷ giá
    /// </summary>
    [JsonPropertyName("tgia")]
    public decimal? tgia { get; set; }

    /// <summary>
    /// Tên người mua
    /// </summary>
    [JsonPropertyName("tnmua")]
    public string? tnmua { get; set; }

    /// <summary>
    /// Mã khách hàng
    /// </summary>
    [JsonPropertyName("mnmua")]
    public string? mnmua { get; set; }

    /// <summary>
    /// Tên đơn vị
    /// </summary>
    [JsonPropertyName("ten")]
    public string? ten { get; set; }

    /// <summary>
    /// Mã số thuế
    /// </summary>
    [JsonPropertyName("mst")]
    public string? mst { get; set; }

    /// <summary>
    /// Địa chỉ
    /// </summary>
    [JsonPropertyName("dchi")]
    public string? dchi { get; set; }

    /// <summary>
    /// Email
    /// </summary>
    [JsonPropertyName("email")]
    public string? email { get; set; }

    /// <summary>
    /// Số điện thoại người mua
    /// </summary>
    [JsonPropertyName("sdtnmua")]
    public string? sdtnmua { get; set; }

    /// <summary>
    /// Phương thức thanh toán
    /// </summary>
    [JsonPropertyName("htttoan")]
    public string? htttoan { get; set; }

    /// <summary>
    /// Mã tra cứu hóa đơn
    /// </summary>
    [JsonPropertyName("sbmat")]
    public string? sbmat { get; set; }

    /// <summary>
    /// Mã đơn vị
    /// </summary>
    [JsonPropertyName("mdvi")]
    public string? mdvi { get; set; }

    /// <summary>
    /// Ngày lập
    /// </summary>
    [JsonPropertyName("nlap")]
    public string? nlap { get; set; }

    /// <summary>
    /// Tổng tiền trước thuế
    /// </summary>
    [JsonPropertyName("tgtcthue")]
    public decimal? tgtcthue { get; set; }

    /// <summary>
    /// Tổng tiền thuế
    /// </summary>
    [JsonPropertyName("tgtthue")]
    public decimal? tgtthue { get; set; }

    /// <summary>
    /// Tổng tiền sau thuế
    /// </summary>
    [JsonPropertyName("tgtttbso")]
    public decimal? tgtttbso { get; set; }

    /// <summary>
    /// Tổng tiền cuối cùng
    /// </summary>
    [JsonPropertyName("tgtttbso_last")]
    public decimal? tgtttbso_last { get; set; }

    /// <summary>
    /// Tổng tiền phí
    /// </summary>
    [JsonPropertyName("tgtphi")]
    public decimal? tgtphi { get; set; }

    /// <summary>
    /// Phân biệt hóa đơn có mã/không có mã
    /// </summary>
    [JsonPropertyName("is_hdcma")]
    public int? is_hdcma { get; set; }

    /// <summary>
    /// Mã của cơ quan thuế
    /// </summary>
    [JsonPropertyName("mccqthue")]
    public string? mccqthue { get; set; }

    /// <summary>
    /// Trạng thái ký gửi CQT
    /// </summary>
    [JsonPropertyName("kygui_cqt")]
    public decimal? kygui_cqt { get; set; }

    /// <summary>
    /// Trạng thái signing
    /// </summary>
    [JsonPropertyName("signing")]
    public int? signing { get; set; }

    /// <summary>
    /// Số hộ chiếu
    /// </summary>
    [JsonPropertyName("shchieu")]
    public string? shchieu { get; set; }

    /// <summary>
    /// Mã cửa hàng
    /// </summary>
    [JsonPropertyName("mchang")]
    public string? mchang { get; set; }

    /// <summary>
    /// Tên cửa hàng
    /// </summary>
    [JsonPropertyName("tchang")]
    public string? tchang { get; set; }

    /// <summary>
    /// Mã đơn vị quan hệ ngân sách
    /// </summary>
    [JsonPropertyName("mdvqhnsach_mua")]
    public string? mdvqhnsach_mua { get; set; }

    /// <summary>
    /// Chứng minh thư/Căn cước công dân
    /// </summary>
    [JsonPropertyName("cmndmua")]
    public string? cmndmua { get; set; }

    /// <summary>
    /// Tổng tiền chiết khấu thương mại
    /// </summary>
    [JsonPropertyName("tkcktmn")]
    public decimal? tkcktmn { get; set; }

    /// <summary>
    /// Loại hóa đơn
    /// </summary>
    [JsonPropertyName("loaihd")]
    public int? loaihd { get; set; }

    /// <summary>
    /// Trạng thái gửi mail
    /// </summary>
    [JsonPropertyName("issendmail")]
    public bool? issendmail { get; set; }

    /// <summary>
    /// Tổng giảm trừ khác
    /// </summary>
    [JsonPropertyName("tgtkhac")]
    public decimal? tgtkhac { get; set; }

    /// <summary>
    /// Tổng giảm trừ không chịu thuế
    /// </summary>
    [JsonPropertyName("tgtkcthue")]
    public decimal? tgtkcthue { get; set; }

    /// <summary>
    /// Các trường khác có thể có trong response
    /// </summary>
    [JsonPropertyName("ok")]
    public string? ok { get; set; }
}
