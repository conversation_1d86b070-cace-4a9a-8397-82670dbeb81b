using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.GetInvoiceById.Raws;

/// <summary>
/// Request DTO cho API 4.13 Lấy thông tin hóa đơn theo ID
/// URL: {{base_url}}/api/Invoice68/GetById?id={}
/// Method: GET
/// ContentType: application/json
/// Authorization: Bear Token;ma_dvcs
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class GetInvoiceByIdRequest
{
    /// <summary>
    /// Trường khóa chính (ID) của hóa đơn cần lấy thông tin
    /// Kiểu: Guid
    /// Độ dài: 36 ký tự
    /// Bắt buộc: X
    /// Mô tả: Trườ<PERSON> khóa chính (ID) của hóa đơn cần lấy thông tin
    /// Ghi chú: Bạn sẽ thay thế {} trong URL bằng ID cụ thể của hóa đơn
    /// Ví dụ: "a22b84f1-e299-47ca-8c62-9ee9e0e80168"
    /// </summary>
    [JsonPropertyName("id")]
    public string id { get; set; } = string.Empty;
}
