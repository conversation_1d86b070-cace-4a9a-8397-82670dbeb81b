using System.Text.Json.Serialization;
using Applications.DTOs.MobiFoneInvoice.Common;

namespace Applications.DTOs.MobiFoneInvoice.GetInvoiceById.Raws;

/// <summary>
/// Response DTO cho API 4.13 Lấy thông tin hóa đơn theo ID
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class GetInvoiceByIdResponse
{
    /// <summary>
    /// Dữ liệu chi tiết hóa đơn
    /// Kiểu: Object
    /// </summary>
    [JsonPropertyName("data")]
    public InvoiceDetailData? data { get; set; }

    /// <summary>
    /// Trạng thái thành công
    /// Kiểu: Boolean
    /// Giá trị: true khi thành công
    /// </summary>
    [JsonPropertyName("ok")]
    public bool ok { get; set; }
}

/// <summary>
/// Dữ liệu chi tiết hóa đơn kế thừa từ InvoiceListItem và bổ sung thêm các trường đặc biệt
/// </summary>
public class InvoiceDetailData : InvoiceListItem
{
    /// <summary>
    /// Danh sách chi tiết hóa đơn (tương tự details)
    /// Kiểu: Array
    /// </summary>
    [JsonPropertyName("HoaDon68_ChiTiet")]
    public List<InvoiceDetailItem>? HoaDon68_ChiTiet { get; set; }
}

/// <summary>
/// Chi tiết từng dòng hóa đơn
/// </summary>
public class InvoiceDetailItem
{
    /// <summary>
    /// ID chi tiết hóa đơn
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("cthdon_id")]
    public string? cthdon_id { get; set; }

    /// <summary>
    /// ID hóa đơn
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("hdon_id")]
    public string? hdon_id { get; set; }

    /// <summary>
    /// Tính chất
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("tchat")]
    public string? tchat { get; set; }

    /// <summary>
    /// Số thứ tự dòng của hóa đơn
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("stt")]
    public string? stt { get; set; }

    /// <summary>
    /// Mã hàng
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("ma")]
    public string? ma { get; set; }

    /// <summary>
    /// Tên hàng
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("ten")]
    public string? ten { get; set; }

    /// <summary>
    /// Mã đơn vị tính
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("mdvtinh")]
    public string? mdvtinh { get; set; }

    /// <summary>
    /// Tên đơn vị tính
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("dvtinh")]
    public string? dvtinh { get; set; }

    /// <summary>
    /// Số lượng
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("sluong")]
    public decimal? sluong { get; set; }

    /// <summary>
    /// Đơn giá
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("dgia")]
    public decimal? dgia { get; set; }

    /// <summary>
    /// Tiền trước thuế
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("thtien")]
    public decimal? thtien { get; set; }

    /// <summary>
    /// Phần trăm chiết khấu
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tlckhau")]
    public decimal? tlckhau { get; set; }

    /// <summary>
    /// Tiền chiết khấu
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("stckhau")]
    public decimal? stckhau { get; set; }

    /// <summary>
    /// Mã loại thuế suất
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("tsuat")]
    public string? tsuat { get; set; }

    /// <summary>
    /// Phương thức thuế
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("ptthue")]
    public string? ptthue { get; set; }

    /// <summary>
    /// Tiền thuế
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tthue")]
    public decimal? tthue { get; set; }

    /// <summary>
    /// Tổng tiền
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtien")]
    public decimal? tgtien { get; set; }

    /// <summary>
    /// Tính chất của dòng hàng hóa dịch vụ
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("kmai")]
    public int? kmai { get; set; }

    /// <summary>
    /// Chuỗi thuế suất
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("tsuatstr")]
    public string? tsuatstr { get; set; }
}

/// <summary>
/// Response wrapper cho trường hợp có lỗi
/// </summary>
public class GetInvoiceByIdErrorResponse
{
    /// <summary>
    /// Thông báo lỗi
    /// Kiểu: String
    /// Các lỗi có thể xảy ra:
    /// - "Không tìm thấy hóa đơn": Xảy ra khi không tìm thấy hóa đơn bằng ID đã truyền vào
    /// - "Id không được bỏ trống": Xảy ra nếu trường id trong tham số truy vấn bị thiếu hoặc rỗng
    /// - "Id không đúng định dạng": Xảy ra nếu id truyền vào không đúng định dạng GUID
    /// </summary>
    [JsonPropertyName("error")]
    public string? error { get; set; }

    /// <summary>
    /// Thông báo lỗi (dạng Message)
    /// Kiểu: String
    /// Ví dụ: "Authorization has been denied for this request."
    /// </summary>
    [JsonPropertyName("Message")]
    public string? Message { get; set; }
}
