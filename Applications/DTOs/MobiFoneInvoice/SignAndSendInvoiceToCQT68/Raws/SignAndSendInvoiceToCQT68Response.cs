using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.SignAndSendInvoiceToCQT68.Raws;

/// <summary>
/// Response DTO cho API 4.7 Ký và gửi hóa đơn tới CQT (Chỉ dành cho file mềm, SIM)
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class SignAndSendInvoiceToCQT68Response
{
    /// <summary>
    /// Trạng thái ký hóa đơn
    /// Giá trị: "Đã ký"
    /// </summary>
    [JsonPropertyName("tthai")]
    public string? tthai { get; set; }

    /// <summary>
    /// Trạng thái gửi hóa đơn
    /// Giá trị: "Đã gửi hóa đơn tới Cơ quan thuế"
    /// </summary>
    [JsonPropertyName("trang_thai")]
    public string? trang_thai { get; set; }

    /// <summary>
    /// Trạng thái thành công
    /// </summary>
    [JsonPropertyName("ok")]
    public bool ok { get; set; }

    /// <summary>
    /// Thông báo lỗi (nếu có)
    /// Các lỗi có thể xảy ra:
    /// - "Json không đúng định dạng !"
    /// - "Không tìm thấy chứng thư số"
    /// - "Tồn tại hóa đơn không ở trạng thái chờ ký"
    /// </summary>
    [JsonPropertyName("error")]
    public string? error { get; set; }
}
