using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.PrintMultipleInvoices.Raws;

/// <summary>
/// Request DTO cho API 4.10 In nhiều hóa đơn
/// URL: {{base_url}}/api/Invoice68/InDanhSachHoaDon
/// Method: POST
/// ContentType: application/json
/// Authorization: Bear Token;ma_dvcs
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class PrintMultipleInvoicesRequest
{
    /// <summary>
    /// Danh sách ID của các hóa đơn cần in
    /// Kiểu: Array string
    /// Bắt buộc: X
    /// Mô tả: Danh sách ID của các hóa đơn cần in
    /// Ghi chú: Chứa các ID hóa đơn (thường là GUID - 36 ký tự)
    /// Ví dụ: ["e83cc244-1978-417d-aa06-5f7cc3971108", "9137b53c-1c97-4dab-a8e2-9844fe82293f"]
    /// </summary>
    [JsonPropertyName("invs")]
    public List<string> invs { get; set; } = new();

    /// <summary>
    /// Định dạng file của hóa đơn muốn in
    /// Kiểu: String
    /// Bắt buộc: X
    /// Mô tả: Định dạng file của hóa đơn muốn in
    /// Ghi chú: Giá trị cố định là "PDF"
    /// </summary>
    [JsonPropertyName("type")]
    public string type { get; set; } = "PDF";

    /// <summary>
    /// Tùy chọn in thể hiện hay in chuyển đổi
    /// Kiểu: Boolean
    /// Bắt buộc: X
    /// Mô tả: Tùy chọn in thể hiện hay in chuyển đổi
    /// Ghi chú: True: In chuyển đổi, False: In thể hiện
    /// Ví dụ: "false"
    /// </summary>
    [JsonPropertyName("inchuyendoi")]
    public string inchuyendoi { get; set; } = "false";
}
