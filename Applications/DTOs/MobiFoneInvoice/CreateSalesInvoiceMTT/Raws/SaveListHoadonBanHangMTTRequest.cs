using System.Text.Json.Serialization;
using Applications.DTOs.MobiFoneInvoice.Common;

namespace Applications.DTOs.MobiFoneInvoice.CreateSalesInvoiceMTT.Raws;

/// <summary>
/// Request DTO cho API tạo mới Hóa đơn Bán hàng máy tính tiền sinh mã (SaveListHoadon78MTT)
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class SaveListHoadonBanHangMTTRequest
{
    /// <summary>
    /// Chế độ chỉnh sửa
    /// 1: <PERSON><PERSON><PERSON> mới, 2: <PERSON><PERSON><PERSON>, 3: <PERSON><PERSON><PERSON> hóa đơn
    /// </summary>
    [JsonPropertyName("editmode")]
    public int editmode { get; set; }

    /// <summary>
    /// Mảng dữ liệu chứa thông tin hóa đơn bán hàng
    /// </summary>
    [JsonPropertyName("data")]
    public List<SalesInvoiceFields> data { get; set; } = [];
}
