using System.Text.Json.Serialization;
using Applications.DTOs.MobiFoneInvoice.Common;

namespace Applications.DTOs.MobiFoneInvoice.CreatePXKVCNBInvoice.Raws;

/// <summary>
/// Request DTO cho API tạo mới Phiếu xuất kho kiêm vận chuyển nội bộ (PXKVCNB)
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class SaveListHoadonPXKVCNBRequest
{
    /// <summary>
    /// Chế độ chỉnh sửa
    /// 1: T<PERSON><PERSON> mới, 2: <PERSON><PERSON><PERSON>, 3: <PERSON><PERSON>a hóa đơn
    /// </summary>
    [JsonPropertyName("editmode")]
    public int editmode { get; set; }

    /// <summary>
    /// Mảng dữ liệu chứa thông tin phiếu xuất kho kiêm vận chuyển nội bộ
    /// </summary>
    [JsonPropertyName("data")]
    public List<PXKVCNBInvoiceFields> data { get; set; } = [];
}
