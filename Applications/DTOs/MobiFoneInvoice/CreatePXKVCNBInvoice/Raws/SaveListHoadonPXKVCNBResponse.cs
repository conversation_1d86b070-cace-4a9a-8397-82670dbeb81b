using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.CreatePXKVCNBInvoice.Raws;

/// <summary>
/// Response DTO cho API tạo mới Phiếu xuất kho kiêm vận chuyển nội bộ (PXKVCNB)
/// Đ<PERSON>y là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class SaveListHoadonPXKVCNBResponse
{
    /// <summary>
    /// Dữ liệu trả về từ API
    /// </summary>
    [JsonPropertyName("data")]
    public List<PXKVCNBInvoiceResponseData> data { get; set; } = [];

    /// <summary>
    /// Trạng thái thành công
    /// </summary>
    [JsonPropertyName("ok")]
    public string ok { get; set; } = string.Empty;
}

/// <summary>
/// Dữ liệu chi tiết response cho API tạo phiếu xuất kho kiêm vận chuyển nội bộ
/// </summary>
public class PXKVCNBInvoiceResponseData
{
    /// <summary>
    /// ID của phiếu
    /// </summary>
    [JsonPropertyName("id")]
    public string id { get; set; } = string.Empty;

    /// <summary>
    /// ID hóa đơn
    /// </summary>
    [JsonPropertyName("hdon_id")]
    public string hdon_id { get; set; } = string.Empty;

    /// <summary>
    /// ID cảnh báo
    /// </summary>
    [JsonPropertyName("cctbao_id")]
    public string cctbao_id { get; set; } = string.Empty;

    /// <summary>
    /// Trạng thái
    /// </summary>
    [JsonPropertyName("tthai")]
    public string tthai { get; set; } = string.Empty;

    /// <summary>
    /// Trạng thái hóa đơn
    /// </summary>
    [JsonPropertyName("tthdon")]
    public object tthdon { get; set; }

    /// <summary>
    /// Ký hiệu
    /// </summary>
    [JsonPropertyName("khieu")]
    public string khieu { get; set; } = string.Empty;

    /// <summary>
    /// Số hóa đơn
    /// </summary>
    [JsonPropertyName("shdon")]
    public string shdon { get; set; } = string.Empty;

    /// <summary>
    /// Ngày lập
    /// </summary>
    [JsonPropertyName("nlap")]
    public string nlap { get; set; } = string.Empty;

    /// <summary>
    /// Đơn vị tiền tệ
    /// </summary>
    [JsonPropertyName("dvtte")]
    public string dvtte { get; set; } = string.Empty;

    /// <summary>
    /// Tỷ giá
    /// </summary>
    [JsonPropertyName("tgia")]
    public double tgia { get; set; }

    /// <summary>
    /// Tên người mua
    /// </summary>
    [JsonPropertyName("tnmua")]
    public string tnmua { get; set; } = string.Empty;

    /// <summary>
    /// Mã người mua
    /// </summary>
    [JsonPropertyName("mnmua")]
    public string mnmua { get; set; } = string.Empty;

    /// <summary>
    /// Tên
    /// </summary>
    [JsonPropertyName("ten")]
    public string ten { get; set; } = string.Empty;

    /// <summary>
    /// Mã số thuế
    /// </summary>
    [JsonPropertyName("mst")]
    public string mst { get; set; } = string.Empty;

    /// <summary>
    /// Địa chỉ
    /// </summary>
    [JsonPropertyName("dchi")]
    public string dchi { get; set; } = string.Empty;

    /// <summary>
    /// Email
    /// </summary>
    [JsonPropertyName("email")]
    public string email { get; set; } = string.Empty;

    /// <summary>
    /// Số điện thoại người mua
    /// </summary>
    [JsonPropertyName("sdtnmua")]
    public string sdtnmua { get; set; } = string.Empty;

    /// <summary>
    /// Hình thức thanh toán
    /// </summary>
    [JsonPropertyName("htttoan")]
    public string htttoan { get; set; } = string.Empty;

    /// <summary>
    /// Số tài khoản bán
    /// </summary>
    [JsonPropertyName("stknban")]
    public string stknban { get; set; } = string.Empty;

    /// <summary>
    /// Mã đơn vị
    /// </summary>
    [JsonPropertyName("mdvi")]
    public string mdvi { get; set; } = string.Empty;

    /// <summary>
    /// Tổng tiền trước thuế
    /// </summary>
    [JsonPropertyName("tgtcthue")]
    public double tgtcthue { get; set; }

    /// <summary>
    /// Tổng tiền thuế
    /// </summary>
    [JsonPropertyName("tgtthue")]
    public double tgtthue { get; set; }

    /// <summary>
    /// Tổng tiền sau thuế
    /// </summary>
    [JsonPropertyName("tgtttbso")]
    public double tgtttbso { get; set; }

    /// <summary>
    /// Tổng tiền cuối cùng
    /// </summary>
    [JsonPropertyName("tgtttbso_last")]
    public double tgtttbso_last { get; set; }

    /// <summary>
    /// Lệnh điều động nội bộ
    /// </summary>
    [JsonPropertyName("lddnbo")]
    public object lddnbo { get; set; }

    /// <summary>
    /// Tên người vận chuyển
    /// </summary>
    [JsonPropertyName("tnvchuyen")]
    public object tnvchuyen { get; set; }

    /// <summary>
    /// Phương tiện vận chuyển
    /// </summary>
    [JsonPropertyName("ptvchuyen")]
    public object ptvchuyen { get; set; }

    /// <summary>
    /// Địa chỉ kho xuất
    /// </summary>
    [JsonPropertyName("dckhoxuat")]
    public object dckhoxuat { get; set; }

    /// <summary>
    /// Tên người xuất hàng
    /// </summary>
    [JsonPropertyName("hvtnxhang")]
    public object hvtnxhang { get; set; }

    /// <summary>
    /// Hợp đồng vận chuyển
    /// </summary>
    [JsonPropertyName("hdvc")]
    public object hdvc { get; set; }

    /// <summary>
    /// Có hóa đơn có mã hay không
    /// </summary>
    [JsonPropertyName("is_hdcma")]
    public int is_hdcma { get; set; }
}
