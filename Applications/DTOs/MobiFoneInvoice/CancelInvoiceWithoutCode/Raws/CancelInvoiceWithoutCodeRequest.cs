using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.CancelInvoiceWithoutCode.Raws;

/// <summary>
/// Request DTO cho API 4.12 Hủy hóa đơn không mã
/// URL: {{base_url}}/api/Invoice68/uploadCanceledInv?id={}
/// Method: GET
/// ContentType: application/json
/// Authorization: Bear Token;ma_dvcs
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class CancelInvoiceWithoutCodeRequest
{
    /// <summary>
    /// Trường khóa chính (ID) của hóa đơn cần hủy
    /// Kiểu: Guid
    /// Độ dài: 36 ký tự
    /// Bắt buộc: X
    /// Mô tả: Trườ<PERSON> kh<PERSON>a chín<PERSON> (ID) của hóa đơn cần hủy
    /// Ghi chú: Bạn sẽ thay thế {} trong URL bằng ID cụ thể của hóa đơn
    /// Ví dụ: "a22b84f1-e299-47ca-8c62-9ee9e0e80168"
    /// </summary>
    [JsonPropertyName("id")]
    public string id { get; set; } = string.Empty;
}
