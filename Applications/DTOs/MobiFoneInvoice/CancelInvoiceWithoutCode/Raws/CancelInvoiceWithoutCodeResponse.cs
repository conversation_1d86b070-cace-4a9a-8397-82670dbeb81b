using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.CancelInvoiceWithoutCode.Raws;

/// <summary>
/// Response DTO cho API 4.12 Hủy hóa đơn không mã
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class CancelInvoiceWithoutCodeResponse
{
    /// <summary>
    /// Trạng thái thành công
    /// Kiểu: Boolean
    /// Giá trị: true khi thành công
    /// </summary>
    [JsonPropertyName("ok")]
    public bool ok { get; set; }
}

/// <summary>
/// Response wrapper cho trường hợp có lỗi
/// </summary>
public class CancelInvoiceWithoutCodeErrorResponse
{
    /// <summary>
    /// Thông báo lỗi
    /// Kiểu: String
    /// Các lỗi có thể xảy ra:
    /// - "Id không được bỏ trống": <PERSON><PERSON><PERSON> ra nếu trường id trong tham số truy vấn bị thiếu hoặc rỗng
    /// - "Id không đúng định dạng": Xảy ra nếu id truyền vào không đúng định dạng GUID
    /// - "Không tìm thấy hóa đơn": Xảy ra nếu không tìm thấy hóa đơn với id đã cung cấp
    /// </summary>
    [JsonPropertyName("error")]
    public string? error { get; set; }

    /// <summary>
    /// Thông báo lỗi (dạng Message)
    /// Kiểu: String
    /// Ví dụ: "Authorization has been denied for this request."
    /// </summary>
    [JsonPropertyName("Message")]
    public string? Message { get; set; }
}
