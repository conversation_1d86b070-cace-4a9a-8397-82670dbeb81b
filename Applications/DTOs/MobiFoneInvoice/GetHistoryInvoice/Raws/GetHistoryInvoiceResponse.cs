using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.GetHistoryInvoice.Raws;

/// <summary>
/// Response DTO cho API 4.20 L<PERSON>y danh sách lịch sử hóa đơn theo ID
/// Đ<PERSON>y là DTO raw theo chuẩn tài liệu <PERSON>, giữ nguyên tên field
/// </summary>
public class GetHistoryInvoiceResponse
{
    /// <summary>
    /// Thời gian gửi thông điệp
    /// Kiểu: String
    /// Ví dụ: "2022-02-10T07:59:18"
    /// </summary>
    [JsonPropertyName("tgian_gui")]
    public string? tgian_gui { get; set; }

    /// <summary>
    /// Mã loại thông điệp đã gửi
    /// Kiểu: String
    /// Ví dụ: "200", "202"
    /// </summary>
    [JsonPropertyName("mltdiep_gui")]
    public string? mltdiep_gui { get; set; }

    /// <summary>
    /// Đơn vị hoặc người gửi thông điệp
    /// Kiểu: String
    /// Ví dụ: "BONGSON", "MobiFone TVAN", "Cơ quan Thuế"
    /// </summary>
    [JsonPropertyName("nguoi_gui")]
    public string? nguoi_gui { get; set; }

    /// <summary>
    /// Ghi chú về thông điệp
    /// Kiểu: String
    /// Ví dụ: "app", "phkt", "capma", "ktdl"
    /// </summary>
    [JsonPropertyName("note")]
    public string? note { get; set; }

    /// <summary>
    /// Nội dung chi tiết của thông điệp (nếu có)
    /// Kiểu: String
    /// Có thể là null hoặc "0"
    /// </summary>
    [JsonPropertyName("noidung")]
    public string? noidung { get; set; }

    /// <summary>
    /// ID của bản ghi lịch sử cụ thể này
    /// Kiểu: String
    /// Ví dụ: "08a5eecd-352b-4cd0-89c5-8ceb128fdc46"
    /// </summary>
    [JsonPropertyName("id")]
    public string? id { get; set; }

    /// <summary>
    /// ID của hóa đơn mà lịch sử này thuộc về
    /// Kiểu: String
    /// Ví dụ: "87a0c099-fdce-4833-93cf-23fd32b13f58"
    /// </summary>
    [JsonPropertyName("typeid")]
    public string? typeid { get; set; }
}
