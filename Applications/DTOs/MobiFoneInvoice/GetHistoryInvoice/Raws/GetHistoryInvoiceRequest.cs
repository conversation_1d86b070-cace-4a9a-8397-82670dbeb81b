using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.GetHistoryInvoice.Raws;

/// <summary>
/// Request DTO cho API 4.20 L<PERSON>y danh sách lịch sử hóa đơn theo ID
/// URL: {{base_url}}/api/Invoice68/GetHistoryInvoice?id={hdon_id}
/// Method: GET
/// ContentType: application/json
/// Authorization: Bear Token;ma_dvcs
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class GetHistoryInvoiceRequest
{
    /// <summary>
    /// Id của hóa đơn
    /// Kiểu: String, Độ dài: 36, Bắt buộc: X
    /// Mô tả: ID chính xác của hóa đơn mà bạn muốn tra cứu lịch sử
    /// Ví dụ: "87a0c099-fdce-4833-93cf-23fd32b13f58"
    /// </summary>
    [JsonPropertyName("id")]
    public string id { get; set; } = string.Empty;
}
