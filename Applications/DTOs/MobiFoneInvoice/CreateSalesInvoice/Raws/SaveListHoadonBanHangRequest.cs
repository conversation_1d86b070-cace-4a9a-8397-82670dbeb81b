using System.Text.Json.Serialization;
using Applications.DTOs.MobiFoneInvoice.Common;

namespace Applications.DTOs.MobiFoneInvoice.CreateSalesInvoice.Raws;

/// <summary>
/// Request DTO cho API tạo mới Hóa đơn <PERSON> (SaveListHoadon78)
/// Đ<PERSON>y là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class SaveListHoadonBanHangRequest
{
    /// <summary>
    /// Chế độ chỉnh sửa
    /// 1: Tạo mới, 2: <PERSON><PERSON><PERSON>, 3: <PERSON><PERSON><PERSON> hó<PERSON> đơn
    /// </summary>
    [JsonPropertyName("editmode")]
    public int editmode { get; set; }

    /// <summary>
    /// Mảng dữ liệu chứa thông tin hóa đơn bán hàng
    /// </summary>
    [JsonPropertyName("data")]
    public List<SalesInvoiceFields> data { get; set; } = [];
}


