using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.CreateSalesInvoice.Raws;

/// <summary>
/// Response DTO cho API tạo mới Hóa đơn Bán hàng (SaveListHoadon78)
/// Đ<PERSON>y là DTO raw theo chuẩn tài liệu <PERSON>, giữ nguyên tên field
/// Cấu trúc tương tự SaveListHoadon78Response
/// </summary>
public class SaveListHoadonBanHangResponse
{
    /// <summary>
    /// Dữ liệu trả về
    /// </summary>
    [JsonPropertyName("data")]
    public SalesInvoiceResponseData? data { get; set; }

    /// <summary>
    /// Trạng thái kết quả
    /// </summary>
    [JsonPropertyName("ok")]
    public string ok { get; set; } = string.Empty;
}

/// <summary>
/// Dữ liệu chi tiết response cho API tạo hóa đơn bán hàng
/// Tương tự cấu trúc Data trong SaveListHoadon78Response nhưng có thêm các trường đặc biệt cho Hóa đơn Bán hàng
/// </summary>
public class SalesInvoiceResponseData
{
    [JsonPropertyName("id")]
    public string? id { get; set; }

    [JsonPropertyName("hdon_id")]
    public string? hdon_id { get; set; }

    [JsonPropertyName("cctbao_id")]
    public string? cctbao_id { get; set; }

    [JsonPropertyName("hdlket_id")]
    public object? hdlket_id { get; set; }

    [JsonPropertyName("tthai")]
    public string? tthai { get; set; }

    [JsonPropertyName("tthdon")]
    public object? tthdon { get; set; }

    [JsonPropertyName("khieu")]
    public string? khieu { get; set; }

    [JsonPropertyName("shdon")]
    public string? shdon { get; set; }

    [JsonPropertyName("tdlap")]
    public string? tdlap { get; set; }

    [JsonPropertyName("dvtte")]
    public string? dvtte { get; set; }

    [JsonPropertyName("tgia")]
    public double? tgia { get; set; }

    [JsonPropertyName("gchu")]
    public object? gchu { get; set; }

    [JsonPropertyName("tnmua")]
    public string? tnmua { get; set; }

    [JsonPropertyName("mnmua")]
    public string? mnmua { get; set; }

    [JsonPropertyName("ten")]
    public string? ten { get; set; }

    [JsonPropertyName("mst")]
    public string? mst { get; set; }

    [JsonPropertyName("dchi")]
    public string? dchi { get; set; }

    [JsonPropertyName("email")]
    public string? email { get; set; }

    [JsonPropertyName("sdtnmua")]
    public string? sdtnmua { get; set; }

    [JsonPropertyName("stknmua")]
    public string? stknmua { get; set; }

    [JsonPropertyName("htttoan")]
    public string? htttoan { get; set; }

    [JsonPropertyName("stknban")]
    public string? stknban { get; set; }

    [JsonPropertyName("sbmat")]
    public string? sbmat { get; set; }

    [JsonPropertyName("mdvi")]
    public string? mdvi { get; set; }

    [JsonPropertyName("nglap")]
    public string? nglap { get; set; }

    [JsonPropertyName("nlap")]
    public string? nlap { get; set; }

    [JsonPropertyName("ngsua")]
    public object? ngsua { get; set; }

    [JsonPropertyName("nsua")]
    public object? nsua { get; set; }

    [JsonPropertyName("tgtcthue")]
    public double? tgtcthue { get; set; }

    [JsonPropertyName("tgtthue")]
    public double? tgtthue { get; set; }

    [JsonPropertyName("ttcktmai")]
    public object? ttcktmai { get; set; }

    [JsonPropertyName("tgtttbso")]
    public double? tgtttbso { get; set; }

    [JsonPropertyName("tgtttbchu")]
    public string? tgtttbchu { get; set; }

    [JsonPropertyName("dlqrcode")]
    public object? dlqrcode { get; set; }

    [JsonPropertyName("sdhang")]
    public string? sdhang { get; set; }

    [JsonPropertyName("shdon1")]
    public object? shdon1 { get; set; }

    [JsonPropertyName("mccqthue")]
    public object? mccqthue { get; set; }

    [JsonPropertyName("ngky")]
    public object? ngky { get; set; }

    [JsonPropertyName("nky")]
    public object? nky { get; set; }

    [JsonPropertyName("signature")]
    public object? signature { get; set; }

    [JsonPropertyName("hthdbtthe")]
    public object? hthdbtthe { get; set; }

    [JsonPropertyName("tdlhdbtthe")]
    public object? tdlhdbtthe { get; set; }

    [JsonPropertyName("khmshdbtthe")]
    public object? khmshdbtthe { get; set; }

    [JsonPropertyName("khhdbtthe")]
    public object? khhdbtthe { get; set; }

    [JsonPropertyName("shdbtthe")]
    public object? shdbtthe { get; set; }

    [JsonPropertyName("tgtphi")]
    public double? tgtphi { get; set; }

    [JsonPropertyName("is_hdcma")]
    public int? is_hdcma { get; set; }

    [JsonPropertyName("tthdon_original")]
    public int? tthdon_original { get; set; }

    [JsonPropertyName("kygui_cqt")]
    public double? kygui_cqt { get; set; }

    [JsonPropertyName("tnhban")]
    public string? tnhban { get; set; }

    [JsonPropertyName("tnhmua")]
    public string? tnhmua { get; set; }

    // ===== TRƯỜNG ĐẶC BIỆT CHO HÓA ĐƠN BÁN HÀNG =====

    /// <summary>
    /// Phí thuế quan (Đặc biệt cho Hóa đơn Bán hàng)
    /// </summary>
    [JsonPropertyName("hddckptquan")]
    public object? hddckptquan { get; set; }

    /// <summary>
    /// Hóa đơn giảm theo nghị quyết 101/2023/QH15 (Đặc biệt cho Hóa đơn Bán hàng)
    /// </summary>
    [JsonPropertyName("giamthuebanhang20")]
    public object? giamthuebanhang20 { get; set; }

    /// <summary>
    /// Số tiền được giảm theo nghị quyết 101/2023/QH15 (Đặc biệt cho Hóa đơn Bán hàng)
    /// </summary>
    [JsonPropertyName("tienthuegtgtgiam")]
    public object? tienthuegtgtgiam { get; set; }

    [JsonPropertyName("cmndmua")]
    public string? cmndmua { get; set; }

    [JsonPropertyName("tkcktmn")]
    public double? tkcktmn { get; set; }

    [JsonPropertyName("tgtttbso_last")]
    public double? tgtttbso_last { get; set; }

    [JsonPropertyName("mdvqhnsach_mua")]
    public string? mdvqhnsach_mua { get; set; }

    [JsonPropertyName("issendmail")]
    public bool? issendmail { get; set; }

    [JsonPropertyName("loaihd")]
    public int? loaihd { get; set; }

    [JsonPropertyName("signing")]
    public int? signing { get; set; }

    [JsonPropertyName("shchieu")]
    public string? shchieu { get; set; }

    [JsonPropertyName("mchang")]
    public string? mchang { get; set; }

    [JsonPropertyName("tchang")]
    public string? tchang { get; set; }

    [JsonPropertyName("fkey")]
    public object? fkey { get; set; }
}
