using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.Common;

/// <summary>
/// DTO chung cho cấu trúc hóa đơn trong response của các API trả về danh sách hóa đơn
/// Đ<PERSON><PERSON><PERSON> sử dụng bởi API 4.14 (GetHoadonFkey) và API 4.17 (GetInvoiceByTimeAndUnit)
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class InvoiceListItem
{
    /// <summary>
    /// ID hóa đơn
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("hdon_id")]
    public string? hdon_id { get; set; }

    /// <summary>
    /// ID của dải ký hiệu hóa đơn
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("cctbao_id")]
    public string? cctbao_id { get; set; }

    /// <summary>
    /// ID liên kết hóa đơn
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("hdlket_id")]
    public string? hdlket_id { get; set; }

    /// <summary>
    /// Trạng thái gửi CQT
    /// Kiểu: String
    /// Ví dụ: "Chờ ký", "Đã ký", "Chờ cấp mã", "CQT đã nhận"
    /// </summary>
    [JsonPropertyName("tthai")]
    public string? tthai { get; set; }

    /// <summary>
    /// Trạng thái Hóa đơn
    /// Kiểu: Number
    /// 0: Gốc, 1: Giải trình, 2: Thay thế, 3: Hủy
    /// </summary>
    [JsonPropertyName("tthdon")]
    public int? tthdon { get; set; }

    /// <summary>
    /// Ký hiệu hóa đơn
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("khieu")]
    public string? khieu { get; set; }

    /// <summary>
    /// Số hóa đơn
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("shdon")]
    public decimal? shdon { get; set; }

    /// <summary>
    /// Thời điểm lập hóa đơn
    /// Kiểu: String (Date-time)
    /// </summary>
    [JsonPropertyName("tdlap")]
    public string? tdlap { get; set; }

    /// <summary>
    /// Mã ngoại tệ
    /// Kiểu: String, độ dài: 3
    /// Mặc định: "VND"
    /// </summary>
    [JsonPropertyName("dvtte")]
    public string? dvtte { get; set; }

    /// <summary>
    /// Tỷ giá
    /// Kiểu: Number
    /// Mặc định: 1 nếu là "VND"
    /// </summary>
    [JsonPropertyName("tgia")]
    public decimal? tgia { get; set; }

    /// <summary>
    /// Ghi chú
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("gchu")]
    public string? gchu { get; set; }

    /// <summary>
    /// Tên người mua
    /// Kiểu: String, độ dài: 255
    /// </summary>
    [JsonPropertyName("tnmua")]
    public string? tnmua { get; set; }

    /// <summary>
    /// Mã khách hàng
    /// Kiểu: String, độ dài: 30
    /// </summary>
    [JsonPropertyName("mnmua")]
    public string? mnmua { get; set; }

    /// <summary>
    /// Tên đơn vị
    /// Kiểu: String, độ dài: 255
    /// </summary>
    [JsonPropertyName("ten")]
    public string? ten { get; set; }

    /// <summary>
    /// Mã số thuế bên mua
    /// Kiểu: String, độ dài: 14
    /// </summary>
    [JsonPropertyName("mst")]
    public string? mst { get; set; }

    /// <summary>
    /// Địa chỉ người mua
    /// Kiểu: String, độ dài: 250
    /// </summary>
    [JsonPropertyName("dchi")]
    public string? dchi { get; set; }

    /// <summary>
    /// Email của người mua
    /// Kiểu: String, độ dài: 50
    /// </summary>
    [JsonPropertyName("email")]
    public string? email { get; set; }

    /// <summary>
    /// Số điện thoại người mua
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("sdtnmua")]
    public string? sdtnmua { get; set; }

    /// <summary>
    /// Tài khoản ngân hàng bên mua
    /// Kiểu: String, độ dài: 20
    /// </summary>
    [JsonPropertyName("stknmua")]
    public string? stknmua { get; set; }

    /// <summary>
    /// Phương thức thanh toán
    /// Kiểu: String, độ dài: 50
    /// Ví dụ: "Tiền mặt", "Chuyển khoản", "Tiền mặt/Chuyển khoản"
    /// </summary>
    [JsonPropertyName("htttoan")]
    public string? htttoan { get; set; }

    /// <summary>
    /// Tài khoản ngân hàng bên bán
    /// Kiểu: String, độ dài: 50
    /// </summary>
    [JsonPropertyName("stknban")]
    public string? stknban { get; set; }

    /// <summary>
    /// Mã tra cứu hóa đơn
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("sbmat")]
    public string? sbmat { get; set; }

    /// <summary>
    /// Mã đơn vị tạo hóa đơn
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("mdvi")]
    public string? mdvi { get; set; }

    /// <summary>
    /// Người lập
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("nglap")]
    public string? nglap { get; set; }

    /// <summary>
    /// Ngày hóa đơn
    /// Kiểu: Date (Date-time)
    /// Định dạng: yyyy-MM-dd
    /// </summary>
    [JsonPropertyName("nlap")]
    public string? nlap { get; set; }

    /// <summary>
    /// Người sửa
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("ngsua")]
    public string? ngsua { get; set; }

    /// <summary>
    /// Ngày sửa
    /// Kiểu: Date
    /// </summary>
    [JsonPropertyName("nsua")]
    public string? nsua { get; set; }

    /// <summary>
    /// Tổng tiền trước thuế của cả hóa đơn
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtcthue")]
    public decimal? tgtcthue { get; set; }

    /// <summary>
    /// Tổng tiền thuế của cả hóa đơn
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtthue")]
    public decimal? tgtthue { get; set; }

    /// <summary>
    /// Tổng tiền chiết khấu mặt hàng
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("ttcktmai")]
    public decimal? ttcktmai { get; set; }

    /// <summary>
    /// Tổng tiền sau thuế của cả hóa đơn
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtttbso")]
    public decimal? tgtttbso { get; set; }

    /// <summary>
    /// Tổng tiền sau thuế bằng chữ
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("tgtttbchu")]
    public string? tgtttbchu { get; set; }

    /// <summary>
    /// Dữ liệu QR code
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("dlqrcode")]
    public string? dlqrcode { get; set; }

    /// <summary>
    /// Số đơn hàng
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("sdhang")]
    public string? sdhang { get; set; }

    /// <summary>
    /// Số hóa đơn 1
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("shdon1")]
    public string? shdon1 { get; set; }

    /// <summary>
    /// Mã của cơ quan thuế
    /// Kiểu: String, độ dài: 34
    /// </summary>
    [JsonPropertyName("mccqthue")]
    public string? mccqthue { get; set; }

    /// <summary>
    /// Người ký
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("ngky")]
    public string? ngky { get; set; }

    /// <summary>
    /// Thời điểm ký
    /// Kiểu: Date (Date-time)
    /// </summary>
    [JsonPropertyName("nky")]
    public string? nky { get; set; }

    /// <summary>
    /// Chữ ký
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("signature")]
    public string? signature { get; set; }

    /// <summary>
    /// Hình thức hóa đơn bị thay thế
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("hthdbtthe")]
    public string? hthdbtthe { get; set; }

    /// <summary>
    /// Thời điểm lập hóa đơn bị thay thế
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("tdlhdbtthe")]
    public string? tdlhdbtthe { get; set; }

    /// <summary>
    /// Ký hiệu mẫu số hóa đơn bị thay thế
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("khmshdbtthe")]
    public string? khmshdbtthe { get; set; }

    /// <summary>
    /// Ký hiệu hóa đơn bị thay thế
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("khhdbtthe")]
    public string? khhdbtthe { get; set; }

    /// <summary>
    /// Số hóa đơn bị thay thế
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("shdbtthe")]
    public string? shdbtthe { get; set; }

    /// <summary>
    /// Tổng tiền phí của hóa đơn
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtphi")]
    public decimal? tgtphi { get; set; }

    /// <summary>
    /// Tổng tiền trước thuế cho thuế suất 0%
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtcthue0")]
    public decimal? tgtcthue0 { get; set; }

    /// <summary>
    /// Tổng tiền thuế cho thuế suất 0%
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtthue0")]
    public decimal? tgtthue0 { get; set; }

    /// <summary>
    /// Tổng tiền chiết khấu mặt hàng cho thuế suất 0%
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("ttcktmai0")]
    public decimal? ttcktmai0 { get; set; }

    /// <summary>
    /// Tổng tiền sau thuế cho thuế suất 0%
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtttbso0")]
    public decimal? tgtttbso0 { get; set; }

    /// <summary>
    /// Tổng tiền trước thuế cho thuế suất 5%
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtcthue5")]
    public decimal? tgtcthue5 { get; set; }

    /// <summary>
    /// Tổng tiền thuế cho thuế suất 5%
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtthue5")]
    public decimal? tgtthue5 { get; set; }

    /// <summary>
    /// Tổng tiền chiết khấu mặt hàng cho thuế suất 5%
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("ttcktmai5")]
    public decimal? ttcktmai5 { get; set; }

    /// <summary>
    /// Tổng tiền sau thuế cho thuế suất 5%
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtttbso5")]
    public decimal? tgtttbso5 { get; set; }

    /// <summary>
    /// Tổng tiền trước thuế cho thuế suất 10%
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtcthue10")]
    public decimal? tgtcthue10 { get; set; }

    /// <summary>
    /// Tổng tiền thuế cho thuế suất 10%
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtthue10")]
    public decimal? tgtthue10 { get; set; }

    /// <summary>
    /// Tổng tiền chiết khấu mặt hàng cho thuế suất 10%
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("ttcktmai10")]
    public decimal? ttcktmai10 { get; set; }

    /// <summary>
    /// Tổng tiền sau thuế cho thuế suất 10%
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtttbso10")]
    public decimal? tgtttbso10 { get; set; }

    /// <summary>
    /// Tổng tiền trước thuế cho loại không chịu thuế
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtcthuekct")]
    public decimal? tgtcthuekct { get; set; }

    /// <summary>
    /// Tổng tiền thuế cho loại không chịu thuế
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtthuekct")]
    public decimal? tgtthuekct { get; set; }

    /// <summary>
    /// Tổng tiền chiết khấu mặt hàng cho loại không chịu thuế
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("ttcktmaikct")]
    public decimal? ttcktmaikct { get; set; }

    /// <summary>
    /// Tổng tiền sau thuế cho loại không chịu thuế
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtttbsokct")]
    public decimal? tgtttbsokct { get; set; }

    /// <summary>
    /// Tổng tiền trước thuế cho loại không kê khai nộp thuế
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtcthuekkk")]
    public decimal? tgtcthuekkk { get; set; }

    /// <summary>
    /// Tổng tiền thuế cho loại không kê khai nộp thuế
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtthuekkk")]
    public decimal? tgtthuekkk { get; set; }

    /// <summary>
    /// Tổng tiền chiết khấu mặt hàng cho loại không kê khai nộp thuế
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("ttcktmaikkk")]
    public decimal? ttcktmaikkk { get; set; }

    /// <summary>
    /// Tổng tiền sau thuế cho loại không kê khai nộp thuế
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtttbsokkk")]
    public decimal? tgtttbsokkk { get; set; }

    /// <summary>
    /// Tổng tiền phí cho thuế suất 0%
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtphi0")]
    public decimal? tgtphi0 { get; set; }

    /// <summary>
    /// Tổng tiền phí cho thuế suất 5%
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtphi5")]
    public decimal? tgtphi5 { get; set; }

    /// <summary>
    /// Tổng tiền phí cho thuế suất 10%
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtphi10")]
    public decimal? tgtphi10 { get; set; }

    /// <summary>
    /// Tổng tiền phí cho loại không chịu thuế
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtphikct")]
    public decimal? tgtphikct { get; set; }

    /// <summary>
    /// Tổng tiền phí cho loại không kê khai nộp thuế
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtphikkk")]
    public decimal? tgtphikkk { get; set; }

    /// <summary>
    /// Loại hóa đơn
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("lhdon")]
    public int? lhdon { get; set; }

    /// <summary>
    /// Lệnh điều động nội bộ (đối với PXKVCNB)
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("lddnbo")]
    public string? lddnbo { get; set; }

    /// <summary>
    /// Tên người vận chuyển
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("tnvchuyen")]
    public string? tnvchuyen { get; set; }

    /// <summary>
    /// Phương tiện vận chuyển
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("ptvchuyen")]
    public string? ptvchuyen { get; set; }

    /// <summary>
    /// Địa chỉ kho xuất
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("dckhoxuat")]
    public string? dckhoxuat { get; set; }

    /// <summary>
    /// Địa chỉ kho nhập
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("dckhonhap")]
    public string? dckhonhap { get; set; }

    /// <summary>
    /// Tên người nhận hàng
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("tennguoinhanhang")]
    public string? tennguoinhanhang { get; set; }

    /// <summary>
    /// MST người nhận hàng
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("mstnguoinhanhang")]
    public string? mstnguoinhanhang { get; set; }

    /// <summary>
    /// Phòng ban
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("phongban")]
    public string? phongban { get; set; }

    /// <summary>
    /// Về việc
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("veviec")]
    public string? veviec { get; set; }

    /// <summary>
    /// Số hợp đồng
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("sohopdong")]
    public string? sohopdong { get; set; }

    /// <summary>
    /// ID liên kết hóa đơn 68
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("hdon68_id_lk")]
    public string? hdon68_id_lk { get; set; }

    /// <summary>
    /// Mã thông điệp từ CQT
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("mtdiep_cqt")]
    public string? mtdiep_cqt { get; set; }

    /// <summary>
    /// Mã thông điệp gửi
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("mtdiep_gui")]
    public string? mtdiep_gui { get; set; }

    /// <summary>
    /// Trạng thái hóa đơn cũ
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tthdon_old")]
    public int? tthdon_old { get; set; }

    /// <summary>
    /// ID hóa đơn gốc (khi điều chỉnh/thay thế)
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("hdon_id_old")]
    public string? hdon_id_old { get; set; }

    /// <summary>
    /// Phân biệt hóa đơn có mã/không có mã
    /// Kiểu: Number
    /// 0: Không mã, 1: Có mã
    /// </summary>
    [JsonPropertyName("is_hdcma")]
    public int? is_hdcma { get; set; }

    /// <summary>
    /// Ghi chú hóa đơn
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("hdon_ghichu")]
    public string? hdon_ghichu { get; set; }

    /// <summary>
    /// Trạng thái hóa đơn gốc
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tthdon_original")]
    public int? tthdon_original { get; set; }

    /// <summary>
    /// Trạng thái ký gửi CQT
    /// Kiểu: Number
    /// 0: chưa gửi, 1: đã gửi
    /// </summary>
    [JsonPropertyName("kygui_cqt")]
    public decimal? kygui_cqt { get; set; }

    /// <summary>
    /// Ngày hợp đồng kinh tế
    /// Kiểu: Date
    /// </summary>
    [JsonPropertyName("hdktngay")]
    public string? hdktngay { get; set; }

    /// <summary>
    /// Tên ngân hàng bên bán
    /// Kiểu: String, độ dài: 250
    /// </summary>
    [JsonPropertyName("tnhban")]
    public string? tnhban { get; set; }

    /// <summary>
    /// Tên ngân hàng bên mua
    /// Kiểu: String, độ dài: 100
    /// </summary>
    [JsonPropertyName("tnhmua")]
    public string? tnhmua { get; set; }

    /// <summary>
    /// Hóa đơn có chiết khấu phí thuế quan
    /// Kiểu: Boolean
    /// </summary>
    [JsonPropertyName("hddckptquan")]
    public bool? hddckptquan { get; set; }

    /// <summary>
    /// Số bảng kê
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("sbke")]
    public string? sbke { get; set; }

    /// <summary>
    /// Fax bên bán
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("faxban")]
    public string? faxban { get; set; }

    /// <summary>
    /// Website bên bán
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("webban")]
    public string? webban { get; set; }

    /// <summary>
    /// Số QDBTS (Quyết định bán tài sản)
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("sqdbants")]
    public string? sqdbants { get; set; }

    /// <summary>
    /// Ngày QDBTS
    /// Kiểu: Date
    /// </summary>
    [JsonPropertyName("nqdbants")]
    public string? nqdbants { get; set; }

    /// <summary>
    /// Cơ quan QDBTS
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("cqqdbants")]
    public string? cqqdbants { get; set; }

    /// <summary>
    /// Hình thức QDBTS
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("htbants")]
    public string? htbants { get; set; }

    /// <summary>
    /// Chứng minh thư/căn cước công dân người mua
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("cmndmua")]
    public string? cmndmua { get; set; }

    /// <summary>
    /// Hợp đồng vận chuyển
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("hdvc")]
    public string? hdvc { get; set; }

    /// <summary>
    /// Tên người xuất hàng
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("hvtnxhang")]
    public string? hvtnxhang { get; set; }

    /// <summary>
    /// Hợp đồng kinh tế số
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("hdktso")]
    public string? hdktso { get; set; }

    /// <summary>
    /// Ngày bảng kê
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("nbke")]
    public int? nbke { get; set; }

    /// <summary>
    /// Địa điểm vận chuyển đến
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("ddvchden")]
    public string? ddvchden { get; set; }

    /// <summary>
    /// Thời gian vận chuyển từ
    /// Kiểu: DateTime
    /// </summary>
    [JsonPropertyName("tgvchdtu")]
    public string? tgvchdtu { get; set; }

    /// <summary>
    /// Thời gian vận chuyển đến
    /// Kiểu: DateTime
    /// </summary>
    [JsonPropertyName("tgvchdden")]
    public string? tgvchdden { get; set; }

    /// <summary>
    /// Số điện thoại bên bán
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("sdtban")]
    public string? sdtban { get; set; }

    /// <summary>
    /// Tổng tiền chiết khấu thương mại
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tkcktmn")]
    public decimal? tkcktmn { get; set; }

    /// <summary>
    /// Tổng tiền cuối cùng của cả hóa đơn
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtttbso_last")]
    public decimal? tgtttbso_last { get; set; }

    /// <summary>
    /// Mã đơn vị quan hệ ngân sách mua
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("mdvqhnsach_mua")]
    public string? mdvqhnsach_mua { get; set; }

    /// <summary>
    /// Mã đơn vị quan hệ ngân sách bán
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("mdvqhnsach_ban")]
    public string? mdvqhnsach_ban { get; set; }

    /// <summary>
    /// Số thông báo
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("stbao")]
    public int? stbao { get; set; }

    /// <summary>
    /// Ngày thông báo
    /// Kiểu: Date
    /// </summary>
    [JsonPropertyName("ntbao")]
    public string? ntbao { get; set; }

    /// <summary>
    /// Tính chất của dòng hàng hóa dịch vụ
    /// Kiểu: Number
    /// 1: Hàng hóa dịch vụ
    /// </summary>
    [JsonPropertyName("kmai")]
    public int? kmai { get; set; }

    /// <summary>
    /// Tổng tiền trước thuế (phân loại khác)
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtcthuek")]
    public decimal? tgtcthuek { get; set; }

    /// <summary>
    /// Tổng tiền thuế (phân loại khác)
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtthuek")]
    public decimal? tgtthuek { get; set; }

    /// <summary>
    /// Tổng tiền chiết khấu mặt hàng (phân loại khác)
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("ttcktmaik")]
    public decimal? ttcktmaik { get; set; }

    /// <summary>
    /// Tổng tiền sau thuế (phân loại khác)
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtttbsok")]
    public decimal? tgtttbsok { get; set; }

    /// <summary>
    /// Trạng thái lỗi
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("error_status")]
    public string? error_status { get; set; }

    /// <summary>
    /// Trạng thái gửi email
    /// Kiểu: Boolean
    /// </summary>
    [JsonPropertyName("issendmail")]
    public bool? issendmail { get; set; }

    /// <summary>
    /// Chuyển đổi ngoại tệ sang tiếng Việt
    /// Kiểu: Boolean
    /// </summary>
    [JsonPropertyName("docngoaitetv")]
    public bool? docngoaitetv { get; set; }

    /// <summary>
    /// Hóa đơn giảm thuế theo nghị quyết 101/2023/QH15
    /// Kiểu: Boolean
    /// </summary>
    [JsonPropertyName("giamthuebanhang20")]
    public bool? giamthuebanhang20 { get; set; }

    /// <summary>
    /// Số tiền được giảm theo nghị quyết 101/2023/QH15
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tienthuegtgtgiam")]
    public decimal? tienthuegtgtgiam { get; set; }

    /// <summary>
    /// Loại hóa đơn có liên quan
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("lhdclquan")]
    public int? lhdclquan { get; set; }

    /// <summary>
    /// Ký hiệu mẫu số hóa đơn có liên quan
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("khmshdclquan")]
    public string? khmshdclquan { get; set; }

    /// <summary>
    /// Ký hiệu hóa đơn có liên quan
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("khhdclquan")]
    public string? khhdclquan { get; set; }

    /// <summary>
    /// Số hóa đơn có liên quan
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("shdclquan")]
    public string? shdclquan { get; set; }

    /// <summary>
    /// Ngày lập hóa đơn có liên quan
    /// Kiểu: Date
    /// </summary>
    [JsonPropertyName("nlhdclquan")]
    public string? nlhdclquan { get; set; }

    /// <summary>
    /// Tỷ lệ thuế giảm GTGT
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tiletienthuegtgtgiam")]
    public decimal? tiletienthuegtgtgiam { get; set; }

    /// <summary>
    /// Tổng giảm trừ khác
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtkhac")]
    public decimal? tgtkhac { get; set; }

    /// <summary>
    /// Trường thdon
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("thdon")]
    public string? thdon { get; set; }

    /// <summary>
    /// Quốc tịch
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("qtich")]
    public string? qtich { get; set; }

    /// <summary>
    /// Căn cước trú
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("cnctru")]
    public int? cnctru { get; set; }

    /// <summary>
    /// Ngày cấp CMND
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("ngccmnd")]
    public string? ngccmnd { get; set; }

    /// <summary>
    /// Nơi cấp CMND
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("nccmnd")]
    public string? nccmnd { get; set; }

    /// <summary>
    /// Kết nhập
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("ktnhap")]
    public string? ktnhap { get; set; }

    /// <summary>
    /// Tháng
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("thang")]
    public int? thang { get; set; }

    /// <summary>
    /// Năm
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("nam")]
    public int? nam { get; set; }

    /// <summary>
    /// Mã số biểu lệ TNCN
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("msbl_tncn")]
    public string? msbl_tncn { get; set; }

    /// <summary>
    /// Tỷ lệ khấu trừ
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tlkthu")]
    public decimal? tlkthu { get; set; }

    /// <summary>
    /// Loại hóa đơn
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("loaihd")]
    public int? loaihd { get; set; }

    /// <summary>
    /// Số CMND/CCCD
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("cmnd")]
    public string? cmnd { get; set; }

    /// <summary>
    /// Đơn vị tính
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("dvtinh")]
    public string? dvtinh { get; set; }

    /// <summary>
    /// Hóa đơn tự định
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("hdtndinh")]
    public string? hdtndinh { get; set; }

    /// <summary>
    /// Hóa đơn chiết khấu
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("hdckhau")]
    public string? hdckhau { get; set; }

    /// <summary>
    /// Kết định bảng hàng bắt buộc
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("kdbhbbuoc")]
    public string? kdbhbbuoc { get; set; }

    /// <summary>
    /// Kết nhập string
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("ktnhapstr")]
    public string? ktnhapstr { get; set; }

    /// <summary>
    /// Căn cước không cư trú
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("cnkctru")]
    public int? cnkctru { get; set; }

    /// <summary>
    /// Tự động tính tiền
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tudongtinhtien")]
    public int? tudongtinhtien { get; set; }

    /// <summary>
    /// Tổng giá trị không chịu thuế
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtkcthue")]
    public decimal? tgtkcthue { get; set; }

    /// <summary>
    /// Từ tháng
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tthang")]
    public int? tthang { get; set; }

    /// <summary>
    /// Đến tháng
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("dthang")]
    public int? dthang { get; set; }

    /// <summary>
    /// Tổng trạng thái thanh toán
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("ttthanhtoan")]
    public int? ttthanhtoan { get; set; }

    /// <summary>
    /// Mã FKEY hóa đơn
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("fkey")]
    public string? fkey { get; set; }

    /// <summary>
    /// Danh sách chi tiết hóa đơn dưới dạng chuỗi JSON
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("details")]
    public string? details { get; set; }

    /// <summary>
    /// Danh sách các trường thẻ khác bổ sung
    /// Kiểu: Array
    /// </summary>
    [JsonPropertyName("hoadon68_khac")]
    public object? hoadon68_khac { get; set; }

    /// <summary>
    /// Danh sách các loại tiền phí/lệ phí
    /// Kiểu: Array
    /// </summary>
    [JsonPropertyName("hoadon68_phi")]
    public object? hoadon68_phi { get; set; }
}
