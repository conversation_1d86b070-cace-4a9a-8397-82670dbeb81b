using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.Common;

/// <summary>
/// Base class chứa tất cả các trường chung cho tiền phí/lệ phí hóa đơn
/// Đ<PERSON><PERSON> là những trường xuất hiện ở tất cả các loại hóa đơn trong mảng hoadon68_phi
/// </summary>
public class InvoiceFeeFields
{
    /// <summary>
    /// Tên phí
    /// Kiểu: String, Độ dài: 4, Bắt buộc: X
    /// </summary>
    [JsonPropertyName("tnphi")]
    public string tnphi { get; set; } = string.Empty;

    /// <summary>
    /// Tiền phí
    /// Kiểu: Number, Độ dài: 100, Bắt buộc: X
    /// </summary>
    [JsonPropertyName("tienphi")]
    public decimal tienphi { get; set; }
}
