using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.Common;

/// <summary>
/// Base class chứa tất cả các trường chung cho chi tiết hóa đơn
/// Đ<PERSON><PERSON> là những trường xuất hiện ở tất cả các loại hóa đơn trong mảng details
/// </summary>
public class InvoiceDetailFields
{
    /// <summary>
    /// S<PERSON> thứ tự dòng của hóa đơn (Ví dụ: 1, 2…)
    /// Kiểu: String, Độ dài: 4, Bắt buộc: X
    /// </summary>
    [JsonPropertyName("stt")]
    public string stt { get; set; } = string.Empty;

    /// <summary>
    /// Mã hàng
    /// Kiểu: String, Độ dài: 100
    /// </summary>
    [JsonPropertyName("ma")]
    public string? ma { get; set; }

    /// <summary>
    /// Tên hàng
    /// Kiể<PERSON>: String, <PERSON><PERSON> dài: 250, Bắt buộc: X
    /// </summary>
    [JsonPropertyName("ten")]
    public string ten { get; set; } = string.Empty;

    /// <summary>
    /// Mã đơn vị tính
    /// Kiểu: String, Độ dài: 10
    /// </summary>
    [JsonPropertyName("mdvtinh")]
    public string? mdvtinh { get; set; }

    /// <summary>
    /// Tên đơn vị tính
    /// Kiểu: String, Độ dài: 50
    /// </summary>
    [JsonPropertyName("dvtinh")]
    public string? dvtinh { get; set; }

    /// <summary>
    /// Đơn giá
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("dgia")]
    public decimal? dgia { get; set; }

    /// <summary>
    /// Số lượng
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("sluong")]
    public decimal? sluong { get; set; }

    /// <summary>
    /// Phần trăm chiết khấu
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tlckhau")]
    public decimal? tlckhau { get; set; }

    /// <summary>
    /// Tiền chiết khấu
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("stckhau")]
    public decimal? stckhau { get; set; }

    /// <summary>
    /// Tiền trước thuế
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("thtien")]
    public decimal? thtien { get; set; }

    /// <summary>
    /// Tiền thuế
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tthue")]
    public decimal? tthue { get; set; }

    /// <summary>
    /// Tổng tiền
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtien")]
    public decimal? tgtien { get; set; }

    /// <summary>
    /// Tính chất của dòng hàng hóa dịch vụ, bắt buộc truyền
    /// 1: Hàng hóa dịch vụ, 2: Khuyến mại, 3: Chiết khấu thương mại, 4: Ghi chú, 5: Hàng hoá đặc trưng
    /// Kiểu: Number, Bắt buộc: X
    /// </summary>
    [JsonPropertyName("kmai")]
    public int kmai { get; set; }

    /// <summary>
    /// Mã loại thuế suất
    /// 10: Thuế 10%, 8: Thuế 8%, 5: Thuế 5%, 0: Thuế 0%, -1: Không chịu thuế, -2: Không nộp thuế
    /// Kiểu: String, Độ dài: 20
    /// Lưu ý: Đối với HĐ Bán hàng, trường này không bắt buộc
    /// </summary>
    [JsonPropertyName("tsuat")]
    public string? tsuat { get; set; }

    // ===== CÁC TRƯỜNG CHO HÀNG HÓA ĐẶC TRƯNG (kmai = 5) =====

    /// <summary>
    /// Loại hàng hoá đặc trưng (Bắt buộc nếu kmai = 5)
    /// 1: Hàng hoá là xe ô tô, xe mô tô
    /// 2: Dịch vụ vận chuyển
    /// 3: Dịch vụ vận chuyển trên nền tảng số, TMĐT
    /// Kiểu: Number, Độ dài: 1, Bắt buộc: X (nếu kmai = 5)
    /// </summary>
    [JsonPropertyName("lhhdthu")]
    public int? lhhdthu { get; set; }

    /// <summary>
    /// Số khung (Bắt buộc nếu lhhdthu = 1)
    /// Kiểu: String, Độ dài: 255
    /// </summary>
    [JsonPropertyName("skhung")]
    public string? skhung { get; set; }

    /// <summary>
    /// Số máy (Bắt buộc nếu lhhdthu = 1)
    /// Kiểu: String, Độ dài: 255
    /// </summary>
    [JsonPropertyName("smay")]
    public string? smay { get; set; }

    /// <summary>
    /// Biển kiểm soát phương tiện vận chuyển (Bắt buộc nếu lhhdthu = 2)
    /// Kiểu: String, Độ dài: 255
    /// </summary>
    [JsonPropertyName("bksptvchuyen")]
    public string? bksptvchuyen { get; set; }

    /// <summary>
    /// Tên người gửi hàng (Bắt buộc nếu lhhdthu = 3)
    /// Kiểu: String, Độ dài: 255
    /// </summary>
    [JsonPropertyName("tnghang")]
    public string? tnghang { get; set; }

    /// <summary>
    /// Địa chỉ người gửi hàng (Bắt buộc nếu lhhdthu = 3)
    /// Kiểu: String, Độ dài: 255
    /// </summary>
    [JsonPropertyName("dcnghang")]
    public string? dcnghang { get; set; }

    /// <summary>
    /// MST người gửi hàng (Bắt buộc nếu lhhdthu = 3)
    /// Kiểu: String, Độ dài: 255
    /// </summary>
    [JsonPropertyName("mstnghang")]
    public string? mstnghang { get; set; }

    /// <summary>
    /// Số định danh người gửi hàng (Bắt buộc nếu lhhdthu = 3)
    /// Kiểu: String, Độ dài: 255
    /// </summary>
    [JsonPropertyName("mddnghang")]
    public string? mddnghang { get; set; }
}
