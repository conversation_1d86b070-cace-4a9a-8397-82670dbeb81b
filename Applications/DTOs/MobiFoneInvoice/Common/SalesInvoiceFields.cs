using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.Common;

/// <summary>
/// Class chứa các trường đặc biệt cho Hóa đơn Bán hàng
/// Kế thừa từ NormalInvoiceHeaderFields và thêm các trường riêng biệt
/// </summary>
public class SalesInvoiceFields : NormalInvoiceHeaderFields
{
    /// <summary>
    /// Phí thuế quan (Đặc biệt cho Hóa đơn Bán hàng)
    /// 0: không có, 1: có phí
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("hddckptquan")]
    public decimal? hddckptquan { get; set; }

    /// <summary>
    /// Hóa đơn giảm theo nghị quyết 101/2023/QH15 (Đặc biệt cho Hóa đơn Bán hàng)
    /// 1: <PERSON><PERSON> giả<PERSON>, 0: Không giảm - <PERSON><PERSON><PERSON> buộc nếu có
    /// Kiểu: Boolean
    /// </summary>
    [JsonPropertyName("giamthuebanhang20")]
    public bool? giamthuebanhang20 { get; set; }

    /// <summary>
    /// Số tiền được giảm theo nghị quyết 101/2023/QH15 (Đặc biệt cho Hóa đơn Bán hàng)
    /// Bắt buộc nếu có
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tienthuegtgtgiam")]
    public decimal? tienthuegtgtgiam { get; set; }

    /// <summary>
    /// Trạng thái Hóa đơn - Giới hạn cho Hóa đơn Bán hàng
    /// 0: "Gốc", 2: "Thay thế", 19: "Điều chỉnh tăng", 21: "Điều chỉnh giảm", 23: "Điều chỉnh thông tin"
    /// Kiểu: Number, Bắt buộc: X
    /// </summary>
    [JsonPropertyName("tthdon")]
    public new decimal? tthdon { get; set; }

    /// <summary>
    /// Danh sách chi tiết hóa đơn bán hàng
    /// </summary>
    [JsonPropertyName("details")]
    public List<InvoiceDetailWrapper<SalesInvoiceDetailFields>> details { get; set; } = new();

    /// <summary>
    /// Danh sách các loại tiền phí/lệ phí
    /// </summary>
    [JsonPropertyName("hoadon68_phi")]
    public List<InvoiceFeeWrapper<InvoiceFeeFields>>? hoadon68_phi { get; set; }

    /// <summary>
    /// Danh sách các trường thẻ khác bổ sung
    /// </summary>
    [JsonPropertyName("hoadon68_khac")]
    public List<InvoiceOtherWrapper<InvoiceOtherFields>>? hoadon68_khac { get; set; }
}

/// <summary>
/// Chi tiết hóa đơn Bán hàng - kế thừa từ InvoiceDetailFields
/// Cho Hóa đơn Bán hàng, tsuat KHÔNG bắt buộc và có thêm các trường giảm thuế
/// </summary>
public class SalesInvoiceDetailFields : InvoiceDetailFields
{
    /// <summary>
    /// Tỷ lệ thuế giảm theo NĐ… của hàng hóa này (Đặc biệt cho Hóa đơn Bán hàng)
    /// Kiểu: Number, Bắt buộc nếu có
    /// </summary>
    [JsonPropertyName("tiletienthuegtgtgiam")]
    public decimal? tiletienthuegtgtgiam { get; set; }

    /// <summary>
    /// Số tiền thuế giảm theo NĐ… của hàng hóa này (Đặc biệt cho Hóa đơn Bán hàng)
    /// Kiểu: Number, Bắt buộc nếu có
    /// </summary>
    [JsonPropertyName("tienthuegtgtgiam")]
    public decimal? tienthuegtgtgiam { get; set; }
}
