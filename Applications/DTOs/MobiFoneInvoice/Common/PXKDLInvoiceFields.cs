using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.Common;

/// <summary>
/// Class chứa các trường đặc biệt cho Phiếu xuất kho hàng gửi bán đại lý (PXKDL)
/// Kế thừa từ NormalInvoiceHeaderFields và thêm các trường riêng biệt
/// </summary>
public class PXKDLInvoiceFields : NormalInvoiceHeaderFields
{
    /// <summary>
    /// Địa chỉ kho xuất (Đặc biệt cho PXKDL)
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("dckhoxuat")]
    public string? dckhoxuat { get; set; }

    /// <summary>
    /// Tên người xuất hàng (Đặc biệt cho PXKDL)
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("hvtnxhang")]
    public string? hvtnxhang { get; set; }

    /// <summary>
    /// Hợp đồng vận chuyển (Đặc biệt cho PXKDL)
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("hdvc")]
    public string? hdvc { get; set; }

    /// <summary>
    /// Tên người vận chuyển (Đặc biệt cho PXKDL)
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("tnvchuyen")]
    public string? tnvchuyen { get; set; }

    /// <summary>
    /// Phương tiện vận chuyển (Đặc biệt cho PXKDL)
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("ptvchuyen")]
    public string? ptvchuyen { get; set; }

    /// <summary>
    /// Hợp đồng kinh tế số (Đặc biệt cho PXKDL)
    /// Kiểu: String, Bắt buộc: X
    /// </summary>
    [JsonPropertyName("hdktso")]
    public string hdktso { get; set; } = string.Empty;

    /// <summary>
    /// Ngày hợp đồng kinh tế (Đặc biệt cho PXKDL)
    /// Kiểu: Date, Bắt buộc: X, Định dạng: yyyy-MM-dd
    /// </summary>
    [JsonPropertyName("hdktngay")]
    public string hdktngay { get; set; } = string.Empty;

    /// <summary>
    /// Danh sách chi tiết hóa đơn PXKDL
    /// </summary>
    [JsonPropertyName("details")]
    public List<InvoiceDetailWrapper<InvoiceDetailFields>> details { get; set; } = new();

    /// <summary>
    /// Danh sách các loại tiền phí/lệ phí
    /// </summary>
    [JsonPropertyName("hoadon68_phi")]
    public List<InvoiceFeeWrapper<InvoiceFeeFields>>? hoadon68_phi { get; set; }

    /// <summary>
    /// Danh sách các trường thẻ khác bổ sung
    /// </summary>
    [JsonPropertyName("hoadon68_khac")]
    public List<InvoiceOtherWrapper<InvoiceOtherFields>>? hoadon68_khac { get; set; }
}
