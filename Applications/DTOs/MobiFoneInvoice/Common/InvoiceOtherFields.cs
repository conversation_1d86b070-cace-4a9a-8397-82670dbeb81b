using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.Common;

/// <summary>
/// Base class chứa tất cả các trường chung cho các trường thẻ khác bổ sung
/// Đ<PERSON><PERSON> là những trường xuất hiện ở tất cả các loại hóa đơn trong mảng hoadon68_khac
/// </summary>
public class InvoiceOtherFields
{
    /// <summary>
    /// Tên trường
    /// Kiểu: String, Độ dài: 4, Bắt buộc: X
    /// </summary>
    [JsonPropertyName("ttruong")]
    public string ttruong { get; set; } = string.Empty;

    /// <summary>
    /// Kiểu dữ liệu
    /// Kiểu: String, Độ dài: 100, Bắt buộc: X
    /// Các giá trị: string, numeric, dateTime, date
    /// </summary>
    [JsonPropertyName("kdlieu")]
    public string kdlieu { get; set; } = string.Empty;

    /// <summary>
    /// Gi<PERSON> trị
    /// Kiểu: String, Bắt buộc: X
    /// </summary>
    [JsonPropertyName("dlieu")]
    public string dlieu { get; set; } = string.Empty;
}
