using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.Common;

/// <summary>
/// Wrapper chung cho chi tiết hóa đơn (Invoice Detail Wrapper)
/// </summary>
/// <typeparam name="T">Loại chi tiết hóa đơn kế thừa từ InvoiceDetailFields</typeparam>
public class InvoiceDetailWrapper<T> where T : InvoiceDetailFields
{
    /// <summary>
    /// Mảng dữ liệu chi tiết hóa đơn
    /// </summary>
    [JsonPropertyName("data")]
    public List<T> data { get; set; } = new();
}

/// <summary>
/// Wrapper chung cho các loại tiền phí/lệ phí hóa đơn (Invoice Fee Wrapper)
/// </summary>
/// <typeparam name="T"><PERSON><PERSON>i ph<PERSON> kế thừa từ InvoiceFeeFields</typeparam>
public class InvoiceFeeWrapper<T> where T : InvoiceFeeFields
{
    /// <summary>
    /// Mảng dữ liệu các loại tiền phí/lệ phí
    /// </summary>
    [JsonPropertyName("data")]
    public List<T> data { get; set; } = new();
}

/// <summary>
/// Wrapper chung cho các trường thẻ khác bổ sung (Invoice Other Wrapper)
/// </summary>
/// <typeparam name="T">Loại trường bổ sung kế thừa từ InvoiceOtherFields</typeparam>
public class InvoiceOtherWrapper<T> where T : InvoiceOtherFields
{
    /// <summary>
    /// Mảng dữ liệu các trường thẻ khác bổ sung
    /// </summary>
    [JsonPropertyName("data")]
    public List<T> data { get; set; } = new();
}
