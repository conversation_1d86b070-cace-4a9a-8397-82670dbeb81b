using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.Common;

/// <summary>
/// Class chứa các trường đặc biệt cho Phiếu xuất kho kiêm vận chuyển nội bộ (PXKVCNB)
/// Kế thừa từ NormalInvoiceHeaderFields và thêm các trường riêng biệt
/// </summary>
public class PXKVCNBInvoiceFields : NormalInvoiceHeaderFields
{
    /// <summary>
    /// Lệnh điều động nội bộ (Đặc biệt cho PXKVCNB)
    /// Kiểu: String, Độ dài: 255
    /// </summary>
    [JsonPropertyName("lddnbo")]
    public string? lddnbo { get; set; }

    /// <summary>
    /// Địa chỉ kho xuất (Đặc biệt cho PXKVCNB)
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("dckhoxuat")]
    public string? dckhoxuat { get; set; }

    /// <summary>
    /// Tên người xuất hàng (Đặc biệt cho PXKVCNB)
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("hvtnxhang")]
    public string? hvtnxhang { get; set; }

    /// <summary>
    /// Hợp đồng vận chuyển (Đặc biệt cho PXKVCNB)
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("hdvc")]
    public string? hdvc { get; set; }

    /// <summary>
    /// Tên người vận chuyển (Đặc biệt cho PXKVCNB)
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("tnvchuyen")]
    public string? tnvchuyen { get; set; }

    /// <summary>
    /// Phương tiện vận chuyển (Đặc biệt cho PXKVCNB)
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("ptvchuyen")]
    public string? ptvchuyen { get; set; }

    /// <summary>
    /// Danh sách chi tiết hóa đơn PXKVCNB
    /// </summary>
    [JsonPropertyName("details")]
    public List<InvoiceDetailWrapper<InvoiceDetailFields>> details { get; set; } = new();

    /// <summary>
    /// Danh sách các loại tiền phí/lệ phí
    /// </summary>
    [JsonPropertyName("hoadon68_phi")]
    public List<InvoiceFeeWrapper<InvoiceFeeFields>>? hoadon68_phi { get; set; }

    /// <summary>
    /// Danh sách các trường thẻ khác bổ sung
    /// </summary>
    [JsonPropertyName("hoadon68_khac")]
    public List<InvoiceOtherWrapper<InvoiceOtherFields>>? hoadon68_khac { get; set; }
}
