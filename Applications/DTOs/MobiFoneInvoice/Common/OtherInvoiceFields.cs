using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.Common;

/// <summary>
/// Class chứa các trường đặc biệt cho Hóa đơn Bán tài sản công
/// <PERSON><PERSON> thừa từ NormalInvoiceHeaderFields và thêm các trường riêng biệt
/// </summary>
public class PublicAssetSalesInvoiceFields : NormalInvoiceHeaderFields
{
    /// <summary>
    /// Mã đơn vị quan hệ ngân sách bán
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("mdvqhnsach_ban")]
    public string? mdvqhnsach_ban { get; set; }

    /// <summary>
    /// Fax
    /// Kiểu: String, Độ dài: 20
    /// </summary>
    [JsonPropertyName("fax")]
    public string? fax { get; set; }

    /// <summary>
    /// Website
    /// Kiểu: String, <PERSON><PERSON> dài: 100
    /// </summary>
    [JsonPropertyName("website")]
    public string? website { get; set; }

    /// <summary>
    /// Số QDBTS (Quyết định bán tài sản)
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("sqdbants")]
    public string? sqdbants { get; set; }

    /// <summary>
    /// Ngày QDBTS
    /// Kiểu: Date
    /// </summary>
    [JsonPropertyName("nqdbants")]
    public string? nqdbants { get; set; }

    /// <summary>
    /// Cơ quan QDBTS
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("cqqdbants")]
    public string? cqqdbants { get; set; }

    /// <summary>
    /// Hình thức QDBTS
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("htbants")]
    public string? htbants { get; set; }

    /// <summary>
    /// Chứng minh thư, căn cước công dân - Bắt buộc nếu có
    /// Kiểu: String, Độ dài: 100
    /// </summary>
    [JsonPropertyName("cmndmua")]
    public string? cmndmua { get; set; }

    /// <summary>
    /// Địa điểm vận chuyển đến
    /// Kiểu: String, Độ dài: 300
    /// </summary>
    [JsonPropertyName("ddvchden")]
    public string? ddvchden { get; set; }

    /// <summary>
    /// Thời gian vận chuyển từ
    /// Kiểu: Date
    /// </summary>
    [JsonPropertyName("tgvchdtu")]
    public string? tgvchdtu { get; set; }

    /// <summary>
    /// Thời gian vận chuyển đến
    /// Kiểu: Date
    /// </summary>
    [JsonPropertyName("tgvchdden")]
    public string? tgvchdden { get; set; }

    /// <summary>
    /// Danh sách chi tiết hóa đơn
    /// </summary>
    [JsonPropertyName("details")]
    public List<InvoiceDetailWrapper<InvoiceDetailFields>> details { get; set; } = new();

    /// <summary>
    /// Danh sách các loại tiền phí/lệ phí
    /// </summary>
    [JsonPropertyName("hoadon68_phi")]
    public List<InvoiceFeeWrapper<InvoiceFeeFields>>? hoadon68_phi { get; set; }

    /// <summary>
    /// Danh sách các trường thẻ khác bổ sung
    /// </summary>
    [JsonPropertyName("hoadon68_khac")]
    public List<InvoiceOtherWrapper<InvoiceOtherFields>>? hoadon68_khac { get; set; }
}

/// <summary>
/// Class chứa các trường đặc biệt cho Hóa đơn Bán hàng dự trữ quốc gia
/// Kế thừa từ NormalInvoiceHeaderFields và thêm các trường riêng biệt
/// </summary>
public class NationalReserveSalesInvoiceFields : NormalInvoiceHeaderFields
{
    /// <summary>
    /// CMND người mua - Bắt buộc nếu có
    /// Kiểu: String, Độ dài: 100
    /// </summary>
    [JsonPropertyName("cmndmua")]
    public string? cmndmua { get; set; }

    /// <summary>
    /// Danh sách chi tiết hóa đơn
    /// </summary>
    [JsonPropertyName("details")]
    public List<InvoiceDetailWrapper<InvoiceDetailFields>> details { get; set; } = new();

    /// <summary>
    /// Danh sách các loại tiền phí/lệ phí
    /// </summary>
    [JsonPropertyName("hoadon68_phi")]
    public List<InvoiceFeeWrapper<InvoiceFeeFields>>? hoadon68_phi { get; set; }

    /// <summary>
    /// Danh sách các trường thẻ khác bổ sung
    /// </summary>
    [JsonPropertyName("hoadon68_khac")]
    public List<InvoiceOtherWrapper<InvoiceOtherFields>>? hoadon68_khac { get; set; }
}

/// <summary>
/// Class chứa các trường đặc biệt cho Phiếu xuất kho kiêm vận chuyển nội bộ (PXKVCNB)
/// Kế thừa từ NormalInvoiceHeaderFields và thêm các trường riêng biệt
/// </summary>
public class InternalTransportInvoiceFields : NormalInvoiceHeaderFields
{
    /// <summary>
    /// Lệnh điều động nội bộ
    /// Kiểu: String, Độ dài: 255
    /// </summary>
    [JsonPropertyName("lddnbo")]
    public string? lddnbo { get; set; }

    /// <summary>
    /// Địa chỉ kho xuất
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("dckhoxuat")]
    public string? dckhoxuat { get; set; }

    /// <summary>
    /// Tên người xuất hàng
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("hvtnxhang")]
    public string? hvtnxhang { get; set; }

    /// <summary>
    /// Hợp đồng vận chuyển
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("hdvc")]
    public string? hdvc { get; set; }

    /// <summary>
    /// Tên người vận chuyển
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("tnvchuyen")]
    public string? tnvchuyen { get; set; }

    /// <summary>
    /// Phương tiện vận chuyển
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("ptvchuyen")]
    public string? ptvchuyen { get; set; }

    /// <summary>
    /// Danh sách chi tiết hóa đơn
    /// </summary>
    [JsonPropertyName("details")]
    public List<InvoiceDetailWrapper<InvoiceDetailFields>> details { get; set; } = new();

    /// <summary>
    /// Danh sách các loại tiền phí/lệ phí
    /// </summary>
    [JsonPropertyName("hoadon68_phi")]
    public List<InvoiceFeeWrapper<InvoiceFeeFields>>? hoadon68_phi { get; set; }

    /// <summary>
    /// Danh sách các trường thẻ khác bổ sung
    /// </summary>
    [JsonPropertyName("hoadon68_khac")]
    public List<InvoiceOtherWrapper<InvoiceOtherFields>>? hoadon68_khac { get; set; }
}

/// <summary>
/// Class chứa các trường đặc biệt cho Phiếu xuất kho hàng gửi bán đại lý (PXKDL)
/// Kế thừa từ NormalInvoiceHeaderFields và thêm các trường riêng biệt
/// </summary>
public class AgentSalesInvoiceFields : NormalInvoiceHeaderFields
{
    /// <summary>
    /// Địa chỉ kho xuất
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("dckhoxuat")]
    public string? dckhoxuat { get; set; }

    /// <summary>
    /// Tên người xuất hàng
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("hvtnxhang")]
    public string? hvtnxhang { get; set; }

    /// <summary>
    /// Hợp đồng vận chuyển
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("hdvc")]
    public string? hdvc { get; set; }

    /// <summary>
    /// Tên người vận chuyển
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("tnvchuyen")]
    public string? tnvchuyen { get; set; }

    /// <summary>
    /// Phương tiện vận chuyển
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("ptvchuyen")]
    public string? ptvchuyen { get; set; }

    /// <summary>
    /// Hợp đồng kinh tế số - Bắt buộc
    /// Kiểu: String, Bắt buộc: X
    /// </summary>
    [JsonPropertyName("hdktso")]
    public string hdktso { get; set; } = string.Empty;

    /// <summary>
    /// Ngày hợp đồng kinh tế - Bắt buộc
    /// Kiểu: Date, Bắt buộc: X
    /// </summary>
    [JsonPropertyName("hdktngay")]
    public string hdktngay { get; set; } = string.Empty;

    /// <summary>
    /// Danh sách chi tiết hóa đơn
    /// </summary>
    [JsonPropertyName("details")]
    public List<InvoiceDetailWrapper<InvoiceDetailFields>> details { get; set; } = new();

    /// <summary>
    /// Danh sách các loại tiền phí/lệ phí
    /// </summary>
    [JsonPropertyName("hoadon68_phi")]
    public List<InvoiceFeeWrapper<InvoiceFeeFields>>? hoadon68_phi { get; set; }

    /// <summary>
    /// Danh sách các trường thẻ khác bổ sung
    /// </summary>
    [JsonPropertyName("hoadon68_khac")]
    public List<InvoiceOtherWrapper<InvoiceOtherFields>>? hoadon68_khac { get; set; }
}

/// <summary>
/// Class chứa các trường đặc biệt cho Hóa đơn khác (Tem, vé, thẻ, phiếu…)
/// Kế thừa từ NormalInvoiceHeaderFields và thêm các trường riêng biệt
/// </summary>
public class OtherInvoiceFields : NormalInvoiceHeaderFields
{
    /// <summary>
    /// Tổng giảm trừ không chịu thuế (Giống như GTGT)
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("TGTKCThue")]
    public decimal? TGTKCThue { get; set; }

    /// <summary>
    /// Tổng giảm trừ khác - Bắt buộc nếu có (Giống như GTGT)
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("TGTKhac")]
    public decimal? TGTKhac { get; set; }

    /// <summary>
    /// Trạng thái Hóa đơn - Giới hạn cho Hóa đơn khác
    /// 0: "Gốc", 2: "Thay thế", 19: "Điều chỉnh tăng", 21: "Điều chỉnh giảm", 23: "Điều chỉnh thông tin"
    /// Kiểu: Number, Bắt buộc: X
    /// </summary>
    [JsonPropertyName("tthdon")]
    public new decimal? tthdon { get; set; }

    /// <summary>
    /// Danh sách chi tiết hóa đơn (có thể chứa hàng hóa đặc trưng giống GTGT)
    /// </summary>
    [JsonPropertyName("details")]
    public List<InvoiceDetailWrapper<InvoiceDetailFields>> details { get; set; } = new();

    /// <summary>
    /// Danh sách các loại tiền phí/lệ phí
    /// </summary>
    [JsonPropertyName("hoadon68_phi")]
    public List<InvoiceFeeWrapper<InvoiceFeeFields>>? hoadon68_phi { get; set; }

    /// <summary>
    /// Danh sách các trường thẻ khác bổ sung
    /// </summary>
    [JsonPropertyName("hoadon68_khac")]
    public List<InvoiceOtherWrapper<InvoiceOtherFields>>? hoadon68_khac { get; set; }
}
