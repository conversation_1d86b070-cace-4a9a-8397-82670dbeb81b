using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.SendInvoiceToCQT68.Raws;

/// <summary>
/// Response DTO cho API 4.6 Gửi hóa đơn đã ký lên Cơ quan thuế
/// Đ<PERSON>y là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class SendInvoiceToCQT68Response
{
    /// <summary>
    /// Trạng thái gửi hóa đơn
    /// Giá trị: "Đã gửi hóa đơn tới Cơ quan thuế"
    /// </summary>
    [JsonPropertyName("trang_thai")]
    public string? trang_thai { get; set; }

    /// <summary>
    /// Trạng thái thành công
    /// </summary>
    [JsonPropertyName("ok")]
    public bool ok { get; set; }

    /// <summary>
    /// Thông báo lỗi (nếu có)
    /// C<PERSON><PERSON> lỗi có thể xảy ra:
    /// - "Tồn tại hóa đơn không ở trạng thái chờ ký"
    /// - "Có hóa đơn đã được gửi tới CQT"
    /// </summary>
    [JsonPropertyName("error")]
    public string? error { get; set; }
}
