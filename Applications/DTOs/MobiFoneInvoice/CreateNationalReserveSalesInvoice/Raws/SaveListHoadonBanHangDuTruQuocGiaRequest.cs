using System.Text.Json.Serialization;
using Applications.DTOs.MobiFoneInvoice.Common;

namespace Applications.DTOs.MobiFoneInvoice.CreateNationalReserveSalesInvoice.Raws;

/// <summary>
/// Request để tạo mới Hóa đơn Bán hàng dự trữ quốc gia (Raw DTO theo chuẩn MobiFone)
/// </summary>
public class SaveListHoadonBanHangDuTruQuocGiaRequest
{
    /// <summary>
    /// Chế độ chỉnh sửa
    /// 0: Tạo mới, 1: Sửa, 2: Xóa
    /// </summary>
    [JsonPropertyName("editmode")]
    public int editmode { get; set; }

    /// <summary>
    /// Mảng dữ liệu chứa thông tin hóa đơn bán hàng dự trữ quốc gia
    /// </summary>
    [JsonPropertyName("data")]
    public List<NationalReserveSalesInvoiceFields> data { get; set; } = [];
}
