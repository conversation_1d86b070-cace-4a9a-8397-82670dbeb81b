using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.DeleteUnsignedInvoice.Raws;

/// <summary>
/// Response DTO cho API 4.11 Xóa hóa đơn chưa ký gửi
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class DeleteUnsignedInvoiceResponse
{
    /// <summary>
    /// Trạng thái thành công
    /// Kiểu: Boolean
    /// Giá trị: true khi thành công
    /// </summary>
    [JsonPropertyName("ok")]
    public bool ok { get; set; }
}

/// <summary>
/// Response wrapper cho trường hợp có lỗi
/// </summary>
public class DeleteUnsignedInvoiceErrorResponse
{
    /// <summary>
    /// Thông báo lỗi
    /// Kiểu: String
    /// Các lỗi có thể xảy ra:
    /// - "<PERSON>ày văn bản không được nhỏ hơn ngày hóa đơn": Lỗi này xảy ra khi ngày văn bản xóa bỏ nhỏ hơn ngày hóa đơn
    /// - "Không tìm thấy hóa đơn": Thông báo khi không tìm thấy hóa đơn với ID đã cung cấp
    /// - "Json không đúng định dạng !": Lỗi này xảy ra khi JSON Input truyền lên API không đúng định dạng mẫu
    /// </summary>
    [JsonPropertyName("error")]
    public string? error { get; set; }

    /// <summary>
    /// Thông báo lỗi (dạng Message)
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("Message")]
    public string? Message { get; set; }
}
