using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.DeleteUnsignedInvoice.Raws;

/// <summary>
/// Request DTO cho API 4.11 Xóa hóa đơn chưa ký gửi
/// URL: {{base_url}}/api/Invoice68/hoadonXoaNhieu
/// Method: POST
/// ContentType: application/json
/// Authorization: Bear Token;ma_dvcs
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class DeleteUnsignedInvoiceRequest
{
    /// <summary>
    /// Chế độ chỉnh sửa
    /// Kiểu: String
    /// Bắt buộc: X
    /// Mô tả: Chế độ chỉnh sửa
    /// Ghi chú: Giá trị cố định là "3" (Xóa hóa đơn)
    /// </summary>
    [JsonPropertyName("editmode")]
    public int editmode { get; set; } = 3;

    /// <summary>
    /// Mảng dữ liệu chứa thông tin hóa đơn cần xóa
    /// Kiểu: Array
    /// Bắt buộc: X
    /// </summary>
    [JsonPropertyName("data")]
    public List<DeleteUnsignedInvoiceData> data { get; set; } = new();
}

/// <summary>
/// Dữ liệu hóa đơn cần xóa
/// </summary>
public class DeleteUnsignedInvoiceData
{
    /// <summary>
    /// ID của hóa đơn cần xóa
    /// Kiểu: String
    /// Bắt buộc: X
    /// Mô tả: ID của hóa đơn cần xóa
    /// Ví dụ: "67a2d333-9d92-42c5-a924-80c32f03c1c1"
    /// </summary>
    [JsonPropertyName("hdon_id")]
    public string hdon_id { get; set; } = string.Empty;

    /// <summary>
    /// ID của dải ký hiệu hóa đơn
    /// Kiểu: String
    /// Bắt buộc: X
    /// Mô tả: Là ID của dải ký hiệu hóa đơn
    /// Ghi chú: Lấy theo trường qlkhsdung_id của API 1.b
    /// </summary>
    [JsonPropertyName("cctbao_id")]
    public string? cctbao_id { get; set; }
}
