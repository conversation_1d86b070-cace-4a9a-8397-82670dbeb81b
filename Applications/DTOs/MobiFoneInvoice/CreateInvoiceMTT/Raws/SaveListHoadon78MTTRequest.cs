using System.Text.Json.Serialization;
using Applications.DTOs.MobiFoneInvoice.Common;

namespace Applications.DTOs.MobiFoneInvoice.CreateInvoiceMTT.Raws;

/// <summary>
/// Request DTO cho API tạo mới hóa đơn máy tính tiền sinh mã (SaveListHoadon78MTT)
/// Đ<PERSON>y là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class SaveListHoadon78MTTRequest
{
    /// <summary>
    /// Chế độ chỉnh sửa
    /// 1: <PERSON><PERSON><PERSON> mới, 2: <PERSON><PERSON><PERSON>, 3: <PERSON><PERSON><PERSON> hóa đơn
    /// </summary>
    [JsonPropertyName("editmode")]
    public int editmode { get; set; }

    /// <summary>
    /// Mảng dữ liệu chứa thông tin hóa đơn GTGT
    /// </summary>
    [JsonPropertyName("data")]
    public List<GTGTInvoiceFields> data { get; set; } = new();
}
