using System.Text.Json.Serialization;
using Applications.DTOs.MobiFoneInvoice.Common;

namespace Applications.DTOs.MobiFoneInvoice.CreatePXKDLInvoice.Raws;

/// <summary>
/// Request DTO cho API tạo mới Phiếu xuất kho hàng gửi bán đại lý (PXKDL)
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class SaveListHoadonPXKDLRequest
{
    /// <summary>
    /// Chế độ chỉnh sửa
    /// 1: <PERSON>ạ<PERSON> mới, 2: <PERSON><PERSON><PERSON>, 3: <PERSON><PERSON>a hóa đơn
    /// </summary>
    [JsonPropertyName("editmode")]
    public int editmode { get; set; }

    /// <summary>
    /// Mảng dữ liệu chứa thông tin phiếu xuất kho hàng gửi bán đại lý
    /// </summary>
    [JsonPropertyName("data")]
    public List<PXKDLInvoiceFields> data { get; set; } = [];
}
