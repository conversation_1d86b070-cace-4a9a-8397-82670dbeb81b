using System.Text.Json.Serialization;
using Applications.DTOs.MobiFoneInvoice.Common;

namespace Applications.DTOs.MobiFoneInvoice.CreatePublicAssetSalesInvoice.Raws;

/// <summary>
/// Request để tạo mới Hóa đơn Bán tài sản công (Raw DTO theo chuẩn MobiFone)
/// </summary>
public class SaveListHoadonBanTaiSanCongRequest
{
    /// <summary>
    /// Chế độ chỉnh sửa
    /// 0: Tạo mới, 1: Sửa, 2: Xóa
    /// </summary>
    [JsonPropertyName("editmode")]
    public int editmode { get; set; }

    /// <summary>
    /// Mảng dữ liệu chứa thông tin hóa đơn bán tài sản công
    /// </summary>
    [JsonPropertyName("data")]
    public List<PublicAssetSalesInvoiceFields> data { get; set; } = [];
}
