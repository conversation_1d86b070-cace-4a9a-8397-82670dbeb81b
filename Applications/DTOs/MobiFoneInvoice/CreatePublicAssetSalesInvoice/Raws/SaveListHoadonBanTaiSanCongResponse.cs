using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.CreatePublicAssetSalesInvoice.Raws;

/// <summary>
/// Response từ API tạo mới Hóa đơn Bán tài sản công (Raw DTO theo chuẩn MobiFone)
/// </summary>
public class SaveListHoadonBanTaiSanCongResponse
{
    /// <summary>
    /// Dữ liệu trả về từ API
    /// </summary>
    [JsonPropertyName("data")]
    public List<PublicAssetSalesInvoiceResponseData> data { get; set; } = [];

    /// <summary>
    /// Trạng thái thành công
    /// </summary>
    [JsonPropertyName("ok")]
    public string ok { get; set; } = string.Empty;
}

/// <summary>
/// Dữ liệu chi tiết response cho Hóa đơn Bán tài sản công
/// </summary>
public class PublicAssetSalesInvoiceResponseData
{
    /// <summary>
    /// ID hóa đơn được tạo
    /// </summary>
    [JsonPropertyName("hdon_id")]
    public string hdon_id { get; set; } = string.Empty;

    /// <summary>
    /// Ký hiệu hóa đơn
    /// </summary>
    [JsonPropertyName("khieu")]
    public string khieu { get; set; } = string.Empty;

    /// <summary>
    /// Số hóa đơn
    /// </summary>
    [JsonPropertyName("shdon")]
    public decimal shdon { get; set; }

    /// <summary>
    /// Ngày lập hóa đơn
    /// </summary>
    [JsonPropertyName("nlap")]
    public string nlap { get; set; } = string.Empty;

    /// <summary>
    /// Mã tra cứu hóa đơn
    /// </summary>
    [JsonPropertyName("sbmat")]
    public string sbmat { get; set; } = string.Empty;

    /// <summary>
    /// Trạng thái hóa đơn
    /// </summary>
    [JsonPropertyName("tthai")]
    public string tthai { get; set; } = string.Empty;

    /// <summary>
    /// Tổng tiền hóa đơn
    /// </summary>
    [JsonPropertyName("tgtttbso")]
    public decimal tgtttbso { get; set; }

    /// <summary>
    /// Thông tin lỗi (nếu có)
    /// </summary>
    [JsonPropertyName("error_status")]
    public string? error_status { get; set; }
}
