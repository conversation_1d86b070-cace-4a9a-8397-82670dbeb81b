using System.Text.Json.Serialization;
using Applications.DTOs.MobiFoneInvoice.Common;

namespace Applications.DTOs.MobiFoneInvoice.CreateOtherInvoiceMTT.Raws;

/// <summary>
/// Request DTO cho API tạo mới Hóa đơn khác (Tem, vé, thẻ, phiếu...) máy tính tiền sinh mã (SaveListHoadon78MTT)
/// Đây là DTO raw theo chuẩn tài liệu <PERSON>, giữ nguyên tên field
/// </summary>
public class SaveListHoaDonKhacMTTRequest
{
    /// <summary>
    /// Chế độ chỉnh sửa
    /// 1: <PERSON><PERSON><PERSON> mới, 2: <PERSON><PERSON><PERSON>, 3: <PERSON><PERSON><PERSON> hó<PERSON> đơn
    /// </summary>
    [JsonPropertyName("editmode")]
    public int editmode { get; set; }

    /// <summary>
    /// Mảng dữ liệu chứa thông tin hóa đơn khác
    /// </summary>
    [JsonPropertyName("data")]
    public List<OtherInvoiceFields> data { get; set; } = [];
}
