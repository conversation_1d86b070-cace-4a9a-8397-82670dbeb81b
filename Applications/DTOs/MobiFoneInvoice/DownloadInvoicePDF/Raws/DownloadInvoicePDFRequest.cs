using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.DownloadInvoicePDF.Raws;

/// <summary>
/// Request DTO cho API 4.9 Tải hóa đơn File .PDF
/// URL: {{base_url}}/api/Invoice68/inHoadon?id={hdon_id}&type=PDF&inchuyendoi=false
/// Method: GET
/// ContentType: application/json
/// Authorization: Bear Token;ma_dvcs
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class DownloadInvoicePDFRequest
{
    /// <summary>
    /// ID của hóa đơn cần tải về máy
    /// Kiểu: GUID
    /// Độ dài: 36 ký tự
    /// Bắt buộc: X
    /// Mô tả: Là trường khóa chính của hóa đơn
    /// Ví dụ: "b6036140-9572-4b2d-a8b8-6c75f5cc76cb"
    /// </summary>
    [JsonPropertyName("id")]
    public string id { get; set; } = string.Empty;

    /// <summary>
    /// Định dạng file của hóa đơn muốn tải về
    /// Kiểu: String
    /// Bắt buộc: X
    /// Mô tả: Định dạng file của hóa đơn muốn tải về
    /// Ghi chú: Giá trị cố định là "PDF"
    /// </summary>
    [JsonPropertyName("type")]
    public string type { get; set; } = "PDF";

    /// <summary>
    /// Tùy chọn in thể hiện hay in chuyển đổi
    /// Kiểu: Boolean
    /// Bắt buộc: X
    /// Mô tả: Tùy chọn in thể hiện hay in chuyển đổi
    /// Ghi chú: true: In chuyển đổi, false: In thể hiện
    /// Ví dụ: false
    /// </summary>
    [JsonPropertyName("inchuyendoi")]
    public bool inchuyendoi { get; set; } = false;
}
