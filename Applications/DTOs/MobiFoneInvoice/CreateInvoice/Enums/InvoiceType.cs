namespace Applications.DTOs.MobiFoneInvoice.CreateInvoice.Enums;

/// <summary>
/// Enum định nghĩa các loại hóa đơn trong hệ thống MobiFone Invoice
/// </summary>
public enum InvoiceType
{
    /// <summary>
    /// Hóa đơn Giá trị gia tăng (GTGT)
    /// - <PERSON><PERSON> trường TGTKCThue, TGTKhac
    /// - Hỗ trợ hàng hóa đặc trưng (xe ô tô, vận chuyển, TMĐT)
    /// </summary>
    GTGT = 1,

    /// <summary>
    /// Hóa đơn Bán hàng
    /// - <PERSON><PERSON> trường hddckptquan, giamthuebanhang20, tienthuegtgtgiam
    /// - tthdon giới hạn: 0, 2, 19, 21, 23
    /// - tsuat không bắt buộc
    /// </summary>
    BanHang = 2,

    /// <summary>
    /// Hóa đơn Bán tài sản công
    /// - <PERSON><PERSON> các trường đặc biệt: mdvqhnsach_ban, fax, website, sqdbants, nqdbants, cqqdbants, htbants
    /// - Có thông tin vận chuyển: ddvchden, tgvchdtu, tgvchdden
    /// </summary>
    BanTaiSanCong = 3,

    /// <summary>
    /// Hóa đơn Bán hàng dự trữ quốc gia
    /// - Có trường cmndmua, mdvqhnsach_mua, shchieu
    /// </summary>
    BanHangDuTruQuocGia = 4,

    /// <summary>
    /// Phiếu xuất kho kiêm vận chuyển nội bộ (PXKVCNB)
    /// - Có các trường: lddnbo, dckhoxuat, hvtnxhang, hdvc, tnvchuyen, ptvchuyen
    /// </summary>
    PhieuXuatKhoVanChuyenNoiBo = 5,

    /// <summary>
    /// Phiếu xuất kho hàng gửi bán đại lý (PXKDL)
    /// - Có các trường: dckhoxuat, hvtnxhang, hdvc, tnvchuyen, ptvchuyen
    /// - Bắt buộc: hdktso, hdktngay
    /// </summary>
    PhieuXuatKhoHangGuiBanDaiLy = 6,

    /// <summary>
    /// Hóa đơn khác (Tem, vé, thẻ, phiếu…)
    /// - Tương tự GTGT: có TGTKCThue, TGTKhac
    /// - tthdon giới hạn: 0, 2, 19, 21, 23
    /// - Hỗ trợ hàng hóa đặc trưng
    /// </summary>
    HoaDonKhac = 7
}
