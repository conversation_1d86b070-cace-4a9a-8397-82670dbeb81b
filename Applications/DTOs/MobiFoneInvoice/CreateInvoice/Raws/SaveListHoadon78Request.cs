using System.Text.Json.Serialization;
using Applications.DTOs.MobiFoneInvoice.Common;

namespace Applications.DTOs.MobiFoneInvoice.CreateInvoice.Raws;

/// <summary>
/// Request DTO cho API tạo mới hóa đơn quy trình thư<PERSON> (SaveListHoadon78)
/// Đ<PERSON>y là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field như ma_Dvs
/// </summary>
public class SaveListHoadon78Request
{
    /// <summary>
    /// Chế độ chỉnh sửa
    /// 1: T<PERSON><PERSON> mới, 2: <PERSON><PERSON><PERSON>, 3: <PERSON><PERSON><PERSON> hóa đơn
    /// </summary>
    [JsonPropertyName("editmode")]
    public int editmode { get; set; }

    /// <summary>
    /// Mảng dữ liệu chứa thông tin hóa đơn GTGT
    /// </summary>
    [JsonPropertyName("data")]
    public List<GTGTInvoiceFields> data { get; set; } = new();
}


