namespace Applications.DTOs.MobiFoneInvoice.CreateInvoice.Raws;

/// <summary>
/// Response DTO cho API tạo mới hóa đơn quy trình th<PERSON> (SaveListHoadon78)
/// <PERSON><PERSON><PERSON> là DTO raw theo chuẩn tài l<PERSON>, gi<PERSON> nguyên tên field
/// </summary>

/// <summary>
/// Dữ liệu chi tiết response cho API tạo hóa đơn
/// </summary>
public class SaveListHoadon78Response
{
    public Data data { get; set; }
    public string ok { get; set; }
}

public class Data
{
    public string id { get; set; }
    public string hdon_id { get; set; }
    public string cctbao_id { get; set; }
    public object hdlket_id { get; set; }
    public string tthai { get; set; }
    public object tthdon { get; set; }
    public string khieu { get; set; }
    public string shdon { get; set; }
    public string tdlap { get; set; }
    public string dvtte { get; set; }
    public double tgia { get; set; }
    public object gchu { get; set; }
    public string tnmua { get; set; }
    public string mnmua { get; set; }
    public string ten { get; set; }
    public string mst { get; set; }
    public string dchi { get; set; }
    public string email { get; set; }
    public string sdtnmua { get; set; }
    public string stknmua { get; set; }
    public string htttoan { get; set; }
    public string stknban { get; set; }
    public string sbmat { get; set; }
    public string mdvi { get; set; }
    public string nglap { get; set; }
    public string nlap { get; set; }
    public object ngsua { get; set; }
    public object nsua { get; set; }
    public double tgtcthue { get; set; }
    public double tgtthue { get; set; }
    public object ttcktmai { get; set; }
    public double tgtttbso { get; set; }
    public string tgtttbchu { get; set; }
    public object dlqrcode { get; set; }
    public string sdhang { get; set; }
    public object shdon1 { get; set; }
    public object mccqthue { get; set; }
    public object ngky { get; set; }
    public object nky { get; set; }
    public object signature { get; set; }
    public object hthdbtthe { get; set; }
    public object tdlhdbtthe { get; set; }
    public object khmshdbtthe { get; set; }
    public object khhdbtthe { get; set; }
    public object shdbtthe { get; set; }
    public double tgtphi { get; set; }
    public object tgtcthue0 { get; set; }
    public object tgtthue0 { get; set; }
    public object ttcktmai0 { get; set; }
    public object tgtttbso0 { get; set; }
    public object tgtcthue5 { get; set; }
    public object tgtthue5 { get; set; }
    public object ttcktmai5 { get; set; }
    public object tgtttbso5 { get; set; }
    public object tgtcthue10 { get; set; }
    public object tgtthue10 { get; set; }
    public object ttcktmai10 { get; set; }
    public object tgtttbso10 { get; set; }
    public object tgtcthuekct { get; set; }
    public object tgtthuekct { get; set; }
    public object ttcktmaikct { get; set; }
    public object tgtttbsokct { get; set; }
    public object tgtcthuekkk { get; set; }
    public object tgtthuekkk { get; set; }
    public object ttcktmaikkk { get; set; }
    public object tgtttbsokkk { get; set; }
    public object tgtphi0 { get; set; }
    public object tgtphi5 { get; set; }
    public object tgtphi10 { get; set; }
    public object tgtphikct { get; set; }
    public object tgtphikkk { get; set; }
    public object lhdon { get; set; }
    public object lddnbo { get; set; }
    public object tnvchuyen { get; set; }
    public object ptvchuyen { get; set; }
    public object dckhoxuat { get; set; }
    public object dckhonhap { get; set; }
    public object tennguoinhanhang { get; set; }
    public object mstnguoinhanhang { get; set; }
    public object phongban { get; set; }
    public object veviec { get; set; }
    public object sohopdong { get; set; }
    public object hdon68_id_lk { get; set; }
    public object mtdiep_cqt { get; set; }
    public object mtdiep_gui { get; set; }
    public object tthdon_old { get; set; }
    public object hdon_id_old { get; set; }
    public int is_hdcma { get; set; }
    public object hdon_ghichu { get; set; }
    public int tthdon_original { get; set; }
    public double kygui_cqt { get; set; }
    public object hdktngay { get; set; }
    public string tnhban { get; set; }
    public string tnhmua { get; set; }
    public object hddckptquan { get; set; }
    public object sbke { get; set; }
    public object faxban { get; set; }
    public object webban { get; set; }
    public object sqdbants { get; set; }
    public object nqdbants { get; set; }
    public object cqqdbants { get; set; }
    public object htbants { get; set; }
    public string cmndmua { get; set; }
    public object hdvc { get; set; }
    public object hvtnxhang { get; set; }
    public object hdktso { get; set; }
    public object nbke { get; set; }
    public object ddvchden { get; set; }
    public object tgvchdtu { get; set; }
    public object tgvchdden { get; set; }
    public object sdtban { get; set; }
    public double tkcktmn { get; set; }
    public double tgtttbso_last { get; set; }
    public string mdvqhnsach_mua { get; set; }
    public object mdvqhnsach_ban { get; set; }
    public object stbao { get; set; }
    public object ntbao { get; set; }
    public object kmai { get; set; }
    public object tgtcthuek { get; set; }
    public object tgtthuek { get; set; }
    public object ttcktmaik { get; set; }
    public object tgtttbsok { get; set; }
    public object error_status { get; set; }
    public bool issendmail { get; set; }
    public object docngoaitetv { get; set; }
    public object giamthuebanhang20 { get; set; }
    public object tienthuegtgtgiam { get; set; }
    public object lhdclquan { get; set; }
    public object khmshdclquan { get; set; }
    public object khhdclquan { get; set; }
    public object shdclquan { get; set; }
    public object nlhdclquan { get; set; }
    public object tiletienthuegtgtgiam { get; set; }
    public object tgtkhac { get; set; }
    public object thdon { get; set; }
    public object qtich { get; set; }
    public object cnctru { get; set; }
    public object ngccmnd { get; set; }
    public object nccmnd { get; set; }
    public object ktnhap { get; set; }
    public object thang { get; set; }
    public object nam { get; set; }
    public object msbl_tncn { get; set; }
    public object tlkthu { get; set; }
    public int loaihd { get; set; }
    public object cmnd { get; set; }
    public object dvtinh { get; set; }
    public object hdtndinh { get; set; }
    public object hdckhau { get; set; }
    public object kdbhbbuoc { get; set; }
    public object ktnhapstr { get; set; }
    public object cnkctru { get; set; }
    public object tudongtinhtien { get; set; }
    public object tgtkcthue { get; set; }
    public object tthang { get; set; }
    public object dthang { get; set; }
    public object ttthanhtoan { get; set; }
    public object orderid { get; set; }
    public int signing { get; set; }
    public string shchieu { get; set; }
    public string mchang { get; set; }
    public string tchang { get; set; }
    public object fkey { get; set; }
}


