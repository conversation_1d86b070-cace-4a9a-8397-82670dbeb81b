using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.SignInvoiceCertFile68.Raws;

/// <summary>
/// Request DTO cho API 4.5 Ký chờ xử lý hóa đơn (bằng file mềm, SIM)
/// URL: {{base_url}}/api/Invoice68/SignInvoiceCertFile68
/// Method: POST
/// ContentType: application/json
/// Authorization: Bear Token;ma_dvcs
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class SignInvoiceCertFile68Request
{
    /// <summary>
    /// Mảng dữ liệu chứa thông tin ký hóa đơn
    /// </summary>
    [JsonPropertyName("data")]
    public List<SignInvoiceData> data { get; set; } = new();
}

/// <summary>
/// Dữ liệu ký hóa đơn trong mảng data
/// </summary>
public class SignInvoiceData
{
    /// <summary>
    /// Mã đơn vị cơ sở
    /// Kiểu: String, Bắt buộc: X
    /// </summary>
    [JsonPropertyName("branch_code")]
    public string branch_code { get; set; } = string.Empty;

    /// <summary>
    /// Tên tài khoản
    /// Kiểu: String, Bắt buộc: X
    /// </summary>
    [JsonPropertyName("username")]
    public string username { get; set; } = string.Empty;

    /// <summary>
    /// List id hóa đơn
    /// Kiểu: Array String, Bắt buộc: X
    /// Mô tả: Danh sách ID hóa đơn cần ký
    /// </summary>
    [JsonPropertyName("lsthdon_id")]
    public List<string> lsthdon_id { get; set; } = new();

    /// <summary>
    /// Chữ ký số HSM MobiFone
    /// Kiểu: String, Bắt buộc: X
    /// Ghi chú: không cần truyền
    /// </summary>
    [JsonPropertyName("cer_serial")]
    public string? cer_serial { get; set; }

    /// <summary>
    /// Loại hóa đơn không mã hoặc có mã
    /// Kiểu: String, Bắt buộc: X
    /// Giá trị: 200: có mã, 203: không mã, 206: có mã từ MTT
    /// </summary>
    [JsonPropertyName("type_cmd")]
    public string type_cmd { get; set; } = string.Empty;

    /// <summary>
    /// Có gửi CQT?
    /// Kiểu: String
    /// Giá trị: Notsend: Không gửi
    /// </summary>
    [JsonPropertyName("guiCQT")]
    public string? guiCQT { get; set; }
}
