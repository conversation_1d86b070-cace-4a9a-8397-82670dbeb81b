using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.SignInvoiceCertFile68.Raws;

/// <summary>
/// Response DTO cho API 4.5 Ký chờ xử lý hóa đơn (bằng file mềm, SIM)
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class SignInvoiceCertFile68Response
{
    /// <summary>
    /// Trạng thái ký hóa đơn
    /// Giá trị: "Đã ký"
    /// </summary>
    [JsonPropertyName("tthai")]
    public string? tthai { get; set; }

    /// <summary>
    /// Trạng thái thành công
    /// </summary>
    [JsonPropertyName("ok")]
    public bool ok { get; set; }

    /// <summary>
    /// Thông báo lỗi (nếu có)
    /// Các lỗi có thể xảy ra:
    /// - "<PERSON><PERSON> không đúng định dạng !"
    /// - "Không tìm thấy chứng thư số"
    /// - "Tồn tại hóa đơn không ở trạng thái chờ ký"
    /// </summary>
    [JsonPropertyName("error")]
    public string? error { get; set; }
}
