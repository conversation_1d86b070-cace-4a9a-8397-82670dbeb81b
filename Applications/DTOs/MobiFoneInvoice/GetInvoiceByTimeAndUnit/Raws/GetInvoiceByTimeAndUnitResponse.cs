using System.Text.Json.Serialization;
using Applications.DTOs.MobiFoneInvoice.Common;

namespace Applications.DTOs.MobiFoneInvoice.GetInvoiceByTimeAndUnit.Raws;

/// <summary>
/// Response DTO cho API 4.17 L<PERSON><PERSON> danh sách hoá đơn theo thời gian, đơn vị và trạng thái
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// Response trả về là một mảng các đối tượng hóa đơn
/// </summary>
public class GetInvoiceByTimeAndUnitResponse : List<InvoiceListItem>
{
    // Response là một mảng trực tiếp, không có wrapper object
    // Kế thừa từ List<InvoiceListItem> để sử dụng DTO chung
}

/// <summary>
/// Response wrapper cho trường hợp có lỗi
/// </summary>
public class GetInvoiceByTimeAndUnitErrorResponse
{
    /// <summary>
    /// Thông báo lỗi
    /// Kiểu: String
    /// Ví dụ: "Dữ liệu tu_ngay/den_ngay không hợp lệ!"
    /// </summary>
    [JsonPropertyName("error")]
    public string error { get; set; } = string.Empty;
}
