using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.GetInvoiceByTimeAndUnit.Raws;

/// <summary>
/// Request DTO cho API 4.17 L<PERSON>y danh sách hoá đơn theo thời gian, đơn vị và trạng thái
/// URL: {{base_url}}/api/Invoice68/GetInvoiceByTimeAndUnit
/// Method: POST
/// ContentType: application/json
/// Authorization: Bear Token;ma_dvcs
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class GetInvoiceByTimeAndUnitRequest
{
    /// <summary>
    /// Từ ngày
    /// Kiểu: String
    /// Bắt buộc: X
    /// Mô tả: Ngày bắt đầu của khoảng thời gian tìm kiếm
    /// Định dạng: yyyy-MM-dd
    /// Ví dụ: "2021-12-01"
    /// </summary>
    [JsonPropertyName("tu_ngay")]
    public string tu_ngay { get; set; } = string.Empty;

    /// <summary>
    /// Đến ngày
    /// Kiểu: String
    /// Bắt buộc: X
    /// Mô tả: Ngày kết thúc của khoảng thời gian tìm kiếm
    /// Định dạng: yyyy-MM-dd
    /// Ví dụ: "2021-12-11"
    /// </summary>
    [JsonPropertyName("den_ngay")]
    public string den_ngay { get; set; } = string.Empty;
}
