using System.Text.Json.Serialization;
using Applications.DTOs.MobiFoneInvoice.Common;

namespace Applications.DTOs.MobiFoneInvoice.CreateOtherInvoice.Raws;

/// <summary>
/// Request để tạo mới Hóa đơn khác (Tem, vé, thẻ, phiếu...) (Raw DTO theo chuẩn MobiFone)
/// </summary>
public class SaveListHoaDonKhacRequest
{
    /// <summary>
    /// Chế độ chỉnh sửa
    /// 0: T<PERSON>o mới, 1: Sửa, 2: Xóa
    /// </summary>
    [JsonPropertyName("editmode")]
    public int editmode { get; set; }

    /// <summary>
    /// Mảng dữ liệu chứa thông tin hóa đơn khác
    /// </summary>
    [JsonPropertyName("data")]
    public List<OtherInvoiceFields> data { get; set; } = [];
}
