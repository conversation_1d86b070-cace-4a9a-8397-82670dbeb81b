using System.Text.Json.Serialization;
using Applications.DTOs.MobiFoneInvoice.Common;

namespace Applications.DTOs.MobiFoneInvoice.GetHoadonFkey.Raws;

/// <summary>
/// Response DTO cho API 4.14 L<PERSON>y danh sách hóa đơn theo FKEY hoặc Khoảng thời gian
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// Response trả về là một mảng các đối tượng hóa đơn
/// </summary>
public class GetHoadonFkeyResponse : List<InvoiceListItem>
{
    // Response là một mảng trực tiếp, không có wrapper object
    // Kế thừa từ List<InvoiceListItem> để sử dụng DTO chung
}

/// <summary>
/// Response wrapper cho trường hợp có lỗi
/// </summary>
public class GetHoadonFkeyErrorResponse
{
    /// <summary>
    /// Thông báo lỗi
    /// Kiểu: String
    /// Ví dụ: "Không có dữ liệu hóa đơn!", "Dữ liệu tu_ngay/den_ngay không hợp lệ!"
    /// </summary>
    [JsonPropertyName("error")]
    public string error { get; set; } = string.Empty;
}
