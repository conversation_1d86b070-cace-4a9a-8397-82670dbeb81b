using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.GetHoadonFkey.Raws;

/// <summary>
/// Request DTO cho API 4.14 Lấy danh sách hóa đơn theo FKEY hoặc Khoảng thời gian
/// URL: {{base_url}}/api/Invoice68/GetHoadonFkey
/// Method: POST
/// ContentType: application/json
/// Authorization: Bear Token;ma_dvcs
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class GetHoadonFkeyRequest
{
    /// <summary>
    /// Mã FKEY hóa đơn
    /// Kiểu: String
    /// Bắt buộc: X (Bắt buộc nếu tu_ngay và den_ngay trống)
    /// Mô tả: Nếu truyền giá trị này, hệ thống ưu tiên lấy hóa đơn theo FKEY
    /// Ví dụ: "MUINQ04"
    /// </summary>
    [JsonPropertyName("hdon_id")]
    public string? hdon_id { get; set; }

    /// <summary>
    /// Từ ngày
    /// Kiểu: String
    /// Bắt buộc: X (Bắt buộc nếu hdon_id trống)
    /// Mô tả: Ngày bắt đầu của khoảng thời gian tìm kiếm
    /// Định dạng: yyyy-MM-dd
    /// Ví dụ: "2021-12-01"
    /// </summary>
    [JsonPropertyName("tu_ngay")]
    public string? tu_ngay { get; set; }

    /// <summary>
    /// Đến ngày
    /// Kiểu: String
    /// Bắt buộc: X (Bắt buộc nếu hdon_id trống)
    /// Mô tả: Ngày kết thúc của khoảng thời gian tìm kiếm
    /// Định dạng: yyyy-MM-dd
    /// Ví dụ: "2021-12-11"
    /// </summary>
    [JsonPropertyName("den_ngay")]
    public string? den_ngay { get; set; }
}
