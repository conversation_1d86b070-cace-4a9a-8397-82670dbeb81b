using Applications.DTOs.MobiFoneInvoice.CreateInvoiceMTT.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để tạo mới Hóa đơn Giá trị gia tăng máy tính tiền sinh mã trong MobiFone Invoice API
/// </summary>
public record TaoMoiHoaDonGTGTMayTinhTienSinhMaCommand(
    SaveListHoadon78MTTRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<List<SaveListHoadon78MTTResponse>>>;

/// <summary>
/// Validator cho TaoMoiHoaDonGTGTMayTinhTienSinhMaCommand
/// </summary>
public class TaoMoiHoaDonGTGTMayTinhTienSinhMaCommandValidator : AbstractValidator<TaoMoiHoaDonGTGTMayTinhTienSinhMaCommand>
{
    public TaoMoiHoaDonGTGTMayTinhTienSinhMaCommandValidator()
    {
        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request không được null");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token là bắt buộc");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vị là bắt buộc");
    }
}

/// <summary>
/// Handler cho TaoMoiHoaDonGTGTMayTinhTienSinhMaCommand
/// </summary>
public class TaoMoiHoaDonGTGTMayTinhTienSinhMaCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<TaoMoiHoaDonGTGTMayTinhTienSinhMaCommand, Response<List<SaveListHoadon78MTTResponse>>>
{
    public async Task<Response<List<SaveListHoadon78MTTResponse>>> Handle(TaoMoiHoaDonGTGTMayTinhTienSinhMaCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.TaoMoiHoaDonGTGTMayTinhTienSinhMaAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
