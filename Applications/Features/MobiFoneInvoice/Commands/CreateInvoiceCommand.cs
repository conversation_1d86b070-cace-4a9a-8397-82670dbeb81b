using Applications.DTOs.MobiFoneInvoice.CreateInvoice.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để tạo mới hóa đơn quy trình thường trong MobiFone Invoice API
/// </summary>
public record CreateInvoiceCommand(
    SaveListHoadon78Request Request,
    string Token,
    string MaDvcs) : IRequest<Response<List<SaveListHoadon78Response>>>;

// public class ValidateCreateInvoiceCommand : AbstractValidator<CreateInvoiceCommand>
// {
//     public ValidateCreateInvoiceCommand()
//     {
//         RuleFor(x => x.Request)
//             .NotNull()
//             .WithMessage("Request không được null");
//
//         RuleFor(x => x.Request.editmode)
//             .InclusiveBetween(1, 3)
//             .WithMessage("editmode phải là 1 (<PERSON><PERSON><PERSON> mớ<PERSON>), 2 (<PERSON><PERSON><PERSON>), hoặc 3 (<PERSON><PERSON><PERSON>)");
//
//         RuleFor(x => x.Request.data)
//             .NotEmpty()
//             .WithMessage("data không được trống");
//
//         RuleFor(x => x.Token)
//             .NotEmpty()
//             .WithMessage("Token là bắt buộc");
//
//         RuleFor(x => x.MaDvcs)
//             .NotEmpty()
//             .WithMessage("Mã đơn vị là bắt buộc");
//     }
// }

/// <summary>
/// Handler cho CreateInvoiceCommand
/// </summary>
public class CreateInvoiceCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<CreateInvoiceCommand, Response<List<SaveListHoadon78Response>>>
{
    public async Task<Response<List<SaveListHoadon78Response>>> Handle(CreateInvoiceCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.CreateInvoiceAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
