using Applications.DTOs.MobiFoneInvoice.CreateOtherInvoice.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để tạo mới Hóa đơn khác (Tem, vé, thẻ, phiếu...) quy trình thường trong MobiFone Invoice API
/// </summary>
public record TaoMoiHoaDonKhacQuyTrinhThuongCommand(
    SaveListHoaDonKhacRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<List<SaveListHoaDonKhacResponse>>>;

/// <summary>
/// Handler cho TaoMoiHoaDonKhacQuyTrinhThuongCommand
/// </summary>
public class TaoMoiHoaDonKhacQuyTrinhThuongCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<TaoMoiHoaDonKhacQuyTrinhThuongCommand, Response<List<SaveListHoaDonKhacResponse>>>
{
    public async Task<Response<List<SaveListHoaDonKhacResponse>>> Handle(TaoMoiHoaDonKhacQuyTrinhThuongCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.TaoMoiHoaDonKhacQuyTrinhThuongAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
