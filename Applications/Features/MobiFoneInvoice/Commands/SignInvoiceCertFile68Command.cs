using Applications.DTOs.MobiFoneInvoice.SignInvoiceCertFile68.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để ký chờ xử lý hóa đơn (bằng file mềm, SIM) trong MobiFone Invoice API
/// API 4.5: SignInvoiceCertFile68
/// </summary>
public record SignInvoiceCertFile68Command(
    SignInvoiceCertFile68Request Request,
    string Token,
    string MaDvcs) : IRequest<Response<SignInvoiceCertFile68Response>>;

/// <summary>
/// Validator cho SignInvoiceCertFile68Command
/// </summary>
public class SignInvoiceCertFile68CommandValidator : AbstractValidator<SignInvoiceCertFile68Command>
{
    public SignInvoiceCertFile68CommandValidator()
    {
        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request không được null");

        RuleFor(x => x.Request.data)
            .NotNull()
            .NotEmpty()
            .WithMessage("data không được null hoặc rỗng");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token là bắt buộc");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vị là bắt buộc");

        // Validate từng item trong data array
        RuleForEach(x => x.Request.data).ChildRules(signData =>
        {
            signData.RuleFor(x => x.branch_code)
                .NotEmpty()
                .WithMessage("branch_code (Mã đơn vị cơ sở) là bắt buộc");

            signData.RuleFor(x => x.username)
                .NotEmpty()
                .WithMessage("username (Tên tài khoản) là bắt buộc");

            signData.RuleFor(x => x.lsthdon_id)
                .NotNull()
                .NotEmpty()
                .WithMessage("lsthdon_id (List id hóa đơn) không được null hoặc rỗng");

            signData.RuleFor(x => x.type_cmd)
                .NotEmpty()
                .WithMessage("type_cmd là bắt buộc")
                .Must(x => x == "200" || x == "203" || x == "206")
                .WithMessage("type_cmd phải là 200 (có mã), 203 (không mã), hoặc 206 (có mã từ MTT)");
        });
    }
}

/// <summary>
/// Handler cho SignInvoiceCertFile68Command
/// </summary>
public class SignInvoiceCertFile68CommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<SignInvoiceCertFile68Command, Response<SignInvoiceCertFile68Response>>
{
    public async Task<Response<SignInvoiceCertFile68Response>> Handle(SignInvoiceCertFile68Command request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.SignInvoiceCertFile68Async(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
