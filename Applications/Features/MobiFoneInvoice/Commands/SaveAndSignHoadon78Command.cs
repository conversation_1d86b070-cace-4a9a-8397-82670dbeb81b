using Applications.DTOs.MobiFoneInvoice.SaveAndSignHoadon78.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để tạo mới và ký gửi hóa đơn bằng HSM nhà cung cấp khác, file mềm, Sim PKI trong MobiFone Invoice API
/// API 4.4: SaveAndSignHoadon78
/// </summary>
public record SaveAndSignHoadon78Command(
    SaveAndSignHoadon78Request Request,
    string Token,
    string MaDvcs) : IRequest<Response<List<SaveAndSignHoadon78Response>>>;

/// <summary>
/// Validator cho SaveAndSignHoadon78Command
/// </summary>
public class SaveAndSignHoadon78CommandValidator : AbstractValidator<SaveAndSignHoadon78Command>
{
    public SaveAndSignHoadon78CommandValidator()
    {
        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request không được null");

        RuleFor(x => x.Request.editmode)
            .Must(x => x == 1 || x == 2 || x == 3)
            .WithMessage("editmode phải là 1 (Tạo mới), 2 (Sửa), hoặc 3 (Xóa hóa đơn)");

        RuleFor(x => x.Request.type_cmd)
            .NotEmpty()
            .WithMessage("type_cmd là bắt buộc")
            .Must(x => x == "200" || x == "203" || x == "206")
            .WithMessage("type_cmd phải là 200 (Có mã), 203 (Không mã), hoặc 206 (Có mã từ MTT)");

        RuleFor(x => x.Request.guiCQT)
            .NotEmpty()
            .WithMessage("guiCQT là bắt buộc");

        RuleFor(x => x.Request.is_api)
            .NotEmpty()
            .WithMessage("is_api là bắt buộc")
            .Equal("1")
            .WithMessage("is_api phải là '1'");

        RuleFor(x => x.Request.data)
            .NotNull()
            .NotEmpty()
            .WithMessage("data không được null hoặc rỗng");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token là bắt buộc");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vị là bắt buộc");

        // Validate từng item trong data array
        RuleForEach(x => x.Request.data).ChildRules(invoice =>
        {
            invoice.RuleFor(x => x.cctbao_id)
                .NotEmpty()
                .WithMessage("cctbao_id là bắt buộc");

            invoice.RuleFor(x => x.nlap)
                .NotEmpty()
                .WithMessage("nlap (Ngày hóa đơn) là bắt buộc");

            invoice.RuleFor(x => x.dvtte)
                .NotEmpty()
                .WithMessage("dvtte (Mã ngoại tệ) là bắt buộc");

            invoice.RuleFor(x => x.htttoan)
                .NotEmpty()
                .WithMessage("htttoan (Phương thức thanh toán) là bắt buộc");

            invoice.RuleFor(x => x.mdvi)
                .NotEmpty()
                .WithMessage("mdvi (Mã đơn vị tạo hóa đơn) là bắt buộc");

            invoice.RuleFor(x => x.details)
                .NotNull()
                .NotEmpty()
                .WithMessage("details (Chi tiết hóa đơn) không được null hoặc rỗng");
        });
    }
}

/// <summary>
/// Handler cho SaveAndSignHoadon78Command
/// </summary>
public class SaveAndSignHoadon78CommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<SaveAndSignHoadon78Command, Response<List<SaveAndSignHoadon78Response>>>
{
    public async Task<Response<List<SaveAndSignHoadon78Response>>> Handle(SaveAndSignHoadon78Command request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.SaveAndSignHoadon78Async(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
