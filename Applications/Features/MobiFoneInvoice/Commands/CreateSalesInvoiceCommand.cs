using Applications.DTOs.MobiFoneInvoice.CreateSalesInvoice.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để tạo mới Hóa đơn Bán hàng trong MobiFone Invoice API
/// </summary>
public record CreateSalesInvoiceCommand(
    SaveListHoadonBanHangRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<List<SaveListHoadonBanHangResponse>>>;

/// <summary>
/// Validator cho CreateSalesInvoiceCommand
/// </summary>
public class ValidateCreateSalesInvoiceCommand : AbstractValidator<CreateSalesInvoiceCommand>
{
    public ValidateCreateSalesInvoiceCommand()
    {
        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request không được null");

        RuleFor(x => x.Request.editmode)
            .InclusiveBetween(1, 3)
            .WithMessage("editmode phải là 1 (<PERSON>ạ<PERSON> mới), 2 (<PERSON><PERSON><PERSON>), hoặc 3 (Xóa)");

        RuleFor(x => x.Request.data)
            .NotEmpty()
            .WithMessage("data không được trống");

        // Validation cho từng item trong data
        RuleForEach(x => x.Request.data).ChildRules(data =>
        {
            data.RuleFor(x => x.cctbao_id)
                .NotEmpty()
                .WithMessage("cctbao_id là bắt buộc");

            data.RuleFor(x => x.nlap)
                .NotEmpty()
                .WithMessage("nlap (ngày hóa đơn) là bắt buộc");

            data.RuleFor(x => x.htttoan)
                .NotEmpty()
                .Must(x => new[] { "Tiền mặt", "Chuyển khoản", "Tiền mặt/Chuyển khoản" }.Contains(x))
                .WithMessage("htttoan phải là 'Tiền mặt', 'Chuyển khoản', hoặc 'Tiền mặt/Chuyển khoản'");

            data.RuleFor(x => x.mdvi)
                .NotEmpty()
                .WithMessage("mdvi (mã đơn vị) là bắt buộc");

            data.RuleFor(x => x.is_hdcma)
                .InclusiveBetween(0, 1)
                .WithMessage("is_hdcma phải là 0 (Không mã) hoặc 1 (Có mã)");

            // Validation cho tthdon - giới hạn cho Hóa đơn Bán hàng
            data.RuleFor(x => x.tthdon)
                .Must(x => x == null || new decimal[] { 0, 2, 19, 21, 23 }.Contains(x.Value))
                .WithMessage("tthdon cho Hóa đơn Bán hàng chỉ được là: 0 (Gốc), 2 (Thay thế), 19 (Điều chỉnh tăng), 21 (Điều chỉnh giảm), 23 (Điều chỉnh thông tin)");

            data.RuleFor(x => x.details)
                .NotEmpty()
                .WithMessage("details không được trống");

            // Validation cho địa chỉ khi có MST
            data.RuleFor(x => x.dchi)
                .NotEmpty()
                .When(x => !string.IsNullOrEmpty(x.mst))
                .WithMessage("dchi là bắt buộc khi có mst");

            // Validation cho giảm thuế
            data.RuleFor(x => x.tienthuegtgtgiam)
                .NotNull()
                .When(x => x.giamthuebanhang20 == true)
                .WithMessage("tienthuegtgtgiam là bắt buộc khi giamthuebanhang20 = true");
        });

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token là bắt buộc");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vị là bắt buộc");
    }
}

/// <summary>
/// Handler cho CreateSalesInvoiceCommand
/// </summary>
public class CreateSalesInvoiceCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<CreateSalesInvoiceCommand, Response<List<SaveListHoadonBanHangResponse>>>
{
    public async Task<Response<List<SaveListHoadonBanHangResponse>>> Handle(CreateSalesInvoiceCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.CreateSalesInvoiceAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
