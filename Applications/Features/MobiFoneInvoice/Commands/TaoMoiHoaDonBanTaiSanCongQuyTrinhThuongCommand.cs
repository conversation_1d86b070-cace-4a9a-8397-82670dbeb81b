using Applications.DTOs.MobiFoneInvoice.CreatePublicAssetSalesInvoice.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để tạo mới Hóa đơn Bán tài sản công quy trình thường trong MobiFone Invoice API
/// </summary>
public record TaoMoiHoaDonBanTaiSanCongQuyTrinhThuongCommand(
    SaveListHoadonBanTaiSanCongRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<List<SaveListHoadonBanTaiSanCongResponse>>>;

/// <summary>
/// Handler cho TaoMoiHoaDonBanTaiSanCongQuyTrinhThuongCommand
/// </summary>
public class TaoMoiHoaDonBanTaiSanCongQuyTrinhThuongCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<TaoMoiHoaDonBanTaiSanCongQuyTrinhThuongCommand, Response<List<SaveListHoadonBanTaiSanCongResponse>>>
{
    public async Task<Response<List<SaveListHoadonBanTaiSanCongResponse>>> Handle(TaoMoiHoaDonBanTaiSanCongQuyTrinhThuongCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.TaoMoiHoaDonBanTaiSanCongQuyTrinhThuongAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
