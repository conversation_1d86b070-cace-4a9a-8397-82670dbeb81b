using Applications.DTOs.MobiFoneInvoice.CreateSalesInvoice.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để tạo mới Hóa đơn Bán hàng quy trình thường trong MobiFone Invoice API
/// </summary>
public record TaoMoiHoaDonBanHangQuyTrinhThuongCommand(
    SaveListHoadonBanHangRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<List<SaveListHoadonBanHangResponse>>>;

// Validation cho Command (tạm thời comment để tránh lỗi)
// public class ValidateTaoMoiHoaDonBanHangQuyTrinhThuongCommand : AbstractValidator<TaoMoiHoaDonBanHangQuyTrinhThuongCommand>
// {
//     public ValidateTaoMoiHoaDonBanHangQuyTrinhThuongCommand()
//     {
//         RuleFor(x => x.Request)
//             .NotNull()
//             .WithMessage("Request không được để trống");
//
//         RuleFor(x => x.Token)
//             .NotEmpty()
//             .WithMessage("Token là bắt buộc");
//
//         RuleFor(x => x.MaDvcs)
//             .NotEmpty()
//             .WithMessage("Mã đơn vị là bắt buộc");
//     }
// }

/// <summary>
/// Handler cho TaoMoiHoaDonBanHangQuyTrinhThuongCommand
/// </summary>
public class TaoMoiHoaDonBanHangQuyTrinhThuongCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<TaoMoiHoaDonBanHangQuyTrinhThuongCommand, Response<List<SaveListHoadonBanHangResponse>>>
{
    public async Task<Response<List<SaveListHoadonBanHangResponse>>> Handle(TaoMoiHoaDonBanHangQuyTrinhThuongCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.TaoMoiHoaDonBanHangQuyTrinhThuongAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
