using Applications.DTOs.MobiFoneInvoice.CreateSalesInvoiceMTT.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để tạo mới Hóa đơn Bán hàng máy tính tiền sinh mã trong MobiFone Invoice API
/// </summary>
public record TaoMoiHoaDonBanHangMayTinhTienSinhMaCommand(
    SaveListHoadonBanHangMTTRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<List<SaveListHoadonBanHangMTTResponse>>>;

/// <summary>
/// Validator cho TaoMoiHoaDonBanHangMayTinhTienSinhMaCommand
/// </summary>
public class TaoMoiHoaDonBanHangMayTinhTienSinhMaCommandValidator : AbstractValidator<TaoMoiHoaDonBanHangMayTinhTienSinhMaCommand>
{
    public TaoMoiHoaDonBanHangMayTinhTienSinhMaCommandValidator()
    {
        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request không được null");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token là bắt buộc");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vị là bắt buộc");
    }
}

/// <summary>
/// Handler cho TaoMoiHoaDonBanHangMayTinhTienSinhMaCommand
/// </summary>
public class TaoMoiHoaDonBanHangMayTinhTienSinhMaCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<TaoMoiHoaDonBanHangMayTinhTienSinhMaCommand, Response<List<SaveListHoadonBanHangMTTResponse>>>
{
    public async Task<Response<List<SaveListHoadonBanHangMTTResponse>>> Handle(TaoMoiHoaDonBanHangMayTinhTienSinhMaCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.TaoMoiHoaDonBanHangMayTinhTienSinhMaAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
