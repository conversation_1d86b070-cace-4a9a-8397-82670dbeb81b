using Applications.DTOs.MobiFoneInvoice.CancelInvoiceWithoutCode.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để hủy hóa đơn không mã trong MobiFone Invoice API
/// API 4.12: uploadCanceledInv
/// </summary>
public record CancelInvoiceWithoutCodeCommand(
    CancelInvoiceWithoutCodeRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<CancelInvoiceWithoutCodeResponse>>;

/// <summary>
/// Validator cho CancelInvoiceWithoutCodeCommand
/// </summary>
public class CancelInvoiceWithoutCodeCommandValidator : AbstractValidator<CancelInvoiceWithoutCodeCommand>
{
    public CancelInvoiceWithoutCodeCommandValidator()
    {
        RuleFor(x => x.Request.id)
            .NotEmpty()
            .WithMessage("ID hóa đơn không được để trống")
            .Must(BeValidGuid)
            .WithMessage("ID hóa đơn phải là định dạng GUID hợp lệ");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token không được để trống");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vị không được để trống");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }
}

/// <summary>
/// Handler cho CancelInvoiceWithoutCodeCommand
/// </summary>
public class CancelInvoiceWithoutCodeCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<CancelInvoiceWithoutCodeCommand, Response<CancelInvoiceWithoutCodeResponse>>
{
    public async Task<Response<CancelInvoiceWithoutCodeResponse>> Handle(
        CancelInvoiceWithoutCodeCommand request,
        CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.CancelInvoiceWithoutCodeAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
