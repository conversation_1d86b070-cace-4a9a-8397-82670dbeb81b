using Applications.DTOs.MobiFoneInvoice.CreatePXKVCNBInvoice.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để tạo mới Phiếu xuất kho kiêm vận chuyển nội bộ (PXKVCNB) quy trình thường trong MobiFone Invoice API
/// </summary>
public record TaoMoiHoaDonPXKVCNBQuyTrinhThuongCommand(
    SaveListHoadonPXKVCNBRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<List<SaveListHoadonPXKVCNBResponse>>>;

/// <summary>
/// Validator cho TaoMoiHoaDonPXKVCNBQuyTrinhThuongCommand
/// </summary>
public class TaoMoiHoaDonPXKVCNBQuyTrinhThuongCommandValidator : AbstractValidator<TaoMoiHoaDonPXKVCNBQuyTrinhThuongCommand>
{
    public TaoMoiHoaDonPXKVCNBQuyTrinhThuongCommandValidator()
    {
        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request không được null");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token là bắt buộc");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vị là bắt buộc");
    }
}

/// <summary>
/// Handler cho TaoMoiHoaDonPXKVCNBQuyTrinhThuongCommand
/// </summary>
public class TaoMoiHoaDonPXKVCNBQuyTrinhThuongCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<TaoMoiHoaDonPXKVCNBQuyTrinhThuongCommand, Response<List<SaveListHoadonPXKVCNBResponse>>>
{
    public async Task<Response<List<SaveListHoadonPXKVCNBResponse>>> Handle(TaoMoiHoaDonPXKVCNBQuyTrinhThuongCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.TaoMoiHoaDonPXKVCNBQuyTrinhThuongAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
