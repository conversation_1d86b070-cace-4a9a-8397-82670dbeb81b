using Applications.DTOs.MobiFoneInvoice.SendInvoiceByEmail.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để gửi mail phát hành hóa đơn cho người mua trong MobiFone Invoice API
/// API 4.8: AutoSendInvoiceByEmail
/// </summary>
public record SendInvoiceByEmailCommand(
    SendInvoiceByEmailRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<SendInvoiceByEmailResponse>>;

/// <summary>
/// Validator cho SendInvoiceByEmailCommand
/// </summary>
public class SendInvoiceByEmailCommandValidator : AbstractValidator<SendInvoiceByEmailCommand>
{
    public SendInvoiceByEmailCommandValidator()
    {
        RuleFor(x => x.Request.id)
            .NotEmpty()
            .WithMessage("ID hóa đơn không được để trống")
            .Must(BeValidGuid)
            .WithMessage("ID hóa đơn phải là định dạng GUID hợp lệ");

        RuleFor(x => x.Request.nguoinhan)
            .NotEmpty()
            .WithMessage("Email người nhận không được để trống")
            .Must(BeValidEmailList)
            .WithMessage("Email người nhận phải có định dạng hợp lệ");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token không được để trống");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vị không được để trống");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }

    private static bool BeValidEmailList(string emails)
    {
        if (string.IsNullOrWhiteSpace(emails))
            return false;

        var emailList = emails.Split(',', StringSplitOptions.RemoveEmptyEntries);
        return emailList.All(email => IsValidEmail(email.Trim()));
    }

    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }
}

/// <summary>
/// Handler cho SendInvoiceByEmailCommand
/// </summary>
public class SendInvoiceByEmailCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<SendInvoiceByEmailCommand, Response<SendInvoiceByEmailResponse>>
{
    public async Task<Response<SendInvoiceByEmailResponse>> Handle(
        SendInvoiceByEmailCommand request,
        CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.SendInvoiceByEmailAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
