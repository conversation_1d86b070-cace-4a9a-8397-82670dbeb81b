using Applications.DTOs.MobiFoneInvoice.CreatePXKDLInvoice.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để tạo mới Phiếu xuất kho hàng gửi bán đại lý (PXKDL) quy trình thường trong MobiFone Invoice API
/// </summary>
public record TaoMoiHoaDonPXKDLQuyTrinhThuongCommand(
    SaveListHoadonPXKDLRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<List<SaveListHoadonPXKDLResponse>>>;

/// <summary>
/// Validator cho TaoMoiHoaDonPXKDLQuyTrinhThuongCommand
/// </summary>
public class TaoMoiHoaDonPXKDLQuyTrinhThuongCommandValidator : AbstractValidator<TaoMoiHoaDonPXKDLQuyTrinhThuongCommand>
{
    public TaoMoiHoaDonPXKDLQuyTrinhThuongCommandValidator()
    {
        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request không được null");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token là bắt buộc");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vị là bắt buộc");
    }
}

/// <summary>
/// Handler cho TaoMoiHoaDonPXKDLQuyTrinhThuongCommand
/// </summary>
public class TaoMoiHoaDonPXKDLQuyTrinhThuongCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<TaoMoiHoaDonPXKDLQuyTrinhThuongCommand, Response<List<SaveListHoadonPXKDLResponse>>>
{
    public async Task<Response<List<SaveListHoadonPXKDLResponse>>> Handle(TaoMoiHoaDonPXKDLQuyTrinhThuongCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.TaoMoiHoaDonPXKDLQuyTrinhThuongAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
