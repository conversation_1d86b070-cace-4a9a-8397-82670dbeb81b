using Applications.DTOs.MobiFoneInvoice.CreateInvoice.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để tạo mới Hóa đơn Giá trị gia tăng quy trình thường trong MobiFone Invoice API
/// </summary>
public record TaoMoiHoaDonGTGTQuyTrinhThuongCommand(
    SaveListHoadon78Request Request,
    string Token,
    string MaDvcs) : IRequest<Response<List<SaveListHoadon78Response>>>;

// Validation cho Command (tạm thời comment để tránh lỗi)
// public class ValidateTaoMoiHoaDonGTGTQuyTrinhThuongCommand : AbstractValidator<TaoMoiHoaDonGTGTQuyTrinhThuongCommand>
// {
//     public ValidateTaoMoiHoaDonGTGTQuyTrinhThuongCommand()
//     {
//         RuleFor(x => x.Request)
//             .NotNull()
//             .WithMessage("Request không được để trống");
//
//         RuleFor(x => x.Token)
//             .NotEmpty()
//             .WithMessage("Token là bắt buộc");
//
//         RuleFor(x => x.MaDvcs)
//             .NotEmpty()
//             .WithMessage("Mã đơn vị là bắt buộc");
//     }
// }

/// <summary>
/// Handler cho TaoMoiHoaDonGTGTQuyTrinhThuongCommand
/// </summary>
public class TaoMoiHoaDonGTGTQuyTrinhThuongCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<TaoMoiHoaDonGTGTQuyTrinhThuongCommand, Response<List<SaveListHoadon78Response>>>
{
    public async Task<Response<List<SaveListHoadon78Response>>> Handle(TaoMoiHoaDonGTGTQuyTrinhThuongCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.TaoMoiHoaDonGTGTQuyTrinhThuongAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
