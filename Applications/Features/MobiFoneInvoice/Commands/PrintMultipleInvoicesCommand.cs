using Applications.DTOs.MobiFoneInvoice.PrintMultipleInvoices.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để in nhiều hóa đơn trong MobiFone Invoice API
/// API 4.10: InDanhSachHoaDon
/// </summary>
public record PrintMultipleInvoicesCommand(
    PrintMultipleInvoicesRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<byte[]>>;

/// <summary>
/// Validator cho PrintMultipleInvoicesCommand
/// </summary>
public class PrintMultipleInvoicesCommandValidator : AbstractValidator<PrintMultipleInvoicesCommand>
{
    public PrintMultipleInvoicesCommandValidator()
    {
        RuleFor(x => x.Request.invs)
            .NotEmpty()
            .WithMessage("Danh sách ID hóa đơn không được để trống")
            .Must(x => x.Count > 0)
            .WithMessage("Phải có ít nhất một ID hóa đơn");

        RuleForEach(x => x.Request.invs)
            .NotEmpty()
            .WithMessage("ID hóa đơn không được để trống")
            .Must(BeValidGuid)
            .WithMessage("ID hóa đơn phải là định dạng GUID hợp lệ");

        RuleFor(x => x.Request.type)
            .NotEmpty()
            .WithMessage("Loại file không được để trống")
            .Must(x => x.Equals("PDF", StringComparison.OrdinalIgnoreCase))
            .WithMessage("Loại file phải là PDF");

        RuleFor(x => x.Request.inchuyendoi)
            .NotEmpty()
            .WithMessage("Tùy chọn in chuyển đổi không được để trống")
            .Must(x => x == "true" || x == "false")
            .WithMessage("Tùy chọn in chuyển đổi phải là 'true' hoặc 'false'");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token không được để trống");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vị không được để trống");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }
}

/// <summary>
/// Handler cho PrintMultipleInvoicesCommand
/// </summary>
public class PrintMultipleInvoicesCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<PrintMultipleInvoicesCommand, Response<byte[]>>
{
    public async Task<Response<byte[]>> Handle(
        PrintMultipleInvoicesCommand request,
        CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.PrintMultipleInvoicesAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
