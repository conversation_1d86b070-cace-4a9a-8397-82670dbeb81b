using Applications.DTOs.MobiFoneInvoice.SignAndSendInvoiceToCQT68.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để ký và gửi hóa đơn tới CQT (Chỉ dành cho file mềm, SIM) trong MobiFone Invoice API
/// API 4.7: SignAndSendInvoiceToCQT68
/// </summary>
public record SignAndSendInvoiceToCQT68Command(
    SignAndSendInvoiceToCQT68Request Request,
    string Token,
    string MaDvcs) : IRequest<Response<SignAndSendInvoiceToCQT68Response>>;

/// <summary>
/// Validator cho SignAndSendInvoiceToCQT68Command
/// </summary>
public class SignAndSendInvoiceToCQT68CommandValidator : AbstractValidator<SignAndSendInvoiceToCQT68Command>
{
    public SignAndSendInvoiceToCQT68CommandValidator()
    {
        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request không được null");

        RuleFor(x => x.Request.data)
            .NotNull()
            .NotEmpty()
            .WithMessage("data không được null hoặc rỗng");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token là bắt buộc");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vị là bắt buộc");

        // Validate từng item trong data array
        RuleForEach(x => x.Request.data).ChildRules(signData =>
        {
            signData.RuleFor(x => x.branch_code)
                .NotEmpty()
                .WithMessage("branch_code (Mã đơn vị cơ sở) là bắt buộc");

            signData.RuleFor(x => x.username)
                .NotEmpty()
                .WithMessage("username (Tên tài khoản) là bắt buộc");

            signData.RuleFor(x => x.lsthdon_id)
                .NotNull()
                .NotEmpty()
                .WithMessage("lsthdon_id (List id hóa đơn) không được null hoặc rỗng");

            signData.RuleFor(x => x.type_cmd)
                .NotEmpty()
                .WithMessage("type_cmd là bắt buộc")
                .Must(x => x == "200" || x == "203" || x == "206")
                .WithMessage("type_cmd phải là 200 (có mã), 203 (không mã), hoặc 206 (có mã từ MTT)");
        });
    }
}

/// <summary>
/// Handler cho SignAndSendInvoiceToCQT68Command
/// </summary>
public class SignAndSendInvoiceToCQT68CommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<SignAndSendInvoiceToCQT68Command, Response<SignAndSendInvoiceToCQT68Response>>
{
    public async Task<Response<SignAndSendInvoiceToCQT68Response>> Handle(SignAndSendInvoiceToCQT68Command request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.SignAndSendInvoiceToCQT68Async(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
