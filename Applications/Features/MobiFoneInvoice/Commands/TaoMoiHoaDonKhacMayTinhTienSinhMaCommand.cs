using Applications.DTOs.MobiFoneInvoice.CreateOtherInvoiceMTT.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để tạo mới Hóa đơn khác (Tem, vé, thẻ, phiếu...) máy tính tiền sinh mã trong MobiFone Invoice API
/// </summary>
public record TaoMoiHoaDonKhacMayTinhTienSinhMaCommand(
    SaveListHoaDonKhacMTTRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<List<SaveListHoaDonKhacMTTResponse>>>;

/// <summary>
/// Validator cho TaoMoiHoaDonKhacMayTinhTienSinhMaCommand
/// </summary>
public class TaoMoiHoaDonKhacMayTinhTienSinhMaCommandValidator : AbstractValidator<TaoMoiHoaDonKhacMayTinhTienSinhMaCommand>
{
    public TaoMoiHoaDonKhacMayTinhTienSinhMaCommandValidator()
    {
        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request không được null");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token là bắt buộc");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vị là bắt buộc");
    }
}

/// <summary>
/// Handler cho TaoMoiHoaDonKhacMayTinhTienSinhMaCommand
/// </summary>
public class TaoMoiHoaDonKhacMayTinhTienSinhMaCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<TaoMoiHoaDonKhacMayTinhTienSinhMaCommand, Response<List<SaveListHoaDonKhacMTTResponse>>>
{
    public async Task<Response<List<SaveListHoaDonKhacMTTResponse>>> Handle(TaoMoiHoaDonKhacMayTinhTienSinhMaCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.TaoMoiHoaDonKhacMayTinhTienSinhMaAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
