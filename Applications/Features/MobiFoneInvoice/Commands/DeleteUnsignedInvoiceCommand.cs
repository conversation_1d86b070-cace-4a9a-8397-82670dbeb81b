using Applications.DTOs.MobiFoneInvoice.DeleteUnsignedInvoice.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để xóa hóa đơn chưa ký gửi trong MobiFone Invoice API
/// API 4.11: hoadonXoaNhieu
/// </summary>
public record DeleteUnsignedInvoiceCommand(
    DeleteUnsignedInvoiceRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<DeleteUnsignedInvoiceResponse>>;

/// <summary>
/// Validator cho DeleteUnsignedInvoiceCommand
/// </summary>
public class DeleteUnsignedInvoiceCommandValidator : AbstractValidator<DeleteUnsignedInvoiceCommand>
{
    public DeleteUnsignedInvoiceCommandValidator()
    {
        RuleFor(x => x.Request.editmode)
            .Equal(3)
            .WithMessage("Chế độ chỉnh sửa phải là 3 (Xóa hóa đơn)");

        RuleFor(x => x.Request.data)
            .NotEmpty()
            .WithMessage("Danh sách hóa đơn cần xóa không được để trống")
            .Must(x => x.Count > 0)
            .WithMessage("Phải có ít nhất một hóa đơn cần xóa");

        RuleForEach(x => x.Request.data)
            .ChildRules(data =>
            {
                data.RuleFor(x => x.hdon_id)
                    .NotEmpty()
                    .WithMessage("ID hóa đơn không được để trống")
                    .Must(BeValidGuid)
                    .WithMessage("ID hóa đơn phải là định dạng GUID hợp lệ");
            });

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token không được để trống");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vị không được để trống");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }
}

/// <summary>
/// Handler cho DeleteUnsignedInvoiceCommand
/// </summary>
public class DeleteUnsignedInvoiceCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<DeleteUnsignedInvoiceCommand, Response<DeleteUnsignedInvoiceResponse>>
{
    public async Task<Response<DeleteUnsignedInvoiceResponse>> Handle(
        DeleteUnsignedInvoiceCommand request,
        CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.DeleteUnsignedInvoiceAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
