using Applications.DTOs.MobiFoneInvoice.CreateNationalReserveSalesInvoice.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để tạo mới Hóa đơn Bán hàng dự trữ quốc gia quy trình thường trong MobiFone Invoice API
/// </summary>
public record TaoMoiHoaDonBanHangDuTruQuocGiaQuyTrinhThuongCommand(
    SaveListHoadonBanHangDuTruQuocGiaRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<List<SaveListHoadonBanHangDuTruQuocGiaResponse>>>;

/// <summary>
/// Handler cho TaoMoiHoaDonBanHangDuTruQuocGiaQuyTrinhThuongCommand
/// </summary>
public class TaoMoiHoaDonBanHangDuTruQuocGiaQuyTrinhThuongCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<TaoMoiHoaDonBanHangDuTruQuocGiaQuyTrinhThuongCommand, Response<List<SaveListHoadonBanHangDuTruQuocGiaResponse>>>
{
    public async Task<Response<List<SaveListHoadonBanHangDuTruQuocGiaResponse>>> Handle(TaoMoiHoaDonBanHangDuTruQuocGiaQuyTrinhThuongCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.TaoMoiHoaDonBanHangDuTruQuocGiaQuyTrinhThuongAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
