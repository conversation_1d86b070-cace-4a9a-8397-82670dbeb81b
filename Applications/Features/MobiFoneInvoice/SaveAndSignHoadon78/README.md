# API 4.4: <PERSON>àm tạo mới và ký gửi hóa đơn bằng HSM nhà cung cấp khác, file mềm, Sim PKI

## Mô tả
API này cho phép tạo mới và ký gửi hóa đơn bằng HSM nhà cung cấp khác, file mềm, Sim PKI trong hệ thống MobiFone Invoice.

**Lưu ý quan trọng**: API này sử dụng Raw DTOs theo đúng chuẩn tài liệu <PERSON>, tất cả tên field giữ nguyên như trong tài liệu (ví dụ: `editmode`, `type_cmd`, `guiCQT`, `is_api`).

## Endpoint
```
POST /api/mobifone-invoice/save-and-sign-hoadon78
```

## Headers
- `X-Token`: Token từ API login
- `X-MaDvcs`: Mã đơn vị từ API login
- `Content-Type`: application/json

## Request Body (Raw DTO theo chuẩn MobiFone)

### Cấu trú<PERSON> ch<PERSON>
```json
{
  "editmode": 1,
  "cer_serial": null,
  "type_cmd": "200",
  "guiCQT": "Notsend",
  "is_api": "1",
  "data": [
    {
      // Dữ liệu hóa đơn GTGT (sử dụng GTGTInvoiceFields)
    }
  ]
}
```

### Các trường bắt buộc trong request

| Tên trường | Kiểu | Bắt buộc | Mô tả | Giá trị |
|------------|------|----------|-------|---------|
| `editmode` | int | X | Chế độ chỉnh sửa | 1: Tạo mới, 2: Sửa, 3: Xóa hóa đơn |
| `cer_serial` | string | X | Chữ ký số HSM MobiFone | không cần truyền |
| `type_cmd` | string | X | Loại hóa đơn | 200: Có mã, 203: Không mã, 206: Có mã từ MTT |
| `guiCQT` | string | X | Có gửi CQT? | "Notsend": Không gửi |
| `is_api` | string | X | Đơn vị gọi API | "1" |
| `data` | array | X | Mảng dữ liệu hóa đơn | Sử dụng GTGTInvoiceFields |

### Dữ liệu trong mảng `data`
Sử dụng cấu trúc `GTGTInvoiceFields` từ Common, bao gồm:

#### Thông tin cơ bản hóa đơn
- `cctbao_id`: ID dải ký hiệu hóa đơn (từ API GetDataReferences)
- `nlap`: Ngày hóa đơn (yyyy-MM-dd)
- `dvtte`: Mã ngoại tệ (mặc định "VND")
- `tgia`: Tỷ giá (mặc định 1)
- `htttoan`: Phương thức thanh toán
- `mdvi`: Mã đơn vị tạo hóa đơn
- `is_hdcma`: Phân biệt hóa đơn có mã/không có mã (1: Có mã)

#### Thông tin người mua
- `tnmua`: Tên người mua
- `mst`: Mã số thuế bên mua
- `dchi`: Địa chỉ người mua
- `email`: Email của người mua
- `sdtnmua`: Số điện thoại người mua

#### Thông tin tài chính
- `tgtcthue`: Tổng tiền trước thuế
- `tgtthue`: Tổng tiền thuế
- `tgtttbso`: Tổng tiền sau thuế
- `tgtttbso_last`: Tổng tiền cuối cùng

#### Chi tiết hóa đơn (`details`)
```json
{
  "details": [
    {
      "data": [
        {
          "stt": "1",
          "ten": "Tên hàng hóa/dịch vụ",
          "dvtinh": "Đơn vị tính",
          "dgia": 100000,
          "sluong": 1,
          "thtien": 100000,
          "tthue": 10000,
          "tgtien": 110000,
          "kmai": 1,
          "tsuat": "10"
        }
      ]
    }
  ]
}
```

## Response

### Thành công
```json
{
  "code": "000",
  "message": "",
  "data": [
    {
      "id": "unique-id",
      "hdon_id": "invoice-id",
      "cctbao_id": "template-id",
      "tthai": "Đã ký",
      "khieu": "C22TDN",
      "shdon": "0000001",
      "tdlap": "2024-01-15T10:30:00",
      "sbmat": "lookup-code",
      "mccqthue": "tax-authority-code",
      "kygui_cqt": 1,
      "signing": 1,
      // ... các trường khác
    }
  ],
  "isSuccess": true
}
```

### Lỗi
```json
{
  "code": "001",
  "message": "Thông báo lỗi",
  "data": null,
  "isSuccess": false
}
```

## Ví dụ sử dụng

### 1. Tạo mới và ký gửi hóa đơn có mã
```json
{
  "editmode": 1,
  "cer_serial": null,
  "type_cmd": "200",
  "guiCQT": "Notsend",
  "is_api": "1",
  "data": [
    {
      "cctbao_id": "a436b939-9de5-4152-8d32-d766494520af",
      "nlap": "2024-01-15",
      "dvtte": "VND",
      "tgia": 1,
      "htttoan": "Tiền mặt",
      "tnmua": "Nguyễn Văn A",
      "mst": "0123456789",
      "email": "<EMAIL>",
      "dchi": "123 Đường ABC, Quận 1, TP.HCM",
      "tgtcthue": 200000,
      "tgtthue": 20000,
      "tgtttbso": 220000,
      "tgtttbso_last": 220000,
      "mdvi": "VP",
      "is_hdcma": 1,
      "details": [
        {
          "data": [
            {
              "stt": "1",
              "ten": "Sản phẩm A",
              "dvtinh": "Cái",
              "dgia": 100000,
              "sluong": 2,
              "thtien": 200000,
              "tthue": 20000,
              "tgtien": 220000,
              "kmai": 1,
              "tsuat": "10"
            }
          ]
        }
      ]
    }
  ]
}
```

### 2. Tạo mới và ký gửi hóa đơn không mã
```json
{
  "editmode": 1,
  "cer_serial": null,
  "type_cmd": "203",
  "guiCQT": "Notsend",
  "is_api": "1",
  "data": [
    {
      // Cấu trúc tương tự nhưng is_hdcma = 0
      "is_hdcma": 0,
      // ... các trường khác
    }
  ]
}
```

## Validation Rules

1. **editmode**: Phải là 1, 2, hoặc 3
2. **type_cmd**: Phải là "200", "203", hoặc "206"
3. **guiCQT**: Bắt buộc phải có
4. **is_api**: Phải là "1"
5. **data**: Không được null hoặc rỗng
6. **cctbao_id**: Bắt buộc trong mỗi item của data
7. **nlap**: Bắt buộc (định dạng yyyy-MM-dd)
8. **details**: Bắt buộc và không được rỗng

## Lưu ý quan trọng

1. **Raw DTOs**: API này chỉ sử dụng Raw DTOs theo đúng chuẩn tài liệu MobiFone
2. **Tên field**: Tất cả tên field giữ nguyên như trong tài liệu
3. **Authorization**: Sử dụng format "Bear Token;ma_dvcs" (khác với các API khác)
4. **Flow sử dụng**:
   - Gọi API Login để lấy token và mã đơn vị
   - Gọi API GetDataReferences để lấy `cctbao_id` hợp lệ
   - Gọi API SaveAndSignHoadon78 với Raw DTO theo chuẩn MobiFone
5. **Tính toán**: Client phải tự tính toán các tổng tiền
6. **Định dạng ngày**: Sử dụng định dạng `yyyy-MM-dd`
7. **Ký và gửi**: API này vừa tạo vừa ký và gửi hóa đơn trong một lần gọi

## Error Handling

API sử dụng Response<T> pattern thống nhất với error handling tự động và logging chi tiết.

## Dependencies

- GTGTInvoiceFields từ Common
- MediatR CQRS pattern
- FluentValidation
- Comprehensive logging
