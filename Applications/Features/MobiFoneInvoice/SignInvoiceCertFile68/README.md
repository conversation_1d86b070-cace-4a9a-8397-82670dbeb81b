# API 4.5: <PERSON><PERSON> chờ xử lý hóa đơn (bằng file mềm, SIM)

## Mô tả
API này cho phép ký hóa đơn nhưng chưa gửi hóa đơn lên C<PERSON> quan Thuế trong hệ thống MobiFone Invoice.

**Lưu ý quan trọng**: API này sử dụng Raw DTOs theo đúng chuẩn tài li<PERSON>, tất cả tên field giữ nguyên như trong tài liệu (ví dụ: `branch_code`, `lsthdon_id`, `cer_serial`).

## Endpoint
```
POST /api/mobifone-invoice/sign-invoice-cert-file68
```

## Headers
- `X-Token`: Token từ API login
- `X-MaDvcs`: Mã đơn vị từ API login
- `Content-Type`: application/json

## Request Body (Raw DTO theo chuẩn MobiFone)

### Cấu trúc <PERSON>
```json
{
  "data": [
    {
      "branch_code": "VP",
      "username": "ADMINISTRATOR",
      "lsthdon_id": ["a7da2075-c01d-4c9d-a6df-fcd869597661"],
      "cer_serial": "540101094E508CBF7277FFA058C6365F",
      "type_cmd": "203",
      "guiCQT": "Notsend"
    }
  ]
}
```

### Các trường bắt buộc trong request

| Tên trường | Kiểu | Bắt buộc | Mô tả | Giá trị |
|------------|------|----------|-------|---------|
| `data` | array | X | Mảng dữ liệu chứa thông tin ký hóa đơn | |
| `branch_code` | string | X | Mã đơn vị cơ sở | |
| `username` | string | X | Tên tài khoản | |
| `lsthdon_id` | array | X | List id hóa đơn | Danh sách ID hóa đơn cần ký |
| `cer_serial` | string | X | Chữ ký số HSM MobiFone | không cần truyền |
| `type_cmd` | string | X | Loại hóa đơn | 200: có mã, 203: không mã, 206: có mã từ MTT |
| `guiCQT` | string | | Có gửi CQT? | "Notsend": Không gửi |

## Response

### Thành công
```json
{
  "tthai": "Đã ký",
  "ok": true
}
```

### Lỗi
```json
{
  "error": "Json không đúng định dạng !",
  "ok": false
}
```

#### Các lỗi có thể xảy ra:
- `"Json không đúng định dạng !"`: Chuỗi Json truyền lên không đúng. Các trường bắt buộc phải được truyền lên đầy đủ.
- `"Không tìm thấy chứng thư số"`: Lỗi khi hệ thống không tìm thấy chứng thư số.
- `"Tồn tại hóa đơn không ở trạng thái chờ ký"`: Lỗi khi có hóa đơn trong danh sách không ở trạng thái "chờ ký".

## Ví dụ sử dụng

### 1. Ký hóa đơn không mã
```json
{
  "data": [
    {
      "branch_code": "VP",
      "username": "ADMINISTRATOR",
      "lsthdon_id": ["a7da2075-c01d-4c9d-a6df-fcd869597661"],
      "cer_serial": "540101094E508CBF7277FFA058C6365F",
      "type_cmd": "203",
      "guiCQT": "Notsend"
    }
  ]
}
```

### 2. Ký hóa đơn có mã
```json
{
  "data": [
    {
      "branch_code": "VP",
      "username": "ADMINISTRATOR",
      "lsthdon_id": ["b8eb3186-d12e-5d8e-b7ef-gde970608772"],
      "cer_serial": "540101094E508CBF7277FFA058C6365F",
      "type_cmd": "200",
      "guiCQT": "Notsend"
    }
  ]
}
```

### 3. Ký nhiều hóa đơn cùng lúc
```json
{
  "data": [
    {
      "branch_code": "VP",
      "username": "ADMINISTRATOR",
      "lsthdon_id": [
        "a7da2075-c01d-4c9d-a6df-fcd869597661",
        "b8eb3186-d12e-5d8e-b7ef-gde970608772",
        "c9fc4297-e23f-6e9f-c8fg-hef081719883"
      ],
      "cer_serial": "540101094E508CBF7277FFA058C6365F",
      "type_cmd": "203",
      "guiCQT": "Notsend"
    }
  ]
}
```

## Validation Rules

1. **data**: Không được null hoặc rỗng
2. **branch_code**: Bắt buộc phải có
3. **username**: Bắt buộc phải có
4. **lsthdon_id**: Không được null hoặc rỗng
5. **type_cmd**: Phải là "200", "203", hoặc "206"

## Lưu ý quan trọng

1. **Raw DTOs**: API này chỉ sử dụng Raw DTOs theo đúng chuẩn tài liệu MobiFone
2. **Tên field**: Tất cả tên field giữ nguyên như trong tài liệu
3. **Authorization**: Sử dụng format "Bear Token;ma_dvcs"
4. **Flow sử dụng**:
   - Gọi API Login để lấy token và mã đơn vị
   - Tạo hóa đơn trước (sử dụng các API tạo hóa đơn)
   - Gọi API SignInvoiceCertFile68 để ký hóa đơn
   - Hóa đơn sau khi ký sẽ ở trạng thái "Đã ký" nhưng chưa gửi CQT
5. **Trạng thái hóa đơn**: Chỉ ký được hóa đơn ở trạng thái "chờ ký"
6. **Chứng thư số**: Cần có chứng thư số hợp lệ trong hệ thống
7. **Batch processing**: Có thể ký nhiều hóa đơn cùng lúc

## Error Handling

API sử dụng Response<T> pattern thống nhất với error handling tự động và logging chi tiết.

## Dependencies

- MediatR CQRS pattern
- FluentValidation
- Comprehensive logging

## Workflow

1. **Tạo hóa đơn** → Trạng thái: "Chờ ký"
2. **Ký hóa đơn** (API này) → Trạng thái: "Đã ký"
3. **Gửi CQT** (API 4.6) → Trạng thái: "Đã gửi"

API này chỉ thực hiện bước 2 trong workflow trên.
