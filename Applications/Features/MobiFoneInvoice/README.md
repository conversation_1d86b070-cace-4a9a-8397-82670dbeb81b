# MobiFone Invoice API Integration

## Tổng quan

Module tích hợp MobiFone Invoice API vào hệ thống ZenInvoice, hỗ trợ:
- Đăng nhập và lấy token authentication
- <PERSON><PERSON><PERSON> thông tin dải ký hiệu mẫu số hóa đơn
- Auto-switching giữa môi trường Test và Production
- Pattern tương tự ExternalService với HttpClient trực tiếp

## Architecture Pattern

### Service Implementation
```csharp
public class MobiFoneInvoiceService(
    HttpClient httpClient,
    IOptions<MobiFoneInvoiceConfiguration> config,
    ILogger<MobiFoneInvoiceService> logger) : IMobiFoneInvoiceService
{
    // Primary constructor injection pattern
    // HttpClient trực tiếp thay vì IHttpClientFactory
}
```

### Registration Pattern
```csharp
// Program.cs
builder.Services.Configure<MobiFoneInvoiceConfiguration>(
    builder.Configuration.GetSection("MobiFoneInvoice"));
builder.Services.AddHttpClient<IMobiFoneInvoiceService, MobiFoneInvoiceService>(client =>
{
    client.Timeout = TimeSpan.FromSeconds(30);
    client.DefaultRequestHeaders.Add("User-Agent", "ZenInvoice/1.0");
});
```

## Key Features

### 1. Response Pattern
- Sử dụng `Response<T>` pattern thống nhất
- TraceId tự động cho tracking
- Error codes constants
- Success/Failure factory methods

### 2. HTTP Client Pattern  
- HttpClient injection trực tiếp
- Headers được set trên từng request (không dùng DefaultRequestHeaders)
- Curl command generation cho debugging
- Comprehensive error handling

### 3. Environment Switching
- Auto-detect Test vs Production environment
- Different request payloads cho mỗi environment
- URL switching tự động

## Cấu trúc

### DTOs
```
Applications/DTOs/MobiFoneInvoice/
├── Login/
│   ├── LoginRequest.cs
│   ├── LoginResponse.cs
│   └── LoginErrorResponse.cs
└── GetDataReferences/
    ├── GetDataReferencesRequest.cs
    ├── GetDataReferencesResponse.cs
    └── InvoiceTemplateInfo.cs
```

### Features (CQRS)
```
Applications/Features/MobiFoneInvoice/
├── Commands/
│   └── LoginCommand.cs
└── Queries/
    └── GetDataReferencesQuery.cs
```

### Infrastructure
```
Infrastructure/
├── Services/
│   └── MobiFoneInvoiceService.cs
├── Configurations/
│   └── MobiFoneInvoiceConfiguration.cs
└── Utilities/
    └── HttpClientExtensions.cs
```

## Cấu hình

### appsettings.json
```json
{
  "MobiFoneInvoice": {
    "Environment": "Test",
    "ProductionBaseUrl": "https://hcm.mobifone.vn/gateway/mbfinv",
    "TestBaseUrl": "http://mobiinvoice.vn:9000",
    "TaxCode": "0123456789",
    "Username": "<EMAIL>",
    "Password": "Ru51(WMQQ5",
    "TimeoutSeconds": 30
  }
}
```

## API Endpoints

### 1. Login
```http
POST /api/mobifone-invoice/login
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "Ru51(WMQQ5",
  "taxCode": "0123456789"
}
```

**Response:**
```json
{
  "code": "000",
  "message": "",
  "data": {
    "token": "QkdMNHJhVGthUjRJdm9MelpjZXp4cmFOV285cjNLb0xSUnBhdTRzaisvND06QURNSU5",
    "maDvcs": "VP",
    "wbUserId": "576ad54a-49f6-41f6-bc26-7cad72315a5d"
  },
  "isSuccess": true
}
```

### 2. Get Data References
```http
GET /api/mobifone-invoice/data-references?refId=RF00059&taxCode=0123456789
X-Token: {token_from_login}
X-MaDvcs: {ma_dvcs_from_login}
```

**Response:**
```json
{
  "code": "000",
  "message": "",
  "data": {
    "data": [
      {
        "qlmtkeId": "0ac8761e-0c43-4c63-b1b4-fa9b6b9bf9fd",
        "qlkhsdungId": "a436b939-9de5-4152-8d32-d766494520af",
        "lhdon": 6,
        "hthuc": "K",
        "khdon": "N",
        "khhdon": "6K22NYY",
        "sdmau": 1,
        "code": "VP",
        "nglap": "ADMINISTRATOR",
        "nlap": "2022-04-22T21:07:42",
        "permissionId": "0d2fae2c-5e12-44cd-9e87-fa4512becd57",
        "qlkhsdungId1": "a436b939-9de5-4152-8d32-d766494520af",
        "wbUserId": "576ad54a-49f6-41f6-bc26-7cad72315a5d",
        "value": "6K22NYY",
        "id": "a436b939-9de5-4152-8d32-d766494520af"
      }
    ]
  },
  "isSuccess": true
}
```

## Error Codes

| Code | Mô tả |
|------|-------|
| 000 | Thành công |
| 400 | Lỗi chung |
| 401 | Lỗi authentication |
| 403 | Lỗi authorization |
| 404 | Không tìm thấy dữ liệu |
| 422 | Lỗi deserialization |
| 423 | Lỗi JSON parsing |
| 500 | Lỗi exception |

## Logging

Service tự động log:
- Curl commands cho debug
- Response status và content length
- Errors với stack trace
- Performance metrics

## Environment Switching

- **Test**: Sử dụng credentials test với `is_sso_login = 1`
- **Production**: Sử dụng credentials production với `tax_code`

Chuyển đổi bằng cách thay đổi `"Environment": "Test"` thành `"Environment": "Production"` trong appsettings.json

## Sử dụng trong Code

```csharp
// Inject service
private readonly IMobiFoneInvoiceService _mobiFoneService;

// Login
var loginRequest = new LoginRequest { /* ... */ };
var loginResult = await _mobiFoneService.LoginAsync(loginRequest);

if (loginResult.IsSuccess)
{
    var token = loginResult.Data.Token;
    var maDvcs = loginResult.Data.MaDvcs;
    
    // Get data references
    var dataRequest = new GetDataReferencesRequest { RefId = "RF00059" };
    var dataResult = await _mobiFoneService.GetDataReferencesAsync(dataRequest, token, maDvcs);
}
```

## Notes

- Service sử dụng Response<T> pattern thống nhất
- Tích hợp với MediatR CQRS pattern
- Auto-retry và timeout handling
- Comprehensive error handling với detailed logging
- Environment-aware configuration
