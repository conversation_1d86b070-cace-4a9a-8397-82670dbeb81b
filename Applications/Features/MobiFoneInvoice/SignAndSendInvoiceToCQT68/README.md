# API 4.7: <PERSON><PERSON> và gửi hóa đơn tới CQT (Chỉ dành cho file mềm, SIM)

## Mô tả
API này cho phép ký và gửi hóa đơn lên <PERSON><PERSON> quan thuế trong cùng một API trong hệ thống MobiFone Invoice.

**L<PERSON>u ý quan trọng**: API này sử dụng Raw DTOs theo đúng chuẩn tài liệu MobiFone, tất cả tên field giữ nguyên như trong tài liệu (ví dụ: `branch_code`, `lsthdon_id`, `cer_serial`, `is_api`).

## Endpoint
```
POST /api/mobifone-invoice/sign-and-send-invoice-to-cqt68
```

## Headers
- `X-Token`: Token từ API login
- `X-MaDvcs`: Mã đơn vị từ API login
- `Content-Type`: application/json

## Request Body (Raw DTO theo chuẩn Mo<PERSON>Fone)

### C<PERSON>u trúc ch<PERSON>
```json
{
  "data": [
    {
      "branch_code": "VP",
      "username": "ADMINISTRATOR",
      "lsthdon_id": ["a7da2075-c01d-4c9d-a6df-fcd869597661"],
      "cer_serial": "540101094E508CBF7277FFA058C6365F",
      "type_cmd": "203",
      "is_api": "1"
    }
  ]
}
```

### Các trường bắt buộc trong request

| Tên trường | Kiểu | Bắt buộc | Mô tả | Giá trị |
|------------|------|----------|-------|---------|
| `data` | array | X | Mảng dữ liệu chứa thông tin ký và gửi hóa đơn | |
| `branch_code` | string | X | Mã đơn vị cơ sở | |
| `username` | string | X | Tên tài khoản | |
| `lsthdon_id` | array | X | List id hóa đơn | Danh sách ID hóa đơn cần ký và gửi |
| `cer_serial` | string | X | Chữ ký số HSM MobiFone | không cần truyền |
| `type_cmd` | string | X | Loại hóa đơn | 200: có mã, 203: không mã, 206: có mã từ MTT |
| `is_api` | string | | Đơn vị gọi API | "1" |

## Response

### Thành công
```json
{
  "tthai": "Đã ký",
  "trang_thai": "Đã gửi hóa đơn tới Cơ quan thuế",
  "ok": true
}
```

### Lỗi
```json
{
  "error": "Json không đúng định dạng !",
  "ok": false
}
```

#### Các lỗi có thể xảy ra:
- `"Json không đúng định dạng !"`: Chuỗi Json truyền lên không đúng. Các trường bắt buộc phải được truyền lên đầy đủ.
- `"Không tìm thấy chứng thư số"`: Lỗi khi hệ thống không tìm thấy chứng thư số.
- `"Tồn tại hóa đơn không ở trạng thái chờ ký"`: Lỗi khi có hóa đơn trong danh sách không ở trạng thái "chờ ký".

## Ví dụ sử dụng

### 1. Ký và gửi hóa đơn không mã
```json
{
  "data": [
    {
      "branch_code": "VP",
      "username": "ADMINISTRATOR",
      "lsthdon_id": ["a7da2075-c01d-4c9d-a6df-fcd869597661"],
      "cer_serial": "540101094E508CBF7277FFA058C6365F",
      "type_cmd": "203",
      "is_api": "1"
    }
  ]
}
```

### 2. Ký và gửi hóa đơn có mã
```json
{
  "data": [
    {
      "branch_code": "VP",
      "username": "ADMINISTRATOR",
      "lsthdon_id": ["b8eb3186-d12e-5d8e-b7ef-gde970608772"],
      "cer_serial": "540101094E508CBF7277FFA058C6365F",
      "type_cmd": "200",
      "is_api": "1"
    }
  ]
}
```

### 3. Ký và gửi nhiều hóa đơn cùng lúc
```json
{
  "data": [
    {
      "branch_code": "VP",
      "username": "ADMINISTRATOR",
      "lsthdon_id": [
        "a7da2075-c01d-4c9d-a6df-fcd869597661",
        "b8eb3186-d12e-5d8e-b7ef-gde970608772",
        "c9fc4297-e23f-6e9f-c8fg-hef081719883"
      ],
      "cer_serial": "540101094E508CBF7277FFA058C6365F",
      "type_cmd": "203",
      "is_api": "1"
    }
  ]
}
```

## Validation Rules

1. **data**: Không được null hoặc rỗng
2. **branch_code**: Bắt buộc phải có
3. **username**: Bắt buộc phải có
4. **lsthdon_id**: Không được null hoặc rỗng
5. **type_cmd**: Phải là "200", "203", hoặc "206"

## Lưu ý quan trọng

1. **Raw DTOs**: API này chỉ sử dụng Raw DTOs theo đúng chuẩn tài liệu MobiFone
2. **Tên field**: Tất cả tên field giữ nguyên như trong tài liệu
3. **Authorization**: Sử dụng format "Bear Token;ma_dvcs"
4. **Flow sử dụng**:
   - Gọi API Login để lấy token và mã đơn vị
   - Tạo hóa đơn trước (sử dụng các API tạo hóa đơn)
   - Gọi API SignAndSendInvoiceToCQT68 để ký và gửi hóa đơn trong một lần gọi
5. **Trạng thái hóa đơn**: Chỉ ký và gửi được hóa đơn ở trạng thái "chờ ký"
6. **Chứng thư số**: Cần có chứng thư số hợp lệ trong hệ thống
7. **Batch processing**: Có thể ký và gửi nhiều hóa đơn cùng lúc
8. **One-step process**: API này kết hợp cả ký và gửi trong một bước

## Error Handling

API sử dụng Response<T> pattern thống nhất với error handling tự động và logging chi tiết.

## Dependencies

- MediatR CQRS pattern
- FluentValidation
- Comprehensive logging

## Workflow

### So sánh với workflow thông thường:

**Workflow thông thường (3 bước):**
1. **Tạo hóa đơn** → Trạng thái: "Chờ ký"
2. **Ký hóa đơn** (API 4.5) → Trạng thái: "Đã ký"
3. **Gửi CQT** (API 4.6) → Trạng thái: "Đã gửi"

**Workflow với API này (2 bước):**
1. **Tạo hóa đơn** → Trạng thái: "Chờ ký"
2. **Ký và gửi CQT** (API này) → Trạng thái: "Đã ký" + "Đã gửi"

API này thực hiện bước 2 và 3 trong một lần gọi duy nhất.

## Ưu điểm

1. **Tiết kiệm thời gian**: Chỉ cần 1 API call thay vì 2
2. **Đơn giản hóa workflow**: Giảm complexity trong việc quản lý trạng thái
3. **Atomic operation**: Đảm bảo cả ký và gửi đều thành công hoặc đều thất bại
4. **Phù hợp cho file mềm, SIM**: Tối ưu cho các loại chứng thư số này

## Khi nào sử dụng

- **Sử dụng API này khi**: Muốn ký và gửi hóa đơn ngay lập tức
- **Sử dụng API 4.5 + 4.6 khi**: Cần kiểm soát từng bước riêng biệt hoặc có logic xử lý giữa ký và gửi
