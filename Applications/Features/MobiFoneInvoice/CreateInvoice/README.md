# API Tạo H<PERSON>a Đơn MobiFone Invoice

## <PERSON><PERSON> tả
API này cho phép tạo mới hóa đơn quy trình thườ<PERSON> (tất c<PERSON> hình thức <PERSON>) - <PERSON><PERSON><PERSON> đơn Giá trị gia tăng trong hệ thống MobiFone Invoice.

**L<PERSON>u ý quan trọng**: API này sử dụng Raw DTOs theo đúng chuẩn tài liệu MobiFone, tất cả tên field giữ nguyên như trong tài liệu (ví dụ: `cctbao_id`, `ma_dvcs`, `nlap`).

## Endpoint
```
POST /api/mobifone-invoice/create-invoice
```

## Headers
- `X-Token`: Token từ API login
- `X-MaDvcs`: Mã đơn vị từ API login
- `Content-Type`: application/json

## Request Body (Raw DTO theo chuẩn MobiFone)

### Cấu trú<PERSON>
```json
{
  "editmode": 1,
  "data": [
    {
      "cctbao_id": "string",
      "hdon_id": "string",
      "nlap": "2024-01-15",
      "sdhang": "string",
      "khieu": "string",
      "shdon": 0,
      "tthai": "string",
      "tthdon": 0,
      "mccqthue": "string",
      "sbmat": "string",
      "dvtte": "VND",
      "tgia": 1,
      "htttoan": "Tiền mặt",
      "sdtnban": "string",
      "stknban": "string",
      "tnhban": "string",
      "mnmua": "string",
      "mst": "string",
      "tnmua": "string",
      "email": "string",
      "ten": "string",
      "dchi": "string",
      "stknmua": "string",
      "sdtnmua": "string",
      "cmndmua": "string",
      "tnhmua": "string",
      "tchang": "string",
      "mchang": "string",
      "mdvqhnsach_mua": "string",
      "shchieu": "string",
      "tgtcthue": 0,
      "TGTKCThue": 0,
      "tgtthue": 0,
      "tgtttbso": 0,
      "tgtttbso_last": 0,
      "tkcktmn": 0,
      "TGTKhac": 0,
      "tgtphi": 0,
      "mdvi": "string",
      "is_hdcma": 1,
      "details": [
        {
          "data": [
            {
              "stt": "1",
              "ma": "string",
              "ten": "string",
              "mdvtinh": "string",
              "dvtinh": "string",
              "dgia": 0,
              "sluong": 0,
              "tlckhau": 0,
              "stckhau": 0,
              "thtien": 0,
              "tthue": 0,
              "tgtien": 0,
              "kmai": 1,
              "tsuat": "10",
              "lhhdthu": 1,
              "skhung": "string",
              "smay": "string",
              "bksptvchuyen": "string",
              "tnghang": "string",
              "dcnghang": "string",
              "mstnghang": "string",
              "mddnghang": "string"
            }
          ]
        }
      ],
      "hoadon68_phi": [
        {
          "data": [
            {
              "tnphi": "string",
              "tienphi": 0
            }
          ]
        }
      ],
      "hoadon68_khac": [
        {
          "data": [
            {
              "ttruong": "string",
              "kdlieu": "string",
              "dlieu": "string"
            }
          ]
        }
      ],
      "hdon_id_old": "string",
      "lhdclquan": 0,
      "khmshdclquan": "string",
      "khhdclquan": "string",
      "shdclquan": "string",
      "nlhdclquan": "2024-01-15"
    }
  ]
}
```

### Các trường bắt buộc
- `editmode`: Chế độ chỉnh sửa (1: Tạo mới, 2: Sửa, 3: Xóa)
- `data`: Mảng chứa thông tin hóa đơn
- `cctbao_id`: ID dải ký hiệu hóa đơn (lấy từ API GetDataReferences)
- `nlap`: Ngày hóa đơn (định dạng yyyy-MM-dd)
- `dvtte`: Mã ngoại tệ (mặc định "VND")
- `tgia`: Tỷ giá (mặc định 1 cho VND)
- `htttoan`: Phương thức thanh toán
- `tgtcthue`: Tổng tiền trước thuế
- `tgtthue`: Tổng tiền thuế
- `tgtttbso`: Tổng tiền sau thuế
- `tgtttbso_last`: Tổng tiền cuối cùng
- `mdvi`: Mã đơn vị tạo hóa đơn
- `is_hdcma`: Phân biệt hóa đơn có mã/không có mã
- `details`: Danh sách chi tiết hóa đơn

### Validation Rules
- `editmode`: 1 (Tạo mới), 2 (Sửa), 3 (Xóa)
- `is_hdcma`: 0 (Không mã), 1 (Có mã)
- `kmai`: 1 (Hàng hóa dịch vụ), 2 (Khuyến mại), 3 (Chiết khấu thương mại), 4 (Ghi chú), 5 (Hàng hoá đặc trưng)
- `tsuat`: "10", "8", "5", "0", "-1", "-2"
- `dchi`: Bắt buộc nếu có `mst`
- `lhhdthu`: Bắt buộc nếu `kmai = 5`

## Response (Raw DTO theo chuẩn MobiFone)

### Success Response
```json
{
  "code": "000",
  "message": "Success",
  "data": {
    "hdon_id": "string",
    "khieu": "string",
    "shdon": 123456,
    "tthai": "string",
    "sbmat": "string",
    "mccqthue": "string",
    "additional_info": {}
  },
  "isSuccess": true
}
```

### Error Response
```json
{
  "code": "ERROR_CODE",
  "message": "Error message",
  "data": null,
  "isSuccess": false
}
```

## Ví dụ sử dụng (Raw DTO theo chuẩn MobiFone)

### 1. Tạo hóa đơn đơn giản
```json
{
  "editmode": 1,
  "data": [
    {
      "cctbao_id": "a436b939-9de5-4152-8d32-d766494520af",
      "nlap": "2024-01-15",
      "dvtte": "VND",
      "tgia": 1,
      "htttoan": "Tiền mặt",
      "tnmua": "Nguyễn Văn A",
      "email": "<EMAIL>",
      "dchi": "123 Đường ABC, Quận 1, TP.HCM",
      "tgtcthue": 200000,
      "tgtthue": 20000,
      "tgtttbso": 220000,
      "tgtttbso_last": 220000,
      "mdvi": "VP",
      "is_hdcma": 1,
      "details": [
        {
          "data": [
            {
              "stt": "1",
              "ten": "Sản phẩm A",
              "dvtinh": "Cái",
              "dgia": 100000,
              "sluong": 2,
              "thtien": 200000,
              "tthue": 20000,
              "tgtien": 220000,
              "kmai": 1,
              "tsuat": "10"
            }
          ]
        }
      ]
    }
  ]
}
```

### 2. Tạo hóa đơn với hàng hóa đặc trưng (xe ô tô)
```json
{
  "editmode": 1,
  "data": [
    {
      "cctbao_id": "a436b939-9de5-4152-8d32-d766494520af",
      "nlap": "2024-01-15",
      "dvtte": "VND",
      "tgia": 1,
      "htttoan": "Chuyển khoản",
      "tnmua": "Công ty TNHH ABC",
      "mst": "0123456789",
      "dchi": "456 Đường XYZ, Quận 2, TP.HCM",
      "tgtcthue": 1000000000,
      "tgtthue": 100000000,
      "tgtttbso": 1100000000,
      "tgtttbso_last": 1100000000,
      "mdvi": "VP",
      "is_hdcma": 1,
      "details": [
        {
          "data": [
            {
              "stt": "1",
              "ten": "Xe ô tô Toyota Camry",
              "dvtinh": "Chiếc",
              "dgia": 1000000000,
              "sluong": 1,
              "thtien": 1000000000,
              "tthue": 100000000,
              "tgtien": 1100000000,
              "kmai": 5,
              "tsuat": "10",
              "lhhdthu": 1,
              "skhung": "JTDKN3DU5E0123456",
              "smay": "2AR-FE-1234567"
            }
          ]
        }
      ]
    }
  ]
}
```

## Lưu ý quan trọng
1. **Raw DTOs**: API này chỉ sử dụng Raw DTOs theo đúng chuẩn tài liệu MobiFone
2. **Tên field**: Tất cả tên field giữ nguyên như trong tài liệu (ví dụ: `cctbao_id`, `nlap`, `ma_dvcs`)
3. **Không có conversion**: Không có chuyển đổi giữa DTOs "chuẩn C#" và Raw DTOs
4. **Flow sử dụng**:
   - Gọi API Login để lấy token và mã đơn vị
   - Gọi API GetDataReferences để lấy `cctbao_id` hợp lệ
   - Gọi API CreateInvoice với Raw DTO theo chuẩn MobiFone
5. **Validation**: Validation cơ bản được thực hiện ở Command level
6. **Tính toán**: Client phải tự tính toán các tổng tiền (`tgtcthue`, `tgtthue`, `tgtttbso`, `tgtttbso_last`)
7. **Định dạng ngày**: Sử dụng định dạng `yyyy-MM-dd` cho các trường ngày tháng
8. **Cấu trúc nested**: Chú ý cấu trúc nested của `details`, `hoadon68_phi`, `hoadon68_khac` với wrapper `data`

## Mapping với tài liệu MobiFone
- `editmode` → Chế độ chỉnh sửa
- `cctbao_id` → ID dải ký hiệu hóa đơn
- `nlap` → Ngày hóa đơn
- `htttoan` → Phương thức thanh toán
- `mdvi` → Mã đơn vị tạo hóa đơn
- `is_hdcma` → Phân biệt hóa đơn có mã/không có mã
- `kmai` → Tính chất dòng hàng hóa dịch vụ
- `tsuat` → Mã loại thuế suất
- `lhhdthu` → Loại hàng hóa đặc trưng
