using Applications.DTOs.MobiFoneInvoice.DownloadInvoicePDF.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Queries;

/// <summary>
/// Query để tải hóa đơn File .PDF trong MobiFone Invoice API
/// API 4.9: inHoadon
/// </summary>
public record DownloadInvoicePDFQuery(
    DownloadInvoicePDFRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<byte[]>>;

/// <summary>
/// Validator cho DownloadInvoicePDFQuery
/// </summary>
public class DownloadInvoicePDFQueryValidator : AbstractValidator<DownloadInvoicePDFQuery>
{
    public DownloadInvoicePDFQueryValidator()
    {
        RuleFor(x => x.Request.id)
            .NotEmpty()
            .WithMessage("ID hóa đơn không được để trống")
            .Must(BeValidGuid)
            .WithMessage("ID hóa đơn phải là định dạng GUID hợp lệ");

        RuleFor(x => x.Request.type)
            .NotEmpty()
            .WithMessage("Loại file không được để trống")
            .Must(x => x.Equals("PDF", StringComparison.OrdinalIgnoreCase))
            .WithMessage("Loại file phải là PDF");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token không được để trống");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vị không được để trống");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }
}

/// <summary>
/// Handler cho DownloadInvoicePDFQuery
/// </summary>
public class DownloadInvoicePDFQueryHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<DownloadInvoicePDFQuery, Response<byte[]>>
{
    public async Task<Response<byte[]>> Handle(
        DownloadInvoicePDFQuery request,
        CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.DownloadInvoicePDFAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
