using Applications.DTOs.MobiFoneInvoice.GetInvoiceById.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Queries;

/// <summary>
/// Query để lấy thông tin hóa đơn theo ID trong MobiFone Invoice API
/// API 4.13: GetById
/// </summary>
public record GetInvoiceByIdQuery(
    GetInvoiceByIdRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<GetInvoiceByIdResponse>>;

/// <summary>
/// Validator cho GetInvoiceByIdQuery
/// </summary>
public class GetInvoiceByIdQueryValidator : AbstractValidator<GetInvoiceByIdQuery>
{
    public GetInvoiceByIdQueryValidator()
    {
        RuleFor(x => x.Request.id)
            .NotEmpty()
            .WithMessage("ID hóa đơn không được để trống")
            .Must(BeValidGuid)
            .WithMessage("ID hóa đơn phải là định dạng GUID hợp lệ");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token không được để trống");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vị không được để trống");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }
}

/// <summary>
/// Handler cho GetInvoiceByIdQuery
/// </summary>
public class GetInvoiceByIdQueryHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<GetInvoiceByIdQuery, Response<GetInvoiceByIdResponse>>
{
    public async Task<Response<GetInvoiceByIdResponse>> Handle(
        GetInvoiceByIdQuery request,
        CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.GetInvoiceByIdAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
