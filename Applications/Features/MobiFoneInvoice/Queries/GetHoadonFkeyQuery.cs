using Applications.DTOs.MobiFoneInvoice.GetHoadonFkey.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Queries;

/// <summary>
/// Query để lấy danh sách hóa đơn theo FKEY hoặc Khoảng thời gian trong MobiFone Invoice API
/// API 4.14: GetHoadonFkey
/// </summary>
public record GetHoadonFkeyQuery(
    GetHoadonFkeyRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<GetHoadonFkeyResponse>>;

/// <summary>
/// Validator cho GetHoadonFkeyQuery
/// </summary>
public class GetHoadonFkeyQueryValidator : AbstractValidator<GetHoadonFkeyQuery>
{
    public GetHoadonFkeyQueryValidator()
    {
        RuleFor(x => x.Request)
            .Must(HaveValidSearchCriteria)
            .WithMessage("<PERSON>ải cung cấp hdon_id HOẶC cả tu_ngay và den_ngay");

        When(x => !string.IsNullOrEmpty(x.Request.hdon_id), () =>
        {
            RuleFor(x => x.Request.hdon_id)
                .NotEmpty()
                .WithMessage("FKEY hóa đơn không được để trống");
        });

        When(x => string.IsNullOrEmpty(x.Request.hdon_id), () =>
        {
            RuleFor(x => x.Request.tu_ngay)
                .NotEmpty()
                .WithMessage("Từ ngày không được để trống khi không có FKEY")
                .Must(BeValidDate)
                .WithMessage("Từ ngày phải có định dạng yyyy-MM-dd");

            RuleFor(x => x.Request.den_ngay)
                .NotEmpty()
                .WithMessage("Đến ngày không được để trống khi không có FKEY")
                .Must(BeValidDate)
                .WithMessage("Đến ngày phải có định dạng yyyy-MM-dd");

            RuleFor(x => x.Request)
                .Must(x => BeValidDateRange(x.tu_ngay, x.den_ngay))
                .WithMessage("Từ ngày phải nhỏ hơn hoặc bằng đến ngày");
        });

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token không được để trống");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vị không được để trống");
    }

    private static bool HaveValidSearchCriteria(GetHoadonFkeyRequest request)
    {
        return !string.IsNullOrEmpty(request.hdon_id) ||
               (!string.IsNullOrEmpty(request.tu_ngay) && !string.IsNullOrEmpty(request.den_ngay));
    }

    private static bool BeValidDate(string? date)
    {
        if (string.IsNullOrEmpty(date))
            return false;

        return DateTime.TryParseExact(date, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out _);
    }

    private static bool BeValidDateRange(string? tuNgay, string? denNgay)
    {
        if (string.IsNullOrEmpty(tuNgay) || string.IsNullOrEmpty(denNgay))
            return false;

        if (!DateTime.TryParseExact(tuNgay, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out var fromDate) ||
            !DateTime.TryParseExact(denNgay, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out var toDate))
            return false;

        return fromDate <= toDate;
    }
}

/// <summary>
/// Handler cho GetHoadonFkeyQuery
/// </summary>
public class GetHoadonFkeyQueryHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<GetHoadonFkeyQuery, Response<GetHoadonFkeyResponse>>
{
    public async Task<Response<GetHoadonFkeyResponse>> Handle(
        GetHoadonFkeyQuery request,
        CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.GetHoadonFkeyAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
