using Applications.DTOs.MobiFoneInvoice.GetDataReferences;
using Applications.Interfaces.Services;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Queries;

/// <summary>
/// Query để lấy thông tin dải ký hiệu mẫu số hóa đơn từ MobiFone Invoice API
/// </summary>
public record GetDataReferencesQuery(
    GetDataReferencesRequest Request,
    string Token, string maDvcs) : IRequest<Response<GetDataReferencesResponse>>;

/// <summary>
/// Handler cho GetDataReferencesQuery
/// </summary>
public class GetDataReferencesQueryHandler(IMobiFoneInvoiceService mobiFoneInvoiceService) : IRequestHandler<GetDataReferencesQuery, Response<GetDataReferencesResponse>>
{

    public async Task<Response<GetDataReferencesResponse>> Handle(GetDataReferencesQuery request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.GetDataReferencesAsync(
            request.Request, 
            request.Token,
            request.maDvcs,
            cancellationToken);
    }
}
