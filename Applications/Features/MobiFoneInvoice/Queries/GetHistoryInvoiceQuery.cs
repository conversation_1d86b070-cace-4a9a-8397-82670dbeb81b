using Applications.DTOs.MobiFoneInvoice.GetHistoryInvoice.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Queries;

/// <summary>
/// Query để lấy danh sách lịch sử hóa đơn theo ID trong MobiFone Invoice API
/// API 4.20: GetHistoryInvoice
/// </summary>
public record GetHistoryInvoiceQuery(
    string Id,
    string Token,
    string MaDvcs) : IRequest<Response<List<GetHistoryInvoiceResponse>>>;

/// <summary>
/// Validator cho GetHistoryInvoiceQuery
/// </summary>
public class GetHistoryInvoiceQueryValidator : AbstractValidator<GetHistoryInvoiceQuery>
{
    public GetHistoryInvoiceQueryValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("Id hóa đơn là bắt buộc")
            .Must(BeValidGuid)
            .WithMessage("Id hóa đơn phải là định dạng GUID hợp lệ");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token là bắt buộc");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vị là bắt buộc");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }
}

/// <summary>
/// Handler cho GetHistoryInvoiceQuery
/// </summary>
public class GetHistoryInvoiceQueryHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<GetHistoryInvoiceQuery, Response<List<GetHistoryInvoiceResponse>>>
{
    public async Task<Response<List<GetHistoryInvoiceResponse>>> Handle(GetHistoryInvoiceQuery request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.GetHistoryInvoiceAsync(
            request.Id,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
