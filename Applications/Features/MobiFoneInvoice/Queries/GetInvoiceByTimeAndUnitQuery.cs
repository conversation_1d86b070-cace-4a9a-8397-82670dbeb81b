using Applications.DTOs.MobiFoneInvoice.GetInvoiceByTimeAndUnit.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Queries;

/// <summary>
/// Query để lấy danh sách hoá đơn theo thời gian, đơn vị và trạng thái trong MobiFone Invoice API
/// API 4.17: GetInvoiceByTimeAndUnit
/// </summary>
public record GetInvoiceByTimeAndUnitQuery(
    GetInvoiceByTimeAndUnitRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<GetInvoiceByTimeAndUnitResponse>>;

/// <summary>
/// Validator cho GetInvoiceByTimeAndUnitQuery
/// </summary>
public class GetInvoiceByTimeAndUnitQueryValidator : AbstractValidator<GetInvoiceByTimeAndUnitQuery>
{
    public GetInvoiceByTimeAndUnitQueryValidator()
    {
        RuleFor(x => x.Request.tu_ngay)
            .NotEmpty()
            .WithMessage("Từ ngày không được để trống")
            .Must(BeValidDate)
            .WithMessage("Từ ngày phải có định dạng yyyy-MM-dd");

        RuleFor(x => x.Request.den_ngay)
            .NotEmpty()
            .WithMessage("Đến ngày không được để trống")
            .Must(BeValidDate)
            .WithMessage("Đến ngày phải có định dạng yyyy-MM-dd");

        RuleFor(x => x.Request)
            .Must(x => BeValidDateRange(x.tu_ngay, x.den_ngay))
            .WithMessage("Từ ngày phải nhỏ hơn hoặc bằng đến ngày");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token không được để trống");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vị không được để trống");
    }

    private static bool BeValidDate(string date)
    {
        if (string.IsNullOrEmpty(date))
            return false;

        return DateTime.TryParseExact(date, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out _);
    }

    private static bool BeValidDateRange(string tuNgay, string denNgay)
    {
        if (string.IsNullOrEmpty(tuNgay) || string.IsNullOrEmpty(denNgay))
            return false;

        if (!DateTime.TryParseExact(tuNgay, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out var fromDate) ||
            !DateTime.TryParseExact(denNgay, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out var toDate))
            return false;

        return fromDate <= toDate;
    }
}

/// <summary>
/// Handler cho GetInvoiceByTimeAndUnitQuery
/// </summary>
public class GetInvoiceByTimeAndUnitQueryHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<GetInvoiceByTimeAndUnitQuery, Response<GetInvoiceByTimeAndUnitResponse>>
{
    public async Task<Response<GetInvoiceByTimeAndUnitResponse>> Handle(
        GetInvoiceByTimeAndUnitQuery request,
        CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.GetInvoiceByTimeAndUnitAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
