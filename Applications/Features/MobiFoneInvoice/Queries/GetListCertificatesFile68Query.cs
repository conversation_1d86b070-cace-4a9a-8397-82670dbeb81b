using Applications.DTOs.MobiFoneInvoice.GetListCertificatesFile68;
using Applications.Interfaces.Services;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Queries;

/// <summary>
/// Query để lấy thông tin CKS từ MobiFone Invoice API
/// </summary>
public record GetListCertificatesFile68Query(
    GetListCertificatesFile68Request Request,
    string Token, 
    string maDvcs) : IRequest<Response<GetListCertificatesFile68Response>>;

/// <summary>
/// Handler cho GetListCertificatesFile68Query
/// </summary>
public class GetListCertificatesFile68QueryHandler(IMobiFoneInvoiceService mobiFoneInvoiceService) 
    : IRequestHandler<GetListCertificatesFile68Query, Response<GetListCertificatesFile68Response>>
{
    public async Task<Response<GetListCertificatesFile68Response>> Handle(
        GetListCertificatesFile68Query request, 
        CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.GetListCertificatesFile68Async(
            request.Request, 
            request.Token,
            request.maDvcs,
            cancellationToken);
    }
}
