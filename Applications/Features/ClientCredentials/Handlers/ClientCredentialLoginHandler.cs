﻿using Applications.Features.ClientCredentials.Commands;
using Applications.Features.ClientCredentials.Dtos;
using Applications.Interfaces.Repositories;
using Core.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;
using Serilog.Context;
using Shared.Results;
using System.Diagnostics;

namespace Applications.Features.ClientCredentials.Handlers
{
    public class ClientCredentialLoginHandler : IRequestHandler<ClientCredentialLoginCommand, BaseResponse<LoginResponseDto>>
    {
        private readonly IClientCredentialRepository _repository;
        private readonly IJwtTokenGenerator _tokenGenerator;
        private readonly ILogger<ClientCredentialLoginHandler> _logger;

        public ClientCredentialLoginHandler(
            IClientCredentialRepository repository,
            IJwtTokenGenerator tokenGenerator,
            ILogger<ClientCredentialLoginHandler> logger)
        {
            _repository = repository;
            _tokenGenerator = tokenGenerator;
            _logger = logger;
        }

        public async Task<BaseResponse<LoginResponseDto>> Handle(ClientCredentialLoginCommand request, CancellationToken cancellationToken)
        {
            var traceId = Activity.Current?.Id ?? Guid.NewGuid().ToString();

            using (LogContext.PushProperty("TraceId", traceId))
            {
                try
                {
                    _logger.LogInformation("🔐 [Login] Login attempt for ClientId: {ClientId}", request.ClientId);

                    var client = await _repository.GetByClientIdAsync(request.ClientId);
                    if (client == null)
                    {
                        _logger.LogWarning("❌ [Login] ClientId not found - {ClientId}", request.ClientId);
                        return BaseResponse<LoginResponseDto>.Error("Invalid client credentials", "401", traceId);
                    }

                    if (!BCrypt.Net.BCrypt.Verify(request.ClientSecret, client.ClientSecretHash))
                    {
                        _logger.LogWarning("❌ [Login] Invalid secret for ClientId - {ClientId}", request.ClientId);
                        return BaseResponse<LoginResponseDto>.Error("Invalid client credentials", "401", traceId);
                    }

                    var token = _tokenGenerator.GenerateToken(client);
                    _logger.LogInformation("✅ [Login] Token generated for ClientId: {ClientId}", client.ClientId);

                    return BaseResponse<LoginResponseDto>.Success(new LoginResponseDto
                    {
                        AccessToken = token.AccessToken,
                        TokenType = token.TokenType,
                        ExpiresIn = token.ExpiresIn
                    }, "Login successful", "00");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "🔥 [Login] Exception during login for ClientId: {ClientId}", request.ClientId);
                    return BaseResponse<LoginResponseDto>.Error("An unexpected error occurred", "500", traceId);
                }
            }
        }
    }

}
