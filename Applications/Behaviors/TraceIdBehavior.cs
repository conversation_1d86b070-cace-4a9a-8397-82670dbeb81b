using MediatR;
using Microsoft.AspNetCore.Http;
using Shared.Responses;

namespace Applications.Behaviors;

public class TraceIdBehavior<TRequest, TResponse>(IHttpContextAccessor httpContextAccessor)
    : IPipelineBehavior<TRequest, TResponse>
    where TRequest : notnull, IRequest<TResponse>
    where TResponse : notnull
{
    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var response = await next();

        // Nếu response là Response<T>, set TraceId
        if (response is Response<object> responseObj)
        {
            responseObj.TraceId = httpContextAccessor.HttpContext?.TraceIdentifier;
        }
        // Xử lý các kiểu Response khác nếu cần
        else
        {
            // Sử dụng reflection để tìm và set TraceId property
            var responseType = response.GetType();
            if (responseType.IsGenericType && responseType.GetGenericTypeDefinition() == typeof(Response<>))
            {
                var traceIdProperty = responseType.GetProperty("TraceId");
                if (traceIdProperty != null && traceIdProperty.CanWrite)
                {
                    traceIdProperty.SetValue(response, httpContextAccessor.HttpContext?.TraceIdentifier);
                }
            }
        }

        return response;
    }
}
