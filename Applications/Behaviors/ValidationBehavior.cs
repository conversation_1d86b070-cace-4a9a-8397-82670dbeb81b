﻿using FluentValidation;
using MediatR;
using Shared.Results;

namespace Applications.Behaviors
{
    public class ValidationBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>
    {
        private readonly IEnumerable<IValidator<TRequest>> _validators;

        public ValidationBehavior(IEnumerable<IValidator<TRequest>> validators)
        {
            _validators = validators;
        }

        public async Task<TResponse> Handle(
            TRequest request,
            RequestHandlerDelegate<TResponse> next,
            CancellationToken cancellationToken)
        {
            if (!_validators.Any()) return await next();

            var context = new ValidationContext<TRequest>(request);
            var failures = _validators
                .Select(v => v.Validate(context))
                .SelectMany(r => r.Errors)
                .Where(f => f != null)
                .ToList();

            if (failures.Any())
            {
                var errors = string.Join("; ", failures.Select(e => e.<PERSON><PERSON>rMessage));
                var genericType = typeof(TResponse).GetGenericArguments().FirstOrDefault();

                if (genericType != null)
                {
                    var method = typeof(BaseResponse<>)
                        .MakeGenericType(genericType)
                        .GetMethod(nameof(BaseResponse<object>.Error), new[] { typeof(string) });

                    return (TResponse)method!.Invoke(null, new object[] { errors })!;
                }

                throw new ValidationException(failures);
            }

            return await next();
        }
    }
}
