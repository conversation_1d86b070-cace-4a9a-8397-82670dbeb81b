﻿using Applications.Interfaces.Repositories;
using Applications.Interfaces.Services;
using AutoMapper;
using Core;
using Shared.Results;
using System.Diagnostics;
using System.Linq.Expressions;

namespace Applications.Services.Implementations
{
    public class BaseService<TDto, TEntity> : IBaseService<TDto, TEntity>
     where TEntity : Audit
     where TDto : class
    {
        protected readonly IBaseRepository<TEntity> _repository;
        protected readonly IMapper _mapper;

        public BaseService(IBaseRepository<TEntity> repository, IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }

        public virtual async Task<BaseResponse<TDto>> GetByIdAsync(Guid id)
        {
            var traceId = Activity.Current?.Id ?? Guid.NewGuid().ToString();

            var entity = await _repository.GetByIdAsync(id);
            if (entity == null)
                return BaseResponse<TDto>.Error("Entity not found", "404", traceId);

            return BaseResponse<TDto>.Success(_mapper.Map<TDto>(entity), "Retrieved successfully", "00");
        }

        public virtual async Task<BaseResponse<IEnumerable<TDto>>> GetAllAsync()
        {
            var traceId = Activity.Current?.Id ?? Guid.NewGuid().ToString();

            var entities = await _repository.GetAllAsync();
            return BaseResponse<IEnumerable<TDto>>.Success(_mapper.Map<IEnumerable<TDto>>(entities), "List retrieved", "00");
        }

        public virtual async Task<BaseResponse<TDto>> CreateAsync(TDto dto)
        {
            var traceId = Activity.Current?.Id ?? Guid.NewGuid().ToString();

            if (dto == null)
                return BaseResponse<TDto>.Error("Input is null", "400", traceId);

            try
            {
                var entity = _mapper.Map<TEntity>(dto);
                await _repository.AddAsync(entity);
                await _repository.SaveChangesAsync();

                var createdDto = _mapper.Map<TDto>(entity);
                return BaseResponse<TDto>.Success(createdDto, "Created successfully", "00");
            }
            catch (Exception ex)
            {
                return BaseResponse<TDto>.Error($"An error occurred: {ex.Message}", "500", traceId);
            }
        }

        public virtual async Task<BaseResponse<TDto>> UpdateAsync(Guid id, TDto dto)
        {
            var traceId = Activity.Current?.Id ?? Guid.NewGuid().ToString();

            if (dto == null)
                return BaseResponse<TDto>.Error("Input is null", "400", traceId);

            var entity = await _repository.GetByIdAsync(id);
            if (entity == null)
                return BaseResponse<TDto>.Error("Entity not found", "404", traceId);

            try
            {
                _mapper.Map(dto, entity);
                _repository.Update(entity);
                await _repository.SaveChangesAsync();

                return BaseResponse<TDto>.Success(_mapper.Map<TDto>(entity), "Updated successfully", "00");
            }
            catch (Exception ex)
            {
                return BaseResponse<TDto>.Error($"An error occurred: {ex.Message}", "500", traceId);
            }
        }

        public virtual async Task<BaseResponse<bool>> DeleteAsync(Guid id)
        {
            var traceId = Activity.Current?.Id ?? Guid.NewGuid().ToString();

            var entity = await _repository.GetByIdAsync(id);
            if (entity == null)
                return BaseResponse<bool>.Error("Entity not found", "404", traceId);

            try
            {
                _repository.Delete(entity);
                await _repository.SaveChangesAsync();

                return BaseResponse<bool>.Success(true, "Deleted successfully", "00");
            }
            catch (Exception ex)
            {
                return BaseResponse<bool>.Error($"An error occurred: {ex.Message}", "500", traceId);
            }
        }

        public virtual async Task<BaseResponse<IEnumerable<TDto>>> FindAsync(Expression<Func<TEntity, bool>> predicate)
        {
            var traceId = Activity.Current?.Id ?? Guid.NewGuid().ToString();

            var results = await _repository.FindAsync(predicate);
            return BaseResponse<IEnumerable<TDto>>.Success(_mapper.Map<IEnumerable<TDto>>(results), "Filtered list", "00");
        }

        public virtual async Task<bool> ExistsAsync(Guid id)
        {
            return await _repository.AnyAsync(x => x.Id == id);
        }
    }

}
