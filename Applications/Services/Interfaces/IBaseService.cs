﻿using Core;
using Shared.Results;
using System.Linq.Expressions;

namespace Applications.Interfaces.Services
{
    public interface IBaseService<TDto, TEntity>
        where TEntity : Audit
        where TD<PERSON> : class
    {
        Task<BaseResponse<TDto>> GetByIdAsync(Guid id);
        Task<BaseResponse<IEnumerable<TDto>>> GetAllAsync();
        Task<BaseResponse<TDto>> CreateAsync(TDto dto);
        Task<BaseResponse<TDto>> UpdateAsync(Guid id, TDto dto);
        Task<BaseResponse<bool>> DeleteAsync(Guid id);
        Task<BaseResponse<IEnumerable<TDto>>> FindAsync(Expression<Func<TEntity, bool>> predicate);
        Task<bool> ExistsAsync(Guid id);
    }
}
