using Shared.Exceptions;

namespace Applications.Exceptions;

public class MobiFoneApiResponseException : AppLogicException
{
    public override string ErrorCode { get; } = "MOBIFONE_002";

    public MobiFoneApiResponseException() : base("MobiFone API trả về phản hồ<PERSON> không hợp lệ")
    {
    }

    public MobiFoneApiResponseException(string apiName, int statusCode) 
        : base($"MobiFone {apiName} API trả về mã lỗi {statusCode}")
    {
    }

    public MobiFoneApiResponseException(string apiName, string errorMessage) 
        : base($"MobiFone {apiName} API lỗi: {errorMessage}")
    {
    }
}
