using Shared.Exceptions;

namespace Applications.Exceptions;

public class MobiFoneApiConnectionException : AppLogicException
{
    public override string ErrorCode { get; } = "MOBIFONE_001";

    public MobiFoneApiConnectionException() : base("Không thể kết nối đến MobiFone API")
    {
    }

    public MobiFoneApiConnectionException(string message) : base($"Lỗi kết nối MobiFone API: {message}")
    {
    }

    public MobiFoneApiConnectionException(string endpoint, Exception innerException) 
        : base($"Không thể kết nối đến endpoint {endpoint}: {innerException.Message}")
    {
    }
}
