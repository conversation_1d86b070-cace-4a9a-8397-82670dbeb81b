using Shared.Exceptions;

namespace Applications.Exceptions;

public class MobiFoneApiDeserializationException : AppLogicException
{
    public override string ErrorCode { get; } = "MOBIFONE_003";

    public MobiFoneApiDeserializationException() : base("Không thể chuyển đổi dữ liệu từ MobiFone API")
    {
    }

    public MobiFoneApiDeserializationException(string apiName) 
        : base($"Không thể chuyển đổi dữ liệu từ MobiFone {apiName} API")
    {
    }

    public MobiFoneApiDeserializationException(string apiName, Exception innerException) 
        : base($"Lỗi chuyển đổi dữ liệu từ MobiFone {apiName} API: {innerException.Message}")
    {
    }
}
