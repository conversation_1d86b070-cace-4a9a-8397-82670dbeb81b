﻿namespace Applications.Interfaces.Repositories
{
    public interface IUnitOfWork : IDisposable
    {
        IClientCredentialRepository ClientCredentials { get; }
        Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
        Task BeginTransactionAsync(CancellationToken cancellationToken = default);
        Task CommitTransactionAsync(CancellationToken cancellationToken = default);
        Task RollbackTransactionAsync(CancellationToken cancellationToken = default);
    }
}
