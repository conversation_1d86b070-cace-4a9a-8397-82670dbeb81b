using Applications.DTOs.MobiFoneInvoice.GetDataReferences;
using Applications.DTOs.MobiFoneInvoice.GetListCertificatesFile68;
using Applications.DTOs.MobiFoneInvoice.Login;
using Applications.DTOs.MobiFoneInvoice.CreateInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateSalesInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreatePublicAssetSalesInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateNationalReserveSalesInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateOtherInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreatePXKVCNBInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreatePXKDLInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateInvoiceMTT.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateSalesInvoiceMTT.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateOtherInvoiceMTT.Raws;
using Applications.DTOs.MobiFoneInvoice.SaveAndSignHoadon78.Raws;
using Applications.DTOs.MobiFoneInvoice.SignInvoiceCertFile68.Raws;
using Applications.DTOs.MobiFoneInvoice.SendInvoiceToCQT68.Raws;
using Applications.DTOs.MobiFoneInvoice.SignAndSendInvoiceToCQT68.Raws;
using Applications.DTOs.MobiFoneInvoice.GetHistoryInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.SendInvoiceByEmail.Raws;
using Applications.DTOs.MobiFoneInvoice.DownloadInvoicePDF.Raws;
using Applications.DTOs.MobiFoneInvoice.PrintMultipleInvoices.Raws;
using Applications.DTOs.MobiFoneInvoice.DeleteUnsignedInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CancelInvoiceWithoutCode.Raws;
using Applications.DTOs.MobiFoneInvoice.GetInvoiceById.Raws;
using Applications.DTOs.MobiFoneInvoice.GetHoadonFkey.Raws;
using Applications.DTOs.MobiFoneInvoice.GetInvoiceByTimeAndUnit.Raws;
using Shared.Responses;

namespace Applications.Interfaces.Services;

/// <summary>
/// Interface cho service tích hợp MobiFone Invoice API
/// </summary>
public interface IMobiFoneInvoiceService
{
    /// <summary>
    /// Đăng nhập vào hệ thống MobiFone Invoice để lấy token
    /// </summary>
    /// <param name="request">Thông tin đăng nhập</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Thông tin token và user</returns>
    Task<Response<LoginResponse>> LoginAsync(LoginRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Lấy thông tin dải ký hiệu mẫu số hóa đơn
    /// </summary>
    /// <param name="request">Thông tin request</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Danh sách thông tin dải ký hiệu</returns>
    Task<Response<GetDataReferencesResponse>> GetDataReferencesAsync(
        GetDataReferencesRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Lấy thông tin CKS (chứng thư số)
    /// </summary>
    /// <param name="request">Thông tin request</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Danh sách thông tin CKS</returns>
    Task<Response<GetListCertificatesFile68Response>> GetListCertificatesFile68Async(
        GetListCertificatesFile68Request request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Tạo mới hóa đơn quy trình thường (tất cả hình thức HĐ) - Hóa đơn Giá trị gia tăng
    /// </summary>
    /// <param name="request">Thông tin hóa đơn cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Thông tin hóa đơn đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<List<SaveListHoadon78Response>>> CreateInvoiceAsync(
        SaveListHoadon78Request request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Tạo mới Hóa đơn Bán hàng
    /// </summary>
    /// <param name="request">Thông tin hóa đơn bán hàng cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Thông tin hóa đơn bán hàng đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<List<SaveListHoadonBanHangResponse>>> CreateSalesInvoiceAsync(
        SaveListHoadonBanHangRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    // ===== METHODS VỚI TÊN TIẾNG VIỆT =====

    /// <summary>
    /// Tạo mới Hóa đơn Giá trị gia tăng quy trình thường
    /// </summary>
    /// <param name="request">Thông tin hóa đơn cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Thông tin hóa đơn đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<List<SaveListHoadon78Response>>> TaoMoiHoaDonGTGTQuyTrinhThuongAsync(
        SaveListHoadon78Request request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Tạo mới Hóa đơn Bán hàng quy trình thường
    /// </summary>
    /// <param name="request">Thông tin hóa đơn bán hàng cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Thông tin hóa đơn bán hàng đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<List<SaveListHoadonBanHangResponse>>> TaoMoiHoaDonBanHangQuyTrinhThuongAsync(
        SaveListHoadonBanHangRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Tạo mới Hóa đơn Bán tài sản công quy trình thường
    /// </summary>
    /// <param name="request">Thông tin hóa đơn bán tài sản công cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Thông tin hóa đơn bán tài sản công đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<List<SaveListHoadonBanTaiSanCongResponse>>> TaoMoiHoaDonBanTaiSanCongQuyTrinhThuongAsync(
        SaveListHoadonBanTaiSanCongRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Tạo mới Hóa đơn Bán hàng dự trữ quốc gia quy trình thường
    /// </summary>
    /// <param name="request">Thông tin hóa đơn bán hàng dự trữ quốc gia cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Thông tin hóa đơn bán hàng dự trữ quốc gia đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<List<SaveListHoadonBanHangDuTruQuocGiaResponse>>> TaoMoiHoaDonBanHangDuTruQuocGiaQuyTrinhThuongAsync(
        SaveListHoadonBanHangDuTruQuocGiaRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Tạo mới Hóa đơn khác (Tem, vé, thẻ, phiếu...) quy trình thường
    /// </summary>
    /// <param name="request">Thông tin hóa đơn khác cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Thông tin hóa đơn khác đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<List<SaveListHoaDonKhacResponse>>> TaoMoiHoaDonKhacQuyTrinhThuongAsync(
        SaveListHoaDonKhacRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Tạo mới Phiếu xuất kho kiêm vận chuyển nội bộ (PXKVCNB) quy trình thường
    /// </summary>
    /// <param name="request">Thông tin phiếu xuất kho kiêm vận chuyển nội bộ cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Thông tin phiếu xuất kho kiêm vận chuyển nội bộ đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<List<SaveListHoadonPXKVCNBResponse>>> TaoMoiHoaDonPXKVCNBQuyTrinhThuongAsync(
        SaveListHoadonPXKVCNBRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Tạo mới Phiếu xuất kho hàng gửi bán đại lý (PXKDL) quy trình thường
    /// </summary>
    /// <param name="request">Thông tin phiếu xuất kho hàng gửi bán đại lý cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Thông tin phiếu xuất kho hàng gửi bán đại lý đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<List<SaveListHoadonPXKDLResponse>>> TaoMoiHoaDonPXKDLQuyTrinhThuongAsync(
        SaveListHoadonPXKDLRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    // ===== METHODS CHO MÁY TÍNH TIỀN SINH MÃ (MTT) =====

    /// <summary>
    /// Tạo mới Hóa đơn Giá trị gia tăng máy tính tiền sinh mã
    /// </summary>
    /// <param name="request">Thông tin hóa đơn cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Thông tin hóa đơn đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<List<SaveListHoadon78MTTResponse>>> TaoMoiHoaDonGTGTMayTinhTienSinhMaAsync(
        SaveListHoadon78MTTRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Tạo mới Hóa đơn Bán hàng máy tính tiền sinh mã
    /// </summary>
    /// <param name="request">Thông tin hóa đơn bán hàng cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Thông tin hóa đơn bán hàng đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<List<SaveListHoadonBanHangMTTResponse>>> TaoMoiHoaDonBanHangMayTinhTienSinhMaAsync(
        SaveListHoadonBanHangMTTRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Tạo mới Hóa đơn khác (Tem, vé, thẻ, phiếu...) máy tính tiền sinh mã
    /// </summary>
    /// <param name="request">Thông tin hóa đơn khác cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Thông tin hóa đơn khác đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<List<SaveListHoaDonKhacMTTResponse>>> TaoMoiHoaDonKhacMayTinhTienSinhMaAsync(
        SaveListHoaDonKhacMTTRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    // ===== API 4.4: SAVE AND SIGN HOADON78 =====

    /// <summary>
    /// API 4.4: Hàm tạo mới và ký gửi hóa đơn bằng HSM nhà cung cấp khác, file mềm, Sim PKI
    /// URL: {{base_url}}/api/Invoice68/SaveAndSignHoadon78
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// </summary>
    /// <param name="request">Thông tin hóa đơn cần tạo và ký gửi (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Thông tin hóa đơn đã tạo và ký gửi (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<List<SaveAndSignHoadon78Response>>> SaveAndSignHoadon78Async(
        SaveAndSignHoadon78Request request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    // ===== API 4.5: SIGN INVOICE CERT FILE 68 =====

    /// <summary>
    /// API 4.5: Ký chờ xử lý hóa đơn (bằng file mềm, SIM)
    /// URL: {{base_url}}/api/Invoice68/SignInvoiceCertFile68
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: Hàm này cho phép ký hóa đơn nhưng chưa gửi hóa đơn lên Cơ quan Thuế
    /// </summary>
    /// <param name="request">Thông tin hóa đơn cần ký (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Thông tin kết quả ký hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<SignInvoiceCertFile68Response>> SignInvoiceCertFile68Async(
        SignInvoiceCertFile68Request request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    // ===== API 4.6: SEND INVOICE TO CQT 68 =====

    /// <summary>
    /// API 4.6: Gửi hóa đơn đã ký lên Cơ quan thuế
    /// URL: {{base_url}}/api/Invoice68/SendInvoiceToCQT68
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: Hàm này cho phép gửi hóa đơn đã được ký tới Cơ quan Thuế
    /// </summary>
    /// <param name="request">Thông tin hóa đơn cần gửi CQT (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Thông tin kết quả gửi hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<SendInvoiceToCQT68Response>> SendInvoiceToCQT68Async(
        SendInvoiceToCQT68Request request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    // ===== API 4.7: SIGN AND SEND INVOICE TO CQT 68 =====

    /// <summary>
    /// API 4.7: Ký và gửi hóa đơn tới CQT (Chỉ dành cho file mềm, SIM)
    /// URL: {{base_url}}/api/Invoice68/SignInvoiceCertFile68
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: Hàm này cho phép ký và gửi hóa đơn lên Cơ quan thuế trong cùng một API
    /// </summary>
    /// <param name="request">Thông tin hóa đơn cần ký và gửi CQT (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Thông tin kết quả ký và gửi hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<SignAndSendInvoiceToCQT68Response>> SignAndSendInvoiceToCQT68Async(
        SignAndSendInvoiceToCQT68Request request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    // ===== API 4.20: GET HISTORY INVOICE =====

    /// <summary>
    /// API 4.20: Lấy danh sách lịch sử hóa đơn theo ID
    /// URL: {{base_url}}/api/Invoice68/GetHistoryInvoice?id={hdon_id}
    /// Method: GET
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: Hàm này cho phép lấy toàn bộ lịch sử truyền nhận dữ liệu của hóa đơn với Tổng cục thuế
    /// </summary>
    /// <param name="id">ID của hóa đơn cần lấy lịch sử</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Danh sách lịch sử hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<List<GetHistoryInvoiceResponse>>> GetHistoryInvoiceAsync(
        string id,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    // ===== API 4.8: SEND INVOICE BY EMAIL =====

    /// <summary>
    /// API 4.8: Gửi mail phát hành hóa đơn cho người mua
    /// URL: {{base_url}}/api/Invoice68/AutoSendInvoiceByEmail
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: API này cho phép người dùng gửi email thông báo xuất hóa đơn cho người mua
    /// </summary>
    /// <param name="request">Thông tin gửi email hóa đơn (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Kết quả gửi email hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<SendInvoiceByEmailResponse>> SendInvoiceByEmailAsync(
        SendInvoiceByEmailRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    // ===== API 4.9: DOWNLOAD INVOICE PDF =====

    /// <summary>
    /// API 4.9: Tải hóa đơn File .PDF
    /// URL: {{base_url}}/api/Invoice68/inHoadon?id={hdon_id}&type=PDF&inchuyendoi=false
    /// Method: GET
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: Hàm này cho phép người dùng tải file hóa đơn dạng PDF về máy
    /// </summary>
    /// <param name="request">Thông tin tải hóa đơn PDF (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>File PDF dưới dạng byte array</returns>
    Task<Response<byte[]>> DownloadInvoicePDFAsync(
        DownloadInvoicePDFRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    // ===== API 4.10: PRINT MULTIPLE INVOICES =====

    /// <summary>
    /// API 4.10: In nhiều hóa đơn
    /// URL: {{base_url}}/api/Invoice68/InDanhSachHoaDon
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: API này cho phép người dùng in nhiều hóa đơn dưới định dạng PDF
    /// </summary>
    /// <param name="request">Thông tin in nhiều hóa đơn (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>File PDF chứa nhiều hóa đơn dưới dạng byte array</returns>
    Task<Response<byte[]>> PrintMultipleInvoicesAsync(
        PrintMultipleInvoicesRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    // ===== API 4.11: DELETE UNSIGNED INVOICE =====

    /// <summary>
    /// API 4.11: Xóa hóa đơn chưa ký gửi
    /// URL: {{base_url}}/api/Invoice68/hoadonXoaNhieu
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: API này cho phép người dùng xóa hóa đơn chưa ký gửi
    /// </summary>
    /// <param name="request">Thông tin xóa hóa đơn (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Kết quả xóa hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<DeleteUnsignedInvoiceResponse>> DeleteUnsignedInvoiceAsync(
        DeleteUnsignedInvoiceRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    // ===== API 4.12: CANCEL INVOICE WITHOUT CODE =====

    /// <summary>
    /// API 4.12: Hủy hóa đơn không mã
    /// URL: {{base_url}}/api/Invoice68/uploadCanceledInv?id={}
    /// Method: GET
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: API này cho phép người dùng hủy các hóa đơn không mã đã lập nhưng chưa được gửi lên Cơ quan Thuế (CQT)
    /// </summary>
    /// <param name="request">Thông tin hủy hóa đơn (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Kết quả hủy hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<CancelInvoiceWithoutCodeResponse>> CancelInvoiceWithoutCodeAsync(
        CancelInvoiceWithoutCodeRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    // ===== API 4.13: GET INVOICE BY ID =====

    /// <summary>
    /// API 4.13: Lấy thông tin hóa đơn theo ID
    /// URL: {{base_url}}/api/Invoice68/GetById?id={}
    /// Method: GET
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: API này cho phép người dùng lấy toàn bộ thông tin của một hóa đơn dựa trên ID của hóa đơn đó
    /// </summary>
    /// <param name="request">Thông tin lấy hóa đơn theo ID (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Thông tin chi tiết hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<GetInvoiceByIdResponse>> GetInvoiceByIdAsync(
        GetInvoiceByIdRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    // ===== API 4.14: GET HOADON FKEY =====

    /// <summary>
    /// API 4.14: Lấy danh sách hóa đơn theo FKEY hoặc Khoảng thời gian
    /// URL: {{base_url}}/api/Invoice68/GetHoadonFkey
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: API này cho phép người dùng lấy toàn bộ danh sách thông tin của hóa đơn theo mã FKEY hoặc theo khoảng thời gian lập hóa đơn
    /// </summary>
    /// <param name="request">Thông tin lấy danh sách hóa đơn (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Danh sách hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<GetHoadonFkeyResponse>> GetHoadonFkeyAsync(
        GetHoadonFkeyRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    // ===== API 4.17: GET INVOICE BY TIME AND UNIT =====

    /// <summary>
    /// API 4.17: Lấy danh sách hoá đơn theo thời gian, đơn vị và trạng thái
    /// URL: {{base_url}}/api/Invoice68/GetInvoiceByTimeAndUnit
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: Hàm này cho phép người dùng lấy danh sách hoá đơn thoả mãn khoảng thời gian cụ thể, đơn vị của người sử dụng và nằm trong các trạng thái cụ thể (Đã cấp mã, Chấp nhận TBSS, CQT đã nhận)
    /// </summary>
    /// <param name="request">Thông tin lấy danh sách hóa đơn theo thời gian và đơn vị (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Danh sách hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<GetInvoiceByTimeAndUnitResponse>> GetInvoiceByTimeAndUnitAsync(
        GetInvoiceByTimeAndUnitRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);
}
