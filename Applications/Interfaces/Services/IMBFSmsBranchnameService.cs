﻿using Applications.DTOs;
using Shared.Results;

namespace Applications.Interfaces.Services
{
    public interface IMBFSmsBranchnameService
    {
        Task<BaseResponse<bool>> SendUnicodeSmsAsync(string phone, string message); // <PERSON><PERSON>i SMS Unicode
        Task<BaseResponse<bool>> SendNonUnicodeSmsAsync(string phone, string message); // G<PERSON>i SMS Non-Unicode
        Task<BaseResponse<bool>> SendSmsInternalAsync(string phone, string message, string unicode, bool isRetry = false, string useCase = "AccountRegistration"); // <PERSON><PERSON>i SMS nội bộ
        Task<BaseResponse<bool>> SendAccountRegistrationUnicodeSMSOtpAsync(AccountCreationSMSOtp req); // G<PERSON>i OTP đăng ký tài khoản
        Task<BaseResponse<bool>> SendAccountCreationUnicodeSMSConfirmationAsync(AccountCreationSMSConfirmation req); // <PERSON><PERSON><PERSON> thông báo xác nhận tài khoản
        Task<BaseResponse<bool>> SendAccountRegistrationNonUnicodeSMSOtpAsync(AccountCreationSMSOtp req); // Gửi OTP đăng ký tài khoản
        Task<BaseResponse<bool>> SendAccountCreationNonUnicodeSMSConfirmationAsync(AccountCreationSMSConfirmation req); // Gửi thông báo xác nhận tài khoản
    }
}
