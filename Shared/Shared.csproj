﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.5" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="System.Linq.Dynamic.Core" Version="1.6.5" />
        <PackageReference Include="FluentValidation" Version="12.0.0" />
        <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
        <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />
        <PackageReference Include="Mapster" Version="7.4.0" />
        <PackageReference Include="MediatR" Version="12.5.0" />
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.5" />
        <PackageReference Include="Microsoft.FeatureManagement.AspNetCore" Version="4.1.0" />
        <PackageReference Include="Refit.HttpClientFactory" Version="8.0.0" />
    </ItemGroup>

</Project>
