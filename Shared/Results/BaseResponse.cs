﻿using System.Diagnostics;

namespace Shared.Results
{
    /// <summary>
    /// Generic response wrapper dùng cho toàn bộ API.
    /// Gói dữ liệu trả về, trạng thái, thông điệp, traceId và thời gian tạo.
    /// </summary>
    public class BaseResponse<T>
    {
        /// <summary>
        /// Mã trạng thái phản hồi (ví dụ: "00" = success, "400", "500", ...)
        /// </summary>
        public string Code { get; set; } = "00";

        /// <summary>
        /// Thông điệp mô tả trạng thái phản hồi
        /// </summary>
        public string Message { get; set; } = "Success";

        /// <summary>
        /// Dữ liệu trả về (nếu có)
        /// </summary>
        public T? Data { get; set; }

        /// <summary>
        /// Mã TraceId để theo dõi log cho request này
        /// </summary>
        public string? TraceId { get; set; }

        /// <summary>
        /// D<PERSON>u thời gian phản hồi được tạo ra (UTC)
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Cho biết phản hồi có thành công không (Code == "00")
        /// </summary>
        public bool IsSuccess => Code == "00";

        /// <summary>
        /// Tạo phản hồi thành công với dữ liệu
        /// </summary>
        public static BaseResponse<T> Success(T data, string message = "Success", string code = "00") =>
            new()
            {
                Code = code,
                Message = message,
                Data = data,
                TraceId = Activity.Current?.Id,
                Timestamp = DateTime.UtcNow
            };

        /// <summary>
        /// Tạo phản hồi lỗi với thông điệp và mã lỗi
        /// </summary>
        public static BaseResponse<T> Error(string message, string code = "400") =>
            new()
            {
                Code = code,
                Message = message,
                Data = default!,
                TraceId = Activity.Current?.Id,
                Timestamp = DateTime.UtcNow
            };

        /// <summary>
        /// Tạo phản hồi lỗi với TraceId thủ công
        /// </summary>
        public static BaseResponse<T> Error(string message, string code, string traceId) =>
            new()
            {
                Code = code,
                Message = message,
                Data = default!,
                TraceId = traceId,
                Timestamp = DateTime.UtcNow
            };
    }

}
