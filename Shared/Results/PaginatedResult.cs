﻿namespace Shared.Results
{
    /// <summary>
    /// G<PERSON><PERSON> trả về kèm dữ liệu phân trang
    /// </summary>
    public class PaginatedResult<T>
    {
        public IEnumerable<T> Items { get; set; } = Enumerable.Empty<T>();
        public PaginationMeta Meta { get; set; } = new();

        public static PaginatedResult<T> Create(IEnumerable<T> items, int pageIndex, int pageSize, int totalCount)
        {
            var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
            return new PaginatedResult<T>
            {
                Items = items,
                Meta = new PaginationMeta
                {
                    PageIndex = pageIndex,
                    PageSize = pageSize,
                    TotalItems = totalCount,
                    TotalPages = totalPages
                }
            };
        }
    }
}
