﻿namespace Shared.Results
{
    /// <summary>
    /// Metadata cho phân trang
    /// </summary>
    public class PaginationMeta
    {
        public int PageIndex { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public int TotalItems { get; set; }
        public bool HasPrevious => PageIndex > 1;
        public bool HasNext => PageIndex < TotalPages;
    }
}
