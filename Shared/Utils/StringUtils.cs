﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Shared.Utils
{
    public static class StringUtils
    {
        // Hàm loại bỏ các ký tự không cần thiết (dấu xuống dòng, tab, v.v.)
        public static string CleanJsonString(string input)
        {
            if (string.IsNullOrEmpty(input)) return input;

            // Loại bỏ dấu xuống dòng, tab, và các ký tự đặc biệt
            return input.Replace("\r", "")
                        .Replace("\n", "")
                        .Replace("\t", "")
                        .Trim();
        }

        // Hàm chuyển chuỗi JSON thành một đối tượng mà không có các ký tự dư thừa
        public static string CleanJsonPayload(string rawJson)
        {
            if (string.IsNullOrEmpty(rawJson)) return rawJson;

            // <PERSON>iả sử bạn đang làm sạch rawJson và muốn trích xuất phần "payload"
            var jsonObject = JObject.Parse(rawJson);
            if (jsonObject.ContainsKey("payload"))
            {
                var payload = jsonObject["payload"].ToString();
                return CleanJsonString(payload); // Làm sạch và trả về phần payload đã xử lý
            }

            return rawJson; // Nếu không tìm thấy payload, trả về rawJson ban đầu
        }

        // Hàm chuyển đổi chuỗi sang chữ thường
        public static string ToLowerCase(string input)
        {
            return input?.ToLower();
        }

        // Hàm chuyển đổi chuỗi sang chữ hoa
        public static string ToUpperCase(string input)
        {
            return input?.ToUpper();
        }

        // Hàm thay thế ký tự đặc biệt và dấu cách bằng dấu gạch ngang (dùng trong URL)
        public static string Slugify(string input)
        {
            if (string.IsNullOrEmpty(input)) return string.Empty;

            // Chuyển chữ tiếng Việt sang không dấu
            input = RemoveVietnameseAccent(input);
            // Thay thế khoảng trắng bằng gạch ngang
            input = input.ToLower().Replace(" ", "-");
            // Loại bỏ các ký tự không phải chữ hoặc số
            input = Regex.Replace(input, @"[^a-z0-9\-]", "");
            return input;
        }

        // Hàm loại bỏ dấu tiếng Việt
        public static string RemoveVietnameseAccent(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input; // Trả về chuỗi ban đầu nếu chuỗi đầu vào null hoặc rỗng

            var sb = new StringBuilder();
            var vietnameseAccents = new Dictionary<char, char>
        {
            {'á', 'a'}, {'à', 'a'}, {'ả', 'a'}, {'ã', 'a'}, {'ạ', 'a'}, {'ă', 'a'}, {'ắ', 'a'},
            {'ằ', 'a'}, {'ẳ', 'a'}, {'ẵ', 'a'}, {'ặ', 'a'}, {'â', 'a'}, {'ấ', 'a'}, {'ầ', 'a'},
            {'ẩ', 'a'}, {'ẫ', 'a'}, {'ậ', 'a'}, {'é', 'e'}, {'è', 'e'}, {'ẻ', 'e'}, {'ẽ', 'e'},
            {'ẹ', 'e'}, {'ê', 'e'}, {'ế', 'e'}, {'ề', 'e'}, {'ể', 'e'}, {'ễ', 'e'}, {'ệ', 'e'},
            {'í', 'i'}, {'ì', 'i'}, {'ỉ', 'i'}, {'ĩ', 'i'}, {'ị', 'i'}, {'ó', 'o'}, {'ò', 'o'},
            {'ỏ', 'o'}, {'õ', 'o'}, {'ọ', 'o'}, {'ô', 'o'}, {'ố', 'o'}, {'ồ', 'o'}, {'ổ', 'o'},
            {'ỗ', 'o'}, {'ộ', 'o'}, {'ơ', 'o'}, {'ớ', 'o'}, {'ờ', 'o'}, {'ở', 'o'}, {'ỡ', 'o'},
            {'ợ', 'o'}, {'ú', 'u'}, {'ù', 'u'}, {'ủ', 'u'}, {'ũ', 'u'}, {'ụ', 'u'}, {'ư', 'u'},
            {'ứ', 'u'}, {'ừ', 'u'}, {'ử', 'u'}, {'ữ', 'u'}, {'ự', 'u'}, {'ý', 'y'}, {'ỳ', 'y'},
            {'ỷ', 'y'}, {'ỹ', 'y'}, {'ỵ', 'y'}, {'đ', 'd'}
        };

            foreach (var c in input)
            {
                sb.Append(vietnameseAccents.ContainsKey(c) ? vietnameseAccents[c] : c);
            }

            return sb.ToString();
        }

        // Hàm kiểm tra xem chuỗi có phải là email hợp lệ không
        public static bool IsValidEmail(string email)
        {
            if (string.IsNullOrEmpty(email)) return false;

            try
            {
                var emailCheck = new System.Net.Mail.MailAddress(email);
                return emailCheck.Address == email;
            }
            catch
            {
                return false;
            }
        }

        // Hàm kiểm tra số điện thoại có hợp lệ không (ví dụ: bắt đầu với +84 cho Việt Nam)
        public static bool IsValidPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrEmpty(phoneNumber)) return false;

            return Regex.IsMatch(phoneNumber, @"^\+84\d{9,10}$");
        }
        
        private static readonly JsonSerializerOptions DefaultJsonOptions = new()
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };

        public static T? DeserializeTo<T>(this string jsonString)
        {
            if (string.IsNullOrWhiteSpace(jsonString))
                return default(T);

            try
            {
                return JsonSerializer.Deserialize<T>(jsonString, DefaultJsonOptions);
            }
            catch (JsonException ex)
            {
                throw new InvalidDataException($"Invalid JSON data: {ex.Message}", ex);
            }
        }

    }
}
