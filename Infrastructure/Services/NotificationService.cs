﻿using Applications.DTOs;
using Applications.Interfaces.Services;
using Shared.Results;

namespace Infrastructure.Services
{
    public class NotificationService : INotificationService
    {
        private readonly IMBFSmsBranchnameService _smsService;

        public NotificationService(IMBFSmsBranchnameService smsService)
        {
            _smsService = smsService;
        }

        public async Task<BaseResponse<bool>> SendAccountRegistrationSMSOtpAsync(AccountCreationSMSOtp otp)
        {
            return await _smsService.SendAccountRegistrationUnicodeSMSOtpAsync(otp); // Delegate to SMS service
        }
    }
}
