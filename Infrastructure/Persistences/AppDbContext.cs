﻿using Core;
using Core.Entities;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System.Reflection;

namespace Infrastructure.Persistences
{
    public class AppDbContext : DbContext
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public AppDbContext(DbContextOptions<AppDbContext> options, IHttpContextAccessor httpContextAccessor)
            : base(options)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        // DbSets
        public DbSet<ClientCredential> ClientCredentials => Set<ClientCredential>();
        public DbSet<SmsLog> SmsLogs => Set<SmsLog>();
        public DbSet<SmsRetryQueue> SmsRetryQueues => Set<SmsRetryQueue>();
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Áp dụng soft delete filter cho các entity kế thừa Audit
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                if (typeof(Audit).IsAssignableFrom(entityType.ClrType))
                {
                    var method = typeof(AppDbContext)
                        .GetMethod(nameof(SetSoftDeleteFilter), BindingFlags.NonPublic | BindingFlags.Static)!
                        .MakeGenericMethod(entityType.ClrType);
                    method.Invoke(null, new object[] { modelBuilder });
                }
            }

            //modelBuilder.Entity<QrOrder>(entity =>
            //{
            //    entity.HasIndex(e => e.F103).HasDatabaseName("IX_QrOrder_F103");
            //});

            base.OnModelCreating(modelBuilder);
        }

        private static void SetSoftDeleteFilter<TEntity>(ModelBuilder builder) where TEntity : Audit
        {
            builder.Entity<TEntity>().HasQueryFilter(e => !e.IsDeleted);
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            Guid? userId = null;
            var sub = _httpContextAccessor?.HttpContext?.User?.FindFirst("sub")?.Value;
            if (Guid.TryParse(sub, out var uid)) userId = uid;

            var now = DateTime.UtcNow;
            foreach (var entry in ChangeTracker.Entries<Audit>())
            {
                switch (entry.State)
                {
                    case EntityState.Added:
                        entry.Entity.CreatedAt = now;
                        if (entry.Entity.CreatedBy == null)
                            entry.Entity.CreatedBy = userId;
                        break;
                    case EntityState.Modified:
                        entry.Entity.UpdatedAt = now;
                        entry.Entity.UpdatedBy = userId;
                        break;
                }
            }

            return await base.SaveChangesAsync(cancellationToken);
        }
    }
}
