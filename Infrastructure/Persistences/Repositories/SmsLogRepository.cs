﻿using Applications.Interfaces.Repositories;
using Core.Entities;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Persistences.Repositories
{
    public class SmsLogRepository : BaseRepository<SmsLog>, ISmsLogRepository
    {
        private readonly AppDbContext _context;

        public SmsLogRepository(AppDbContext context) : base(context)
        {
            _context = context;
        }

        public async Task<List<SmsRetryQueue>> GetPendingRetriesAsync(int maxRetry, int take)
        {
            return await _context.SmsRetryQueues
                .Include(x => x.SmsLog)
                .Where(x => !x.IsDeleted && !x.IsDone && x.RetryCount < maxRetry)
                .OrderBy(x => x.LastTriedAt)
                .Take(take)
                .ToListAsync();
        }

        public async Task AddRetryQueueAsync(SmsRetryQueue retry)
        {
            await _context.SmsRetryQueues.AddAsync(retry);
        }
    }
}
