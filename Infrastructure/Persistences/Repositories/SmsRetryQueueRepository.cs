﻿using Applications.Interfaces.Repositories;
using Core.Entities;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Persistences.Repositories
{
    public class SmsRetryQueueRepository : BaseRepository<SmsRetryQueue>, ISmsRetryQueueRepository
    {
        private readonly AppDbContext _context;

        public SmsRetryQueueRepository(AppDbContext context) : base(context)
        {
            _context = context;
        }

        public async Task<List<SmsRetryQueue>> GetPendingRetriesAsync(int maxRetry, int take)
        {
            return await _context.SmsRetryQueues
                .Include(x => x.SmsLog)
                .Where(x => !x.IsDeleted && !x.IsDone && x.RetryCount < maxRetry)
                .OrderBy(x => x.LastTriedAt)
                .Take(take)
                .ToListAsync();
        }
    }
}
