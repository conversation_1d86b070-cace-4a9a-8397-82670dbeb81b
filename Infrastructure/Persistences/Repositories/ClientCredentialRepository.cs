﻿using Applications.Interfaces.Repositories;
using Core.Entities;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Persistences.Repositories
{
    public class ClientCredentialRepository : BaseRepository<ClientCredential>, IClientCredentialRepository
    {
        public ClientCredentialRepository(AppDbContext context) : base(context) { }

        public Task<ClientCredential?> GetByClientIdAsync(string clientId)
            => _dbSet.FirstOrDefaultAsync(x => x.ClientId == clientId && !x.IsDeleted);

        public Task<ClientCredential?> ValidateClientAsync(string clientId, string clientSecret)
            => _dbSet.FirstOrDefaultAsync(x => x.ClientId == clientId && x.ClientSecretHash == clientSecret && x.IsActive && !x.IsDeleted);
    }

}
