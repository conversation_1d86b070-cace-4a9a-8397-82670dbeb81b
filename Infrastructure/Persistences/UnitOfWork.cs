﻿using Applications.Interfaces.Repositories;
using Microsoft.Extensions.Logging;

namespace Infrastructure.Persistences
{
    public class UnitOfWork : IUnitOfWork, IDisposable
    {
        private readonly AppDbContext _context;
        private readonly ILogger<UnitOfWork> _logger;

        public IClientCredentialRepository ClientCredentials { get; }
        public ISmsLogRepository SmsLogRepositories { get; }
        public ISmsRetryQueueRepository ISmsRetryQueueRepositories { get; }
        public UnitOfWork(
            AppDbContext context,
            ILogger<UnitOfWork> logger,
            IClientCredentialRepository clientCredentials,
            ISmsLogRepository smsLogRepositories,
            ISmsRetryQueueRepository smsRetryQueueRepositories)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            ClientCredentials = clientCredentials;
            SmsLogRepositories = smsLogRepositories;
            ISmsRetryQueueRepositories = smsRetryQueueRepositories;
        }

        public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            return await _context.SaveChangesAsync(cancellationToken);
        }

        public async Task BeginTransactionAsync(CancellationToken cancellationToken = default)
        {
            await _context.Database.BeginTransactionAsync(cancellationToken);
        }

        public async Task CommitTransactionAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                await _context.Database.CommitTransactionAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ CommitTransactionAsync failed.");
                throw;
            }
        }

        public async Task RollbackTransactionAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                await _context.Database.RollbackTransactionAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ RollbackTransactionAsync failed.");
                throw;
            }
        }

        public void Dispose()
        {
            _context.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
