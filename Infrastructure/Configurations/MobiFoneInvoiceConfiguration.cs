namespace Infrastructure.Configurations;

/// <summary>
/// C<PERSON><PERSON> hình cho MobiFone Invoice API
/// </summary>
public class MobiFoneInvoiceConfiguration
{
    /// <summary>
    /// Section name trong appsettings.json
    /// </summary>
    public const string SectionName = "MobiFoneInvoice";

    /// <summary>
    /// Môi trường hiện tại (Test hoặc Production)
    /// </summary>
    public string Environment { get; set; } = "Test";

    /// <summary>
    /// Base URL cho môi trường Production
    /// </summary>
    public string ProductionBaseUrl { get; set; } = string.Empty;

    /// <summary>
    /// Base URL cho môi trường Test
    /// </summary>
    public string TestBaseUrl { get; set; } = string.Empty;

    /// <summary>
    /// Mã số thuế (dùng cho production)
    /// </summary>
    public string TaxCode { get; set; } = string.Empty;

    /// <summary>
    /// Username mặc định
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Password mặc định
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Timeout cho HTTP requests (seconds)
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Lấy base URL dựa trên môi trường hiện tại
    /// </summary>
    public string GetBaseUrl()
    {
        return Environment.ToLower() == "production" ? ProductionBaseUrl : TestBaseUrl;
    }

    /// <summary>
    /// Kiểm tra có phải môi trường production không
    /// </summary>
    public bool IsProduction => Environment.ToLower() == "production";
}
