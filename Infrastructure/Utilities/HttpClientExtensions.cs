using System.Text;

namespace Infrastructure.Utilities;

/// <summary>
/// Extension methods cho HttpClient để generate curl string
/// </summary>
public static class HttpClientExtensions
{
    /// <summary>
    /// Generate curl command string từ HttpRequestMessage
    /// </summary>
    public static string GenerateCurlString(this HttpClient httpClient, HttpRequestMessage request)
    {
        var curlBuilder = new StringBuilder();
        curlBuilder.Append("curl -X ");
        curlBuilder.Append(request.Method.Method.ToUpperInvariant());

        // Base URL
        var requestUri = request.RequestUri?.ToString() ?? "";
        
        // Use full URL from request
        var fullUrl = requestUri.StartsWith("http") ? requestUri : 
                     !string.IsNullOrEmpty(httpClient.BaseAddress?.ToString()) ? 
                     $"{httpClient.BaseAddress.ToString().TrimEnd('/')}/{requestUri.TrimStart('/')}" : 
                     requestUri;
        
        curlBuilder.Append($" \"{fullUrl}\"");

        // Headers
        foreach (var header in request.Headers)
        {
            foreach (var value in header.Value)
            {
                curlBuilder.Append($" -H \"{header.Key}: {value}\"");
            }
        }

        // Content headers if request has content
        if (request.Content != null)
        {
            foreach (var header in request.Content.Headers)
            {
                foreach (var value in header.Value)
                {
                    curlBuilder.Append($" -H \"{header.Key}: {value}\"");
                }
            }

            // Body content
            if (request.Content is StringContent stringContent)
            {
                var content = stringContent.ReadAsStringAsync().Result;
                if (!string.IsNullOrEmpty(content))
                {
                    // Escape quotes in JSON
                    var escapedContent = content.Replace("\"", "\\\"");
                    curlBuilder.Append($" -d \"{escapedContent}\"");
                }
            }
        }

        return curlBuilder.ToString();
    }
}
