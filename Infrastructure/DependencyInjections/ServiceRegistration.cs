﻿using Applications.Interfaces.Repositories;
using Infrastructure.Persistences;
using Infrastructure.Persistences.Repositories;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Infrastructure.DependencyInjections
{
    //public static class ServiceRegistration
    //{
    //    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, string connectionString)
    //    {
    //        services.AddDbContext<AppDbContext>(options =>
    //            options.UseNpgsql(connectionString));

    //        services.AddScoped(typeof(IBaseRepository<>), typeof(BaseRepository<>));
    //        services.AddScoped<IUnitOfWork, UnitOfWork>();
    //        services.AddRepositories();

    //        return services;
    //    }

    //    public static IServiceCollection AddRepositories(this IServiceCollection services)
    //    {
    //        // Thêm các repository khác nếu cần
    //        return services;
    //    }
    //}
    public static class ServiceRegistration
    {
        public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, string connectionString)
        {
            // Đăng ký DbContext với Npgsql và chuỗi kết nối
            services.AddDbContext<AppDbContext>(options =>
                options.UseNpgsql(connectionString));

            // Đăng ký các service repository và UnitOfWork
            services.AddScoped(typeof(IBaseRepository<>), typeof(BaseRepository<>));
            services.AddScoped<IUnitOfWork, UnitOfWork>();

            // Đăng ký các repositories khác
            services.AddRepositories();

            return services;
        }

        public static IServiceCollection AddRepositories(this IServiceCollection services)
        {
            // Thêm các repository khác nếu cần
            return services;
        }
    }

}
