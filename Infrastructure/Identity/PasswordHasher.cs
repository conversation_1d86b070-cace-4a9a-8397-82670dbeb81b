﻿using Core.Interfaces;

namespace Infrastructure.Identity
{
    /// <summary>
    /// Implementation của IPasswordHasher dùng thư viện BCrypt để hash và verify mật khẩu.
    /// Đặt ở tầng Infrastructure vì phụ thuộc thư viện bên ngoài.
    /// </summary>
    public class PasswordHasher : IPasswordHasher
    {
        public string HashPassword(string password)
        {
            return BCrypt.Net.BCrypt.HashPassword(password);
        }

        public bool VerifyPassword(string password, string hashedPassword)
        {
            return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
        }
    }
}
