﻿using Infrastructure.Extensions;
using Shared.Extensions;
using Shared.Interfaces;
using Shared.Results;
using System.Linq.Expressions;

namespace Infrastructure.Helpers
{
    public class PaginationHelper : IPaginationHelper
    {
        private const int DefaultPageSize = 10;
        private const int DefaultPageIndex = 1;

        public PaginatedResult<T> Paginate<T>(
            IQueryable<T> query,
            int pageIndex,
            int pageSize,
            Expression<Func<T, bool>>? filter = null,
            string? search = null,
            string[]? searchFields = null,
            string? sortField = null,
            string? sortDirection = "asc")
        {
            pageIndex = pageIndex < 1 ? DefaultPageIndex : pageIndex;
            pageSize = pageSize <= 0 ? DefaultPageSize : pageSize;

            return query.ToPaginatedResult(
                pageIndex, pageSize, filter, search, searchFields, sortField, sortDirection);
        }

        public async Task<PaginatedResult<T>> PaginateAsync<T>(
            IQueryable<T> query,
            int pageIndex,
            int pageSize,
            Expression<Func<T, bool>>? filter = null,
            string? search = null,
            string[]? searchFields = null,
            string? sortField = null,
            string? sortDirection = "asc")
        {
            pageIndex = pageIndex < 1 ? DefaultPageIndex : pageIndex;
            pageSize = pageSize <= 0 ? DefaultPageSize : pageSize;

            return await query.ToPaginatedResultAsync(
                pageIndex, pageSize, filter, search, searchFields, sortField, sortDirection);
        }
    }
}
////Cách dùng trong QueryHandler
//public class GetUsersQueryHandler : IRequestHandler<GetUsersQuery, PaginatedResult<UserDto>>
//{
//    private readonly IApplicationDbContext _db;
//    private readonly IPaginationHelper _pagination;

//    public GetUsersQueryHandler(IApplicationDbContext db, IPaginationHelper pagination)
//    {
//        _db = db;
//        _pagination = pagination;
//    }

//    public async Task<PaginatedResult<UserDto>> Handle(GetUsersQuery request, CancellationToken cancellationToken)
//    {
//        var query = _db.Users.AsQueryable();

//        return await _pagination.PaginateAsync(
//            query,
//            request.PageIndex,
//            request.PageSize,
//            filter: u => u.IsActive,
//            search: request.Search,
//            searchFields: new[] { "FullName", "Email" },
//            sortField: request.SortField,
//            sortDirection: request.SortDirection
//        );
//    }
//}
