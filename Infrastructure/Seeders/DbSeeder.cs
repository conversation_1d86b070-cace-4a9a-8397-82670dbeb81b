﻿using Core.Entities;
using Core.Enumerables;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Persistences
{
    public static class DbSeeder
    {
        public static async Task SeedAsync(AppDbContext context)
        {
            // Đảm bảo database đã tạo
            await context.Database.MigrateAsync();

            // ✅ Seed ClientCredential cho hệ thống B2B
            if (!context.ClientCredentials.Any())
            {
                var client = new ClientCredential
                {
                    ClientId = "admin",
                    ClientSecretHash = BCrypt.Net.BCrypt.HashPassword("supersecret"), // mã hoá secret
                    Description = "Admin backend service",
                    IsActive = true,
                    Role = RoleEnum.AdminDashboard // Vai trò của client

                };

                context.ClientCredentials.Add(client);
                await context.SaveChangesAsync();
            }

            // (Optional) Bạn có thể seed thêm Role, User, Permission nếu cần
        }
    }
}
