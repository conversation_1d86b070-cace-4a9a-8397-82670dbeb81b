<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
        <PackageReference Include="MediatR" Version="12.5.0" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.16" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.19.5" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
        <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
        <PackageReference Include="Serilog.Enrichers.HttpContext" Version="8.0.9" />
        <PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
        <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
        <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
        <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
        <PackageReference Include="Serilog.Sinks.Seq" Version="9.0.0" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.2" />
        <PackageReference Include="Refit.HttpClientFactory" Version="8.0.0" />
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Logs\" />
        <Folder Include="Models\Authentications\" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Infrastructure\Infrastructure.csproj" />
        <ProjectReference Include="..\Shared\Shared.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

</Project>
