[01:27:35 ERR] Failed executing DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId"; 
[01:28:21 ERR] Failed executing DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId"; 
[01:28:36 INF] 🔑 [GetNapasToken] Bắt đầu xử lý lấy token từ Napas... e3389406d0e235f9441bf9f445df9867
[01:28:36 ERR] ❌ [GetToken] Exception occurred. TraceId: 00-e3389406d0e235f9441bf9f445df9867-51c8ae2beb49e520-00 e3389406d0e235f9441bf9f445df9867
System.ArgumentNullException: Value cannot be null. (Parameter 'fileName')
   at System.ArgumentNullException.Throw(String paramName)
   at System.ArgumentNullException.ThrowIfNull(Object argument, String paramName)
   at System.Security.Cryptography.X509Certificates.X509Certificate..ctor(String fileName, String password, X509KeyStorageFlags keyStorageFlags)
   at Infrastructure.ExternalSystems.Napas.NapasTokenService.GetTokenFromNapasAsync() in C:\Users\<USER>\source\repos\ZenPay-ZenBox\Infrastructure\ExternalSystems\Napas\NapasTokenService.cs:line 41
[01:28:36 WRN] ❌ [GetNapasToken] Lỗi lấy token: Exception: Value cannot be null. (Parameter 'fileName') e3389406d0e235f9441bf9f445df9867
[01:28:36 WRN] ⚠️ [GetToken] Lỗi lấy token. Code: 500, Message: Exception: Value cannot be null. (Parameter 'fileName') e3389406d0e235f9441bf9f445df9867
[01:28:50 INF] 🔑 [GetNapasToken] Bắt đầu xử lý lấy token từ Napas... 366e3d5a2152ea4f176aaffc110fb2d4
[01:28:50 ERR] ❌ [GetToken] Exception occurred. TraceId: 00-366e3d5a2152ea4f176aaffc110fb2d4-81bdc51d5707f561-00 366e3d5a2152ea4f176aaffc110fb2d4
System.ArgumentNullException: Value cannot be null. (Parameter 'fileName')
   at System.ArgumentNullException.Throw(String paramName)
   at System.ArgumentNullException.ThrowIfNull(Object argument, String paramName)
   at System.Security.Cryptography.X509Certificates.X509Certificate..ctor(String fileName, String password, X509KeyStorageFlags keyStorageFlags)
   at Infrastructure.ExternalSystems.Napas.NapasTokenService.GetTokenFromNapasAsync() in C:\Users\<USER>\source\repos\ZenPay-ZenBox\Infrastructure\ExternalSystems\Napas\NapasTokenService.cs:line 41
[01:28:50 WRN] ❌ [GetNapasToken] Lỗi lấy token: Exception: Value cannot be null. (Parameter 'fileName') 366e3d5a2152ea4f176aaffc110fb2d4
[01:29:02 WRN] ⚠️ [GetToken] Lỗi lấy token. Code: 500, Message: Exception: Value cannot be null. (Parameter 'fileName') 366e3d5a2152ea4f176aaffc110fb2d4
[2025-06-17 01:51:00 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\client-privatekey.key (PrivateKeyPemPath)
[2025-06-17 01:51:00 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\napas-signed\ach-client-apg-ZENPAY.cer (PublicKeyPemPath)
[2025-06-17 01:51:00 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\napas-signed\ach-client-apg-ZENPAY.pfx (ClientCertPath)
[2025-06-17 01:51:00 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\payload\napas-ca.crt (NapasPayloadCertPath)
[2025-06-17 01:51:00 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\payload\dvptml.pfx (ZenPayPayClientKey)
[2025-06-17 01:51:32 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\client-privatekey.key (PrivateKeyPemPath)
[2025-06-17 01:51:32 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\napas-signed\ach-client-apg-ZENPAY.cer (PublicKeyPemPath)
[2025-06-17 01:51:32 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\napas-signed\ach-client-apg-ZENPAY.pfx (ClientCertPath)
[2025-06-17 01:51:32 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\payload\napas-ca.crt (NapasPayloadCertPath)
[2025-06-17 01:51:32 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\payload\dvptml.pfx (ZenPayPayClientKey)
[2025-06-17 01:52:22 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\client-privatekey.key (PrivateKeyPemPath)
[2025-06-17 01:52:22 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\napas-signed\ach-client-apg-ZENPAY.cer (PublicKeyPemPath)
[2025-06-17 01:52:22 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\napas-signed\ach-client-apg-ZENPAY.pfx (ClientCertPath)
[2025-06-17 01:52:22 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\payload\napas-ca.crt (NapasPayloadCertPath)
[2025-06-17 01:52:22 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\payload\dvptml.pfx (ZenPayPayClientKey)
[2025-06-17 01:52:44 INF] 🔑 [GetNapasToken] Bắt đầu xử lý lấy token từ Napas...
[2025-06-17 01:52:45 ERR] ❌ [GetToken] Exception occurred. TraceId: 00-aaf7bc319bc7a7ac0468237e2be85a08-04951f3620127a68-00
System.ArgumentNullException: Value cannot be null. (Parameter 'fileName')
   at System.ArgumentNullException.Throw(String paramName)
   at System.ArgumentNullException.ThrowIfNull(Object argument, String paramName)
   at System.Security.Cryptography.X509Certificates.X509Certificate..ctor(String fileName, String password, X509KeyStorageFlags keyStorageFlags)
   at Infrastructure.ExternalSystems.Napas.NapasTokenService.GetTokenFromNapasAsync() in C:\Users\<USER>\source\repos\ZenPay-ZenBox\Infrastructure\ExternalSystems\Napas\NapasTokenService.cs:line 41
[2025-06-17 01:52:45 WRN] ❌ [GetNapasToken] Lỗi lấy token: Exception: Value cannot be null. (Parameter 'fileName')
[2025-06-17 01:54:15 WRN] ⚠️ [GetToken] Lỗi lấy token. Code: 500, Message: Exception: Value cannot be null. (Parameter 'fileName')
[2025-06-17 01:56:27 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\client-privatekey.key (PrivateKeyPemPath)
[2025-06-17 01:56:27 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\napas-signed\ach-client-apg-ZENPAY.cer (PublicKeyPemPath)
[2025-06-17 01:56:27 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\napas-signed\ach-client-apg-ZENPAY.pfx (ClientCertPath)
[2025-06-17 01:56:27 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\payload\napas-ca.crt (NapasPayloadCertPath)
[2025-06-17 01:56:27 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\payload\dvptml.pfx (ZenPayPayClientKey)
[2025-06-17 01:56:39 INF] 🔑 [GetNapasToken] Bắt đầu xử lý lấy token từ Napas...
[2025-06-17 01:56:39 ERR] ❌ [GetToken] Exception occurred. TraceId: 00-be7f58f1c84732761d90360a059fc007-33627c2a2d57a07b-00
System.ArgumentNullException: Value cannot be null. (Parameter 'fileName')
   at System.ArgumentNullException.Throw(String paramName)
   at System.ArgumentNullException.ThrowIfNull(Object argument, String paramName)
   at System.Security.Cryptography.X509Certificates.X509Certificate..ctor(String fileName, String password, X509KeyStorageFlags keyStorageFlags)
   at Infrastructure.ExternalSystems.Napas.NapasTokenService.GetTokenFromNapasAsync() in C:\Users\<USER>\source\repos\ZenPay-ZenBox\Infrastructure\ExternalSystems\Napas\NapasTokenService.cs:line 41
[2025-06-17 01:56:39 WRN] ❌ [GetNapasToken] Lỗi lấy token: Exception: Value cannot be null. (Parameter 'fileName')
[2025-06-17 01:56:56 WRN] ⚠️ [GetToken] Lỗi lấy token. Code: 500, Message: Exception: Value cannot be null. (Parameter 'fileName')
[2025-06-17 01:57:50 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\client-privatekey.key (PrivateKeyPemPath)
[2025-06-17 01:57:50 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\napas-signed\ach-client-apg-ZENPAY.cer (PublicKeyPemPath)
[2025-06-17 01:57:50 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\napas-signed\ach-client-apg-ZENPAY.pfx (ClientCertPath)
[2025-06-17 01:57:50 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\payload\napas-ca.crt (NapasPayloadCertPath)
[2025-06-17 01:57:50 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\payload\dvptml.pfx (ZenPayPayClientKey)
[2025-06-17 01:58:06 INF] 🔑 [GetNapasToken] Bắt đầu xử lý lấy token từ Napas...
[2025-06-17 01:58:06 ERR] ❌ [GetToken] Exception occurred. TraceId: 00-5c3b709ef61601f83ff5f03211b67523-0363813fe1adc5ac-00
System.ArgumentNullException: Value cannot be null. (Parameter 'fileName')
   at System.ArgumentNullException.Throw(String paramName)
   at System.ArgumentNullException.ThrowIfNull(Object argument, String paramName)
   at System.Security.Cryptography.X509Certificates.X509Certificate..ctor(String fileName, String password, X509KeyStorageFlags keyStorageFlags)
   at Infrastructure.ExternalSystems.Napas.NapasTokenService.GetTokenFromNapasAsync() in C:\Users\<USER>\source\repos\ZenPay-ZenBox\Infrastructure\ExternalSystems\Napas\NapasTokenService.cs:line 41
[2025-06-17 01:58:06 WRN] ❌ [GetNapasToken] Lỗi lấy token: Exception: Value cannot be null. (Parameter 'fileName')
[2025-06-17 01:58:15 WRN] ⚠️ [GetToken] Lỗi lấy token. Code: 500, Message: Exception: Value cannot be null. (Parameter 'fileName')
[2025-06-17 01:58:33 INF] 🔑 [GetNapasToken] Bắt đầu xử lý lấy token từ Napas...
[2025-06-17 01:59:00 ERR] ❌ [GetToken] Exception occurred. TraceId: 00-393188121d505c5989151bb4107d0e62-281c08d33e36a6fe-00
System.ArgumentNullException: Value cannot be null. (Parameter 'fileName')
   at System.ArgumentNullException.Throw(String paramName)
   at System.ArgumentNullException.ThrowIfNull(Object argument, String paramName)
   at System.Security.Cryptography.X509Certificates.X509Certificate..ctor(String fileName, String password, X509KeyStorageFlags keyStorageFlags)
   at Infrastructure.ExternalSystems.Napas.NapasTokenService.GetTokenFromNapasAsync() in C:\Users\<USER>\source\repos\ZenPay-ZenBox\Infrastructure\ExternalSystems\Napas\NapasTokenService.cs:line 41
[2025-06-17 01:59:02 WRN] ❌ [GetNapasToken] Lỗi lấy token: Exception: Value cannot be null. (Parameter 'fileName')
[2025-06-17 01:59:02 WRN] ⚠️ [GetToken] Lỗi lấy token. Code: 500, Message: Exception: Value cannot be null. (Parameter 'fileName')
[2025-06-17 02:00:14 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\client-privatekey.key (PrivateKeyPemPath)
[2025-06-17 02:00:14 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\napas-signed\ach-client-apg-ZENPAY.cer (PublicKeyPemPath)
[2025-06-17 02:00:14 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\napas-signed\ach-client-apg-ZENPAY.pfx (ClientCertPath)
[2025-06-17 02:00:14 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\payload\napas-ca.crt (NapasPayloadCertPath)
[2025-06-17 02:00:14 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\payload\dvptml.pfx (ZenPayPayClientKey)
[2025-06-17 02:00:28 INF] 🔑 [GetNapasToken] Bắt đầu xử lý lấy token từ Napas...
[2025-06-17 02:04:06 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\client-privatekey.key (PrivateKeyPemPath)
[2025-06-17 02:04:06 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\napas-signed\ach-client-apg-ZENPAY.cer (PublicKeyPemPath)
[2025-06-17 02:04:06 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\napas-signed\ach-client-apg-ZENPAY.pfx (ClientCertPath)
[2025-06-17 02:04:06 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\payload\napas-ca.crt (NapasPayloadCertPath)
[2025-06-17 02:04:06 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\payload\dvptml.pfx (ZenPayPayClientKey)
[2025-06-17 02:04:19 INF] 🔑 [GetNapasToken] Bắt đầu xử lý lấy token từ Napas...
[2025-06-17 02:04:50 INF] ✅ [GetToken] Token acquired. Expires in 21599s
[2025-06-17 02:04:50 INF] ✅ [GetNapasToken] Token lấy thành công, ExpiresIn: 21599
[2025-06-17 02:04:50 INF] ✅ [GetToken] Lấy token thành công. ExpiresIn: 21599
[2025-06-17 02:06:15 INF] 🔑 [GetNapasToken] Bắt đầu xử lý lấy token từ Napas...
[2025-06-17 02:06:23 INF] ✅ [GetToken] Token acquired. Expires in 21599s
[2025-06-17 02:06:23 INF] ✅ [GetNapasToken] Token lấy thành công, ExpiresIn: 21599
[2025-06-17 02:06:23 INF] ✅ [GetToken] Lấy token thành công. ExpiresIn: 21599
[2025-06-17 02:11:17 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\client-privatekey.key (PrivateKeyPemPath)
[2025-06-17 02:11:17 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\napas-signed\ach-client-apg-ZENPAY.cer (PublicKeyPemPath)
[2025-06-17 02:11:17 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\napas-signed\ach-client-apg-ZENPAY.pfx (ClientCertPath)
[2025-06-17 02:11:17 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\payload\napas-ca.crt (NapasPayloadCertPath)
[2025-06-17 02:11:17 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\payload\dvptml.pfx (ZenPayPayClientKey)
[2025-06-17 02:11:27 INF] 🔑 [GetNapasToken] Bắt đầu xử lý lấy token từ Napas...
[2025-06-17 02:11:31 INF] ✅ [GetToken] Token acquired. Expires in 21599s
[2025-06-17 02:11:31 INF] ✅ [GetNapasToken] Token lấy thành công, ExpiresIn: 21599
[2025-06-17 02:11:31 INF] ✅ [GetToken] Lấy token thành công. ExpiresIn: 21599
[2025-06-17 02:20:00 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\client-privatekey.key (PrivateKeyPemPath)
[2025-06-17 02:20:00 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\napas-signed\ach-client-apg-ZENPAY.cer (PublicKeyPemPath)
[2025-06-17 02:20:00 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\mtls\napas-signed\ach-client-apg-ZENPAY.pfx (ClientCertPath)
[2025-06-17 02:20:00 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\payload\napas-ca.crt (NapasPayloadCertPath)
[2025-06-17 02:20:00 INF] ✅ [NapasConfig] File OK: C:\Users\<USER>\Music\zenpay-tingting\payload\dvptml.pfx (ZenPayPayClientKey)
[2025-06-17 02:20:01 INF] ✅ [GetToken] Token acquired. Expires in 21599s
[2025-06-17 02:20:01 INF] ✅ Token initialized in memory cache.
[2025-06-17 02:21:28 INF] ✅ [NapasConfig] File OK: client-privatekey.key
[2025-06-17 02:21:28 INF] ✅ [NapasConfig] File OK: ach-client-apg-ZENPAY.cer
[2025-06-17 02:21:28 INF] ✅ [NapasConfig] File OK: ach-client-apg-ZENPAY.pfx
[2025-06-17 02:21:28 INF] ✅ [NapasConfig] File OK: napas-ca.crt
[2025-06-17 02:21:28 INF] ✅ [NapasConfig] File OK: dvptml.pfx
[2025-06-17 02:21:29 INF] ✅ [GetToken] Token acquired. Expires in 21599s
[2025-06-17 02:21:29 INF] ✅ Token initialized in memory cache.
[2025-06-17 02:33:18 INF] ✅ [NapasConfig] File OK: client-privatekey.key
[2025-06-17 02:33:18 INF] ✅ [NapasConfig] File OK: ach-client-apg-ZENPAY.cer
[2025-06-17 02:33:18 INF] ✅ [NapasConfig] File OK: ach-client-apg-ZENPAY.pfx
[2025-06-17 02:33:18 INF] ✅ [NapasConfig] File OK: napas-ca.crt
[2025-06-17 02:33:18 INF] ✅ [NapasConfig] File OK: dvptml.pfx
[2025-06-17 02:33:19 INF] ✅ [GetToken] Token acquired. Expires in 21599s
[2025-06-17 02:33:19 INF] ✅ Token initialized in memory cache.
[2025-06-17 02:35:03 INF] ✅ Signature verify result: false
[2025-06-17 02:35:03 WRN] ❌ Chữ ký không hợp lệ. CaseId: 2025061297041101171854677703, TraceId: 00-20f02921cb650e91b449f2e2d6f0f97e-16602fbf68126137-00
[2025-06-17 02:35:44 INF] ✅ [NapasCallback] Xử lý callback thành công. LogId: "6875f128-67d0-44bd-8abd-2ff5da8a114a", TraceId: 0HNDD1IJDENRD:00000001
[2025-06-17 02:36:23 INF] ✅ Signature verify result: false
[2025-06-17 02:36:25 WRN] ❌ Chữ ký không hợp lệ. CaseId: 2025061297041101171854677703, TraceId: 00-4f9ef6f51172becc79914ad409c19fcf-fd2638871d8537fc-00
[2025-06-17 02:36:30 INF] ✅ [NapasCallback] Xử lý callback thành công. LogId: "af58fabb-f810-486b-86be-c051ee03142a", TraceId: 0HNDD1IJDENRF:00000001
[2025-06-17 02:39:58 INF] ✅ [NapasConfig] File OK: client-privatekey.key
[2025-06-17 02:39:58 INF] ✅ [NapasConfig] File OK: ach-client-apg-ZENPAY.cer
[2025-06-17 02:39:58 INF] ✅ [NapasConfig] File OK: ach-client-apg-ZENPAY.pfx
[2025-06-17 02:39:58 INF] ✅ [NapasConfig] File OK: napas-ca.crt
[2025-06-17 02:39:58 INF] ✅ [NapasConfig] File OK: dvptml.pfx
[2025-06-17 02:39:59 INF] ✅ [GetToken] Token acquired. Expires in 21599s
[2025-06-17 02:39:59 INF] ✅ Token initialized in memory cache.
[2025-06-17 02:40:18 ERR] ❌ [NapasCallback] Lỗi khi phân tích JSON. TraceId: 0HNDD1MAJL39L:********
Newtonsoft.Json.JsonReaderException: Error reading JObject from JsonReader. Path '', line 0, position 0.
   at Newtonsoft.Json.Linq.JObject.Load(JsonReader reader, JsonLoadSettings settings)
   at Newtonsoft.Json.Linq.JObject.Parse(String json, JsonLoadSettings settings)
   at Newtonsoft.Json.Linq.JObject.Parse(String json)
   at API.Controllers.NapasController.ReceiveNotification() in C:\Users\<USER>\source\repos\ZenPay-ZenBox\API\Controllers\NapasController.cs:line 130
[2025-06-17 02:40:34 ERR] ❌ [NapasCallback] Lỗi khi phân tích JSON. TraceId: 0HNDD1MAJL39L:********
Newtonsoft.Json.JsonReaderException: Error reading JObject from JsonReader. Path '', line 0, position 0.
   at Newtonsoft.Json.Linq.JObject.Load(JsonReader reader, JsonLoadSettings settings)
   at Newtonsoft.Json.Linq.JObject.Parse(String json, JsonLoadSettings settings)
   at Newtonsoft.Json.Linq.JObject.Parse(String json)
   at API.Controllers.NapasController.ReceiveNotification() in C:\Users\<USER>\source\repos\ZenPay-ZenBox\API\Controllers\NapasController.cs:line 130
[2025-06-17 02:40:38 ERR] ❌ [NapasCallback] Lỗi khi phân tích JSON. TraceId: 0HNDD1MAJL39L:0000000B
Newtonsoft.Json.JsonReaderException: Error reading JObject from JsonReader. Path '', line 0, position 0.
   at Newtonsoft.Json.Linq.JObject.Load(JsonReader reader, JsonLoadSettings settings)
   at Newtonsoft.Json.Linq.JObject.Parse(String json, JsonLoadSettings settings)
   at Newtonsoft.Json.Linq.JObject.Parse(String json)
   at API.Controllers.NapasController.ReceiveNotification() in C:\Users\<USER>\source\repos\ZenPay-ZenBox\API\Controllers\NapasController.cs:line 130
[2025-06-17 02:41:03 INF] ✅ Signature verify result: false
[2025-06-17 02:42:45 INF] ✅ [NapasConfig] File OK: client-privatekey.key
[2025-06-17 02:42:45 INF] ✅ [NapasConfig] File OK: ach-client-apg-ZENPAY.cer
[2025-06-17 02:42:45 INF] ✅ [NapasConfig] File OK: ach-client-apg-ZENPAY.pfx
[2025-06-17 02:42:45 INF] ✅ [NapasConfig] File OK: napas-ca.crt
[2025-06-17 02:42:45 INF] ✅ [NapasConfig] File OK: dvptml.pfx
[2025-06-17 02:42:46 INF] ✅ [GetToken] Token acquired. Expires in 21599s
[2025-06-17 02:42:46 INF] ✅ Token initialized in memory cache.
[2025-06-17 02:44:46 INF] ✅ [NapasConfig] File OK: client-privatekey.key
[2025-06-17 02:44:46 INF] ✅ [NapasConfig] File OK: ach-client-apg-ZENPAY.cer
[2025-06-17 02:44:46 INF] ✅ [NapasConfig] File OK: ach-client-apg-ZENPAY.pfx
[2025-06-17 02:44:46 INF] ✅ [NapasConfig] File OK: napas-ca.crt
[2025-06-17 02:44:46 INF] ✅ [NapasConfig] File OK: dvptml.pfx
[2025-06-17 02:44:47 INF] ✅ [GetToken] Token acquired. Expires in 21599s
[2025-06-17 02:44:47 INF] ✅ Token initialized in memory cache.
[2025-06-17 02:45:43 INF] ✅ Signature verify result: false
[2025-06-17 13:06:48 ERR] Failed executing DbCommand (39ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "ClientCredentials" (
    "Id" uuid NOT NULL,
    "ClientId" text NOT NULL,
    "ClientSecretHash" text NOT NULL,
    "Role" text NOT NULL,
    "IsActive" boolean NOT NULL,
    "Description" text NOT NULL,
    "CreatedBy" uuid,
    "CreatedAt" timestamp with time zone NOT NULL,
    "UpdatedBy" uuid,
    "UpdatedAt" timestamp with time zone,
    "IsDeleted" boolean NOT NULL,
    "IpAddress" character varying(45),
    "TraceId" character varying(100),
    CONSTRAINT "PK_ClientCredentials" PRIMARY KEY ("Id")
);
[2025-06-17 13:19:38 ERR] Failed executing DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-17 13:31:12 INF] 🔐 [Login] Login attempt for ClientId: admin
[2025-06-17 13:31:13 INF] ✅ [Login] Token generated for ClientId: admin
[2025-06-17 13:39:38 INF] 🔐 [Login] Login attempt for ClientId: admin
[2025-06-17 13:39:38 INF] ✅ [Login] Token generated for ClientId: admin
[2025-06-17 13:54:29 INF] 🔐 [Login] Login attempt for ClientId: admin
[2025-06-17 13:54:29 INF] ✅ [Login] Token generated for ClientId: admin
[2025-06-17 13:55:01 ERR] ❌ Unhandled exception. TraceId: 0HNDDDEAJ1BK6:0000000B
System.NullReferenceException: Object reference not set to an instance of an object.
   at Applications.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in C:\Users\<USER>\source\repos\ZenPay-ZenShop-API\Applications\Behaviors\ValidationBehavior.cs:line 47
   at API.Controllers.ClientCredentialController.Create(CreateClientCredentialCommand command) in C:\Users\<USER>\source\repos\ZenPay-ZenShop-API\API\Controllers\ClientCredentialController.cs:line 23
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at API.Middlewares.SerilogEnrichMiddleware.Invoke(HttpContext context) in C:\Users\<USER>\source\repos\ZenPay-ZenShop-API\API\Middlewares\SerilogEnrichMiddleware.cs:line 24
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at ExceptionHandlingMiddleware.Invoke(HttpContext context) in C:\Users\<USER>\source\repos\ZenPay-ZenShop-API\API\Middlewares\ExceptionHandlingMiddleware.cs:line 25
[2025-06-17 15:43:51 ERR] Failed executing DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[2025-06-17 15:43:51 ERR] Failed executing DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "ClientCredentials" AS c
    WHERE NOT (c."IsDeleted"))
[2025-06-17 15:43:51 ERR] An exception occurred while iterating over the results of a query for context type 'Infrastructure.Persistences.AppDbContext'.
Npgsql.PostgresException (0x80004005): 42P01: relation "ClientCredentials" does not exist

POSITION: 41
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult()
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(CommandBehavior behavior)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
  Exception data:
    Severity: ERROR
    SqlState: 42P01
    MessageText: relation "ClientCredentials" does not exist
    Position: 41
    File: parse_relation.c
    Line: 1449
    Routine: parserOpenTable
Npgsql.PostgresException (0x80004005): 42P01: relation "ClientCredentials" does not exist

POSITION: 41
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult()
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(CommandBehavior behavior)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
  Exception data:
    Severity: ERROR
    SqlState: 42P01
    MessageText: relation "ClientCredentials" does not exist
    Position: 41
    File: parse_relation.c
    Line: 1449
    Routine: parserOpenTable
[2025-06-17 15:46:00 INF] 🔐 [Login] Login attempt for ClientId: admin
[2025-06-17 15:46:00 INF] ✅ [Login] Token generated for ClientId: admin
[2025-06-17 16:02:45 INF] 🔐 [Login] Login attempt for ClientId: RONGVIET
[2025-06-17 16:02:45 INF] ✅ [Login] Token generated for ClientId: RONGVIET
[2025-06-17 16:03:23 INF] 📩 Sending OTP SMS to **********
[2025-06-17 16:03:23 ERR] ❌ Failed to send OTP SMS to **********
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Infrastructure.Services.MBFSmsBranchnameService.SendSmsInternalAsync(String phone, String message, String unicode, Boolean isRetry, String useCase) in C:\Users\<USER>\source\repos\ZenPay-ZenShop-API\Infrastructure\Services\MBFSmsBranchnameService.cs:line 74
   at Infrastructure.Services.MBFSmsBranchnameService.SendAccountRegistrationSMSOtpAsync(AccountCreationSMSOtp req) in C:\Users\<USER>\source\repos\ZenPay-ZenShop-API\Infrastructure\Services\MBFSmsBranchnameService.cs:line 142
[2025-06-17 16:03:58 INF] 📩 Sending OTP SMS to **********
[2025-06-17 16:03:58 ERR] ❌ Failed to send OTP SMS to **********
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Infrastructure.Services.MBFSmsBranchnameService.SendSmsInternalAsync(String phone, String message, String unicode, Boolean isRetry, String useCase) in C:\Users\<USER>\source\repos\ZenPay-ZenShop-API\Infrastructure\Services\MBFSmsBranchnameService.cs:line 74
   at Infrastructure.Services.MBFSmsBranchnameService.SendAccountRegistrationSMSOtpAsync(AccountCreationSMSOtp req) in C:\Users\<USER>\source\repos\ZenPay-ZenShop-API\Infrastructure\Services\MBFSmsBranchnameService.cs:line 142
[2025-06-17 16:04:41 INF] 📩 Sending OTP SMS to **********
[2025-06-17 16:04:41 ERR] ❌ Failed to send OTP SMS to **********
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Infrastructure.Services.MBFSmsBranchnameService.SendSmsInternalAsync(String phone, String message, String unicode, Boolean isRetry, String useCase) in C:\Users\<USER>\source\repos\ZenPay-ZenShop-API\Infrastructure\Services\MBFSmsBranchnameService.cs:line 74
   at Infrastructure.Services.MBFSmsBranchnameService.SendAccountRegistrationSMSOtpAsync(AccountCreationSMSOtp req) in C:\Users\<USER>\source\repos\ZenPay-ZenShop-API\Infrastructure\Services\MBFSmsBranchnameService.cs:line 142
[2025-06-17 16:12:51 INF] 📩 Sending OTP SMS to **********
[2025-06-17 16:12:51 ERR] ❌ Failed to send OTP SMS to **********
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Infrastructure.Services.MBFSmsBranchnameService.SendSmsInternalAsync(String phone, String message, String unicode, Boolean isRetry, String useCase)
   at Infrastructure.Services.MBFSmsBranchnameService.SendAccountRegistrationSMSOtpAsync(AccountCreationSMSOtp req)
[2025-06-17 16:14:08 INF] 📩 Sending OTP SMS to **********
[2025-06-17 16:14:23 ERR] ❌ Failed to send OTP SMS to **********
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Infrastructure.Services.MBFSmsBranchnameService.SendSmsInternalAsync(String phone, String message, String unicode, Boolean isRetry, String useCase) in C:\Users\<USER>\source\repos\ZenPay-ZenShop-API\Infrastructure\Services\MBFSmsBranchnameService.cs:line 74
   at Infrastructure.Services.MBFSmsBranchnameService.SendAccountRegistrationSMSOtpAsync(AccountCreationSMSOtp req) in C:\Users\<USER>\source\repos\ZenPay-ZenShop-API\Infrastructure\Services\MBFSmsBranchnameService.cs:line 142
[2025-06-17 16:14:29 INF] 📩 Sending OTP SMS to **********
[2025-06-17 17:09:50 INF] 📩 Sending OTP SMS to **********
[2025-06-17 17:09:51 ERR] ❌ Failed to send OTP SMS to **********
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Infrastructure.Services.MBFSmsBranchnameService.SendSmsInternalAsync(String phone, String message, String unicode, Boolean isRetry, String useCase) in C:\Users\<USER>\source\repos\ZenPay-ZenShop-API\Infrastructure\Services\MBFSmsBranchnameService.cs:line 74
   at Infrastructure.Services.MBFSmsBranchnameService.SendAccountRegistrationSMSOtpAsync(AccountCreationSMSOtp req) in C:\Users\<USER>\source\repos\ZenPay-ZenShop-API\Infrastructure\Services\MBFSmsBranchnameService.cs:line 142
[2025-06-17 17:10:02 INF] 📩 Sending OTP SMS to **********
[2025-06-17 17:12:48 INF] 📩 Sending OTP SMS to **********
[2025-06-17 17:13:09 INF] 📩 SMS gửi đến **********, Success=true, UseCase=AccountRegistrationOtp, TraceId=0HNDDGTB00FPO:********
[2025-06-17 17:13:20 INF] 📩 Sending OTP SMS to **********
[2025-06-17 17:13:20 INF] 📩 SMS gửi đến **********, Success=false, UseCase=AccountRegistrationOtp, TraceId=0HNDDGTB00FPO:********
[2025-06-17 17:13:41 INF] 📩 Sending OTP SMS to **********
[2025-06-17 17:15:49 INF] 📩 SMS gửi đến **********, Success=false, UseCase=AccountRegistrationOtp, TraceId=0HNDDGTB00FPO:0000000B
[2025-06-17 17:16:06 INF] 📩 Sending OTP SMS to **********
[2025-06-17 17:17:33 INF] 📩 SMS gửi đến **********, Success=false, UseCase=AccountRegistrationOtp, TraceId=0HNDDGTB00FPO:0000000D
[2025-06-17 17:17:53 INF] 📩 Sending OTP SMS to **********
[2025-06-17 17:21:11 INF] 📩 SMS gửi đến **********, Success=true, UseCase=AccountRegistrationOtp, TraceId=0HNDDGTB00FPO:0000000F
[2025-06-17 17:38:08 INF] 📩 Sending confirmation SMS to **********
[2025-06-17 17:38:10 INF] 📩 SMS gửi đến **********, Success=true, UseCase=AccountCreationConfirmation, TraceId=0HNDDHBLR83O9:********
[2025-06-17 17:48:27 INF] 🔐 [Login] Login attempt for ClientId: RONGVIET
[2025-06-17 17:48:28 INF] ✅ [Login] Token generated for ClientId: RONGVIET
[2025-06-17 17:49:01 INF] 📩 Sending OTP SMS to **********
[2025-06-17 17:49:03 INF] 📩 SMS gửi đến **********, Success=true, UseCase=AccountRegistrationOtp, TraceId=0HNDDHHHO1ROC:********
