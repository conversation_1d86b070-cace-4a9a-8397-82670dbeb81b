[2025-07-02 00:38:50 INF] Attempting to login to MobiFone Invoice API for user: <EMAIL>
[2025-07-02 00:38:50 INF] Calling MobiFone Login API for Username: <EMAIL> - Environment: Test
[2025-07-02 00:38:51 INF] MobiFone Login Response: "OK" - 343 chars
[2025-07-02 00:38:51 INF] MobiFone Login API call completed successfully
[2025-07-02 00:39:19 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-02 00:39:19 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-02 00:39:20 INF] MobiFone CreateInvoice Response: "OK" - 87 chars
[2025-07-02 00:39:20 INF] MobiFone CreateInvoice API call completed successfully
[2025-07-02 00:39:20 INF] Successfully created invoice with response: 1 items
[2025-07-02 00:40:13 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-02 00:40:13 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-02 00:40:16 INF] MobiFone CreateInvoice Response: "OK" - 2843 chars
[2025-07-02 00:40:16 INF] MobiFone CreateInvoice API call completed successfully
[2025-07-02 00:40:16 INF] Successfully created invoice with response: 1 items
[2025-07-02 00:49:06 ERR] Unexpected exception in TimingPipeFlusher.FlushAsync.
System.IO.IOException: The encryption operation failed, see inner exception.
 ---> System.ComponentModel.Win32Exception (14): Bad address
   --- End of inner exception stack trace ---
   at System.Net.Security.SslStream.WriteSingleChunk[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.ConcurrentPipeWriter.FlushAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.TimingPipeFlusher.FlushAsync(MinDataRate minRate, Int64 count, IHttpOutputAborter outputAborter, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2FrameWriter.WriteGoAwayAsync(Int32 lastStreamId, Http2ErrorCode errorCode)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.UpdateConnectionState()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.ProcessRequestsAsync[TContext](IHttpApplication`1 application)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecuteFromThreadPool(Thread threadPoolThread)
   at System.Threading.ThreadPoolWorkQueue.Dispatch()
   at System.Threading.PortableThreadPool.WorkerThread.WorkerThreadStart()
   at System.Threading.Thread.StartCallback()
--- End of stack trace from previous location ---
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.ConcurrentPipeWriter.FlushAsyncAwaited(ValueTask`1 flushTask, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.TimingPipeFlusher.TimeFlushAsyncAwaited(ValueTask`1 pipeFlushTask, MinDataRate minRate, IHttpOutputAborter outputAborter, CancellationToken cancellationToken)
[2025-07-02 00:49:06 ERR] Unhandled exception while processing 0HNDOOOBA4GSF.
System.IO.IOException: The encryption operation failed, see inner exception.
 ---> System.ComponentModel.Win32Exception (14): Bad address
   --- End of inner exception stack trace ---
   at System.Net.Security.SslStream.WriteSingleChunk[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Threading.Tasks.Task`1.TrySetResult(TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.HttpConnection.ProcessRequestsAsync[TContext](IHttpApplication`1 httpApplication)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Threading.Tasks.Task`1.TrySetResult(TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.ProcessRequestsAsync[TContext](IHttpApplication`1 application)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Threading.Tasks.Task`1.TrySetResult(TResult result)
   at System.Threading.Tasks.UnwrapPromise`1.TrySetFromTask(Task task, Boolean lookForOce)
   at System.Threading.Tasks.UnwrapPromise`1.Invoke(Task completingTask)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Threading.Tasks.Task`1.TrySetResult(TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2FrameWriter.WriteToOutputPipe()
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.ThreadPoolWorkQueue.Dispatch()
   at System.Threading.PortableThreadPool.WorkerThread.WorkerThreadStart()
   at System.Threading.Thread.StartCallback()
--- End of stack trace from previous location ---
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.KestrelConnection`1.ExecuteAsync()
[2025-07-02 00:54:33 INF] Creating GTGT MTT invoice with EditMode: 0, Data count: 1
[2025-07-02 00:54:33 INF] Calling MobiFone CreateGTGTMTTInvoice API - EditMode: 0
[2025-07-02 00:54:33 INF] MobiFone CreateGTGTMTTInvoice Response: "Unauthorized" - 61 chars
[2025-07-02 00:54:33 WRN] MobiFone CreateGTGTMTTInvoice API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-07-02 00:54:33 ERR] Error occurred while creating GTGT MTT invoice in MobiFone Invoice API: MobiFone CreateGTGTMTTInvoice API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone CreateGTGTMTTInvoice API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 109
   at Infrastructure.Services.MobiFoneInvoiceService.TaoMoiHoaDonGTGTMayTinhTienSinhMaAsync(SaveListHoadon78MTTRequest request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 641
[2025-07-02 00:55:13 INF] Creating GTGT MTT invoice with EditMode: 1, Data count: 1
[2025-07-02 00:55:13 INF] Calling MobiFone CreateGTGTMTTInvoice API - EditMode: 1
[2025-07-02 00:55:18 INF] MobiFone CreateGTGTMTTInvoice Response: "OK" - 2861 chars
[2025-07-02 00:55:18 ERR] Error occurred while creating GTGT MTT invoice in MobiFone Invoice API: The JSON value could not be converted to System.String. Path: $[0] | LineNumber: 0 | BytePositionInLine: 2.
System.Text.Json.JsonException: The JSON value could not be converted to System.String. Path: $[0] | LineNumber: 0 | BytePositionInLine: 2.
 ---> System.InvalidOperationException: Cannot get the value of a token type 'StartObject' as a string.
   at System.Text.Json.ThrowHelper.ThrowInvalidOperationException_ExpectedString(JsonTokenType tokenType)
   at System.Text.Json.Utf8JsonReader.GetString()
   at System.Text.Json.Serialization.Converters.StringConverter.Read(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options)
   at System.Text.Json.Serialization.JsonCollectionConverter`2.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, TCollection& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, Utf8JsonReader& reader, Exception ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 126
   at Infrastructure.Services.MobiFoneInvoiceService.TaoMoiHoaDonGTGTMayTinhTienSinhMaAsync(SaveListHoadon78MTTRequest request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 641
[2025-07-02 00:55:28 INF] Creating GTGT MTT invoice with EditMode: 1, Data count: 1
[2025-07-02 00:55:28 INF] Calling MobiFone CreateGTGTMTTInvoice API - EditMode: 1
[2025-07-02 04:04:53 INF] MobiFone CreateGTGTMTTInvoice Response: "OK" - 2861 chars
[2025-07-02 04:04:53 ERR] Error occurred while creating GTGT MTT invoice in MobiFone Invoice API: The JSON value could not be converted to System.String. Path: $[0] | LineNumber: 0 | BytePositionInLine: 2.
System.Text.Json.JsonException: The JSON value could not be converted to System.String. Path: $[0] | LineNumber: 0 | BytePositionInLine: 2.
 ---> System.InvalidOperationException: Cannot get the value of a token type 'StartObject' as a string.
   at System.Text.Json.ThrowHelper.ThrowInvalidOperationException_ExpectedString(JsonTokenType tokenType)
   at System.Text.Json.Utf8JsonReader.GetString()
   at System.Text.Json.Serialization.Converters.StringConverter.Read(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options)
   at System.Text.Json.Serialization.JsonCollectionConverter`2.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, TCollection& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, Utf8JsonReader& reader, Exception ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 126
   at Infrastructure.Services.MobiFoneInvoiceService.TaoMoiHoaDonGTGTMayTinhTienSinhMaAsync(SaveListHoadon78MTTRequest request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 641
[2025-07-02 04:04:54 INF] Getting list certificates file 68
[2025-07-02 04:04:54 INF] Calling MobiFone GetListCertificatesFile68 API
[2025-07-02 04:04:57 INF] MobiFone GetListCertificatesFile68 Response: "OK" - 2316 chars
[2025-07-02 04:04:57 INF] MobiFone GetListCertificatesFile68 API call completed successfully
[2025-07-02 04:04:57 INF] Successfully retrieved 4 certificates
[2025-07-02 04:05:34 INF] Getting data references with RefId: RF00059
[2025-07-02 04:05:34 INF] Calling MobiFone GetDataReferences API - RefId: RF00059
[2025-07-02 04:05:44 INF] MobiFone GetDataReferences Response: "OK" - 87237 chars
[2025-07-02 04:05:44 INF] MobiFone GetDataReferences API call completed successfully
[2025-07-02 04:05:44 INF] Successfully retrieved 147 invoice templates
[2025-07-02 04:12:33 INF] Creating GTGT MTT invoice with EditMode: 1, Data count: 1
[2025-07-02 04:12:33 INF] Calling MobiFone CreateGTGTMTTInvoice API - EditMode: 1
[2025-07-02 04:14:55 INF] MobiFone CreateGTGTMTTInvoice Response: "OK" - 2861 chars
[2025-07-02 04:15:19 ERR] Error occurred while creating GTGT MTT invoice in MobiFone Invoice API: The JSON value could not be converted to System.String. Path: $[0] | LineNumber: 0 | BytePositionInLine: 2.
System.Text.Json.JsonException: The JSON value could not be converted to System.String. Path: $[0] | LineNumber: 0 | BytePositionInLine: 2.
 ---> System.InvalidOperationException: Cannot get the value of a token type 'StartObject' as a string.
   at System.Text.Json.ThrowHelper.ThrowInvalidOperationException_ExpectedString(JsonTokenType tokenType)
   at System.Text.Json.Utf8JsonReader.GetString()
   at System.Text.Json.Serialization.Converters.StringConverter.Read(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options)
   at System.Text.Json.Serialization.JsonCollectionConverter`2.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, TCollection& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, Utf8JsonReader& reader, Exception ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 126
   at Infrastructure.Services.MobiFoneInvoiceService.TaoMoiHoaDonGTGTMayTinhTienSinhMaAsync(SaveListHoadon78MTTRequest request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 641
[2025-07-02 04:16:19 INF] SaveAndSignHoadon78 with EditMode: 1, TypeCmd: 200, GuiCQT: Notsend, Data count: 1
[2025-07-02 04:16:19 INF] Calling MobiFone SaveAndSignHoadon78 API - EditMode: 1, TypeCmd: 200
[2025-07-02 04:16:26 INF] MobiFone SaveAndSignHoadon78 Response: "OK" - 46 chars
[2025-07-02 04:16:26 INF] MobiFone SaveAndSignHoadon78 API call completed successfully
[2025-07-02 04:16:26 INF] Successfully executed SaveAndSignHoadon78 with response: 1 items
[2025-07-02 04:16:59 INF] SaveAndSignHoadon78 with EditMode: 1, TypeCmd: 206, GuiCQT: Notsend, Data count: 1
[2025-07-02 04:16:59 INF] Calling MobiFone SaveAndSignHoadon78 API - EditMode: 1, TypeCmd: 206
[2025-07-02 04:17:04 INF] MobiFone SaveAndSignHoadon78 Response: "Unauthorized" - 61 chars
[2025-07-02 04:17:04 WRN] MobiFone SaveAndSignHoadon78 API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-07-02 04:17:04 ERR] Error occurred while executing SaveAndSignHoadon78 in MobiFone Invoice API: MobiFone SaveAndSignHoadon78 API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone SaveAndSignHoadon78 API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 109
   at Infrastructure.Services.MobiFoneInvoiceService.SaveAndSignHoadon78Async(SaveAndSignHoadon78Request request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 772
[2025-07-02 04:17:12 INF] SaveAndSignHoadon78 with EditMode: 1, TypeCmd: 206, GuiCQT: Notsend, Data count: 1
[2025-07-02 04:17:12 INF] Calling MobiFone SaveAndSignHoadon78 API - EditMode: 1, TypeCmd: 206
[2025-07-02 04:17:13 INF] MobiFone SaveAndSignHoadon78 Response: "OK" - 87 chars
[2025-07-02 04:17:13 INF] MobiFone SaveAndSignHoadon78 API call completed successfully
[2025-07-02 04:17:13 INF] Successfully executed SaveAndSignHoadon78 with response: 1 items
[2025-07-02 04:17:19 INF] SaveAndSignHoadon78 with EditMode: 1, TypeCmd: 206, GuiCQT: Notsend, Data count: 1
[2025-07-02 04:17:19 INF] Calling MobiFone SaveAndSignHoadon78 API - EditMode: 1, TypeCmd: 206
[2025-07-02 04:17:28 INF] MobiFone SaveAndSignHoadon78 Response: "OK" - 87 chars
[2025-07-02 04:17:28 INF] MobiFone SaveAndSignHoadon78 API call completed successfully
[2025-07-02 04:17:28 INF] Successfully executed SaveAndSignHoadon78 with response: 1 items
[2025-07-02 04:17:44 INF] SaveAndSignHoadon78 with EditMode: 1, TypeCmd: 206, GuiCQT: Notsend, Data count: 1
[2025-07-02 04:17:44 INF] Calling MobiFone SaveAndSignHoadon78 API - EditMode: 1, TypeCmd: 206
[2025-07-02 04:18:11 INF] MobiFone SaveAndSignHoadon78 Response: "OK" - 2998 chars
[2025-07-02 04:18:11 INF] MobiFone SaveAndSignHoadon78 API call completed successfully
[2025-07-02 04:18:11 INF] Successfully executed SaveAndSignHoadon78 with response: 1 items
[2025-07-02 04:23:30 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-02 04:23:30 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-02 04:24:55 INF] MobiFone CreateInvoice Response: "OK" - 47 chars
[2025-07-02 04:24:55 INF] MobiFone CreateInvoice API call completed successfully
[2025-07-02 04:24:55 INF] Successfully created invoice with response: 1 items
[2025-07-02 04:25:00 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-02 04:25:00 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-02 04:26:08 INF] MobiFone CreateInvoice Response: "OK" - 58 chars
[2025-07-02 04:26:08 INF] MobiFone CreateInvoice API call completed successfully
[2025-07-02 04:26:08 INF] Successfully created invoice with response: 1 items
[2025-07-02 04:26:23 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-02 04:26:23 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-02 04:26:58 INF] MobiFone CreateInvoice Response: "OK" - 47 chars
[2025-07-02 04:26:58 INF] MobiFone CreateInvoice API call completed successfully
[2025-07-02 04:26:58 INF] Successfully created invoice with response: 1 items
[2025-07-02 04:27:00 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-02 04:27:00 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-02 04:27:15 INF] MobiFone CreateInvoice Response: "OK" - 2843 chars
[2025-07-02 04:27:15 INF] MobiFone CreateInvoice API call completed successfully
[2025-07-02 04:27:15 INF] Successfully created invoice with response: 1 items
[2025-07-02 04:27:38 INF] Getting list certificates file 68
[2025-07-02 04:27:38 INF] Calling MobiFone GetListCertificatesFile68 API
[2025-07-02 04:27:40 INF] MobiFone GetListCertificatesFile68 Response: "OK" - 2316 chars
[2025-07-02 04:27:40 INF] MobiFone GetListCertificatesFile68 API call completed successfully
[2025-07-02 04:27:40 INF] Successfully retrieved 4 certificates
[2025-07-02 04:31:56 INF] SignInvoiceCertFile68 with Data count: 1
[2025-07-02 04:31:56 INF] Calling MobiFone SignInvoiceCertFile68 API - Data count: 1
[2025-07-02 04:32:08 INF] MobiFone SignInvoiceCertFile68 Response: "OK" - 27 chars
[2025-07-02 04:32:08 INF] MobiFone SignInvoiceCertFile68 API call completed successfully
[2025-07-02 04:32:08 INF] Successfully executed SignInvoiceCertFile68 with status: Đã ký
[2025-07-02 04:32:31 INF] GetHistoryInvoice for invoice ID: 02c9f9ce-ba97-4fc3-ac7e-413f3d00c967
[2025-07-02 04:32:31 INF] Calling MobiFone GetHistoryInvoice API for invoice: 02c9f9ce-ba97-4fc3-ac7e-413f3d00c967
[2025-07-02 04:32:53 INF] MobiFone GetHistoryInvoice Response: "Unauthorized" - 61 chars
[2025-07-02 04:32:53 WRN] MobiFone GetHistoryInvoice API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-07-02 04:32:53 ERR] Error occurred while getting history for invoice 02c9f9ce-ba97-4fc3-ac7e-413f3d00c967 in MobiFone Invoice API: MobiFone GetHistoryInvoice API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone GetHistoryInvoice API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 109
   at Infrastructure.Services.MobiFoneInvoiceService.GetHistoryInvoiceAsync(String id, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 960
[2025-07-02 04:32:59 INF] GetHistoryInvoice for invoice ID: 02c9f9ce-ba97-4fc3-ac7e-413f3d00c967
[2025-07-02 04:32:59 INF] Calling MobiFone GetHistoryInvoice API for invoice: 02c9f9ce-ba97-4fc3-ac7e-413f3d00c967
[2025-07-02 04:33:41 INF] MobiFone GetHistoryInvoice Response: "OK" - 2 chars
[2025-07-02 04:33:41 INF] MobiFone GetHistoryInvoice API call completed successfully
[2025-07-02 04:33:41 INF] Successfully retrieved history for invoice 02c9f9ce-ba97-4fc3-ac7e-413f3d00c967 with 0 records
[2025-07-02 04:33:41 INF] GetInvoiceById for invoice ID: 02c9f9ce-ba97-4fc3-ac7e-413f3d00c967
[2025-07-02 04:33:45 INF] MobiFone GetInvoiceById Response: "Unauthorized" - 61 chars
[2025-07-02 04:33:45 WRN] MobiFone GetInvoiceById API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-07-02 04:33:45 ERR] Error occurred while getting invoice by ID 02c9f9ce-ba97-4fc3-ac7e-413f3d00c967 in MobiFone Invoice API: MobiFone GetInvoiceById API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone GetInvoiceById API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 109
   at Infrastructure.Services.MobiFoneInvoiceService.GetInvoiceByIdAsync(GetInvoiceByIdRequest request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 1247
[2025-07-02 04:34:00 INF] GetInvoiceById for invoice ID: 02c9f9ce-ba97-4fc3-ac7e-413f3d00c967
[2025-07-02 04:34:16 INF] MobiFone GetInvoiceById Response: "OK" - 3486 chars
[2025-07-02 04:34:16 INF] MobiFone GetInvoiceById API call completed successfully
[2025-07-02 04:34:16 INF] Successfully retrieved invoice details for 02c9f9ce-ba97-4fc3-ac7e-413f3d00c967
[2025-07-02 04:34:31 INF] GetInvoiceById for invoice ID: 02c9f9ce-ba97-4fc3-ac7e-413f3d00c967
[2025-07-02 04:34:57 INF] MobiFone GetInvoiceById Response: "OK" - 3486 chars
[2025-07-02 04:34:57 INF] MobiFone GetInvoiceById API call completed successfully
[2025-07-02 04:34:57 INF] Successfully retrieved invoice details for 02c9f9ce-ba97-4fc3-ac7e-413f3d00c967
[2025-07-02 04:35:04 INF] GetHistoryInvoice for invoice ID: 02c9f9ce-ba97-4fc3-ac7e-413f3d00c967
[2025-07-02 04:35:04 INF] Calling MobiFone GetHistoryInvoice API for invoice: 02c9f9ce-ba97-4fc3-ac7e-413f3d00c967
[2025-07-02 04:35:11 INF] MobiFone GetHistoryInvoice Response: "OK" - 2 chars
[2025-07-02 04:35:11 INF] MobiFone GetHistoryInvoice API call completed successfully
[2025-07-02 04:35:11 INF] Successfully retrieved history for invoice 02c9f9ce-ba97-4fc3-ac7e-413f3d00c967 with 0 records
[2025-07-02 04:43:47 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-02 04:43:47 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-02 04:44:45 INF] MobiFone CreateInvoice Response: "OK" - 2843 chars
[2025-07-02 04:44:45 INF] MobiFone CreateInvoice API call completed successfully
[2025-07-02 04:44:45 INF] Successfully created invoice with response: 1 items
[2025-07-02 04:45:11 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-02 04:45:11 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-02 04:55:32 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-02 04:55:32 INF] Generated cURL command for MobiFone CreateInvoice API: curl -X POST 'http://mobiinvoice.vn:9000/api/Invoice68/SaveListHoadon78' -H 'User-Agent: ZenInvoice/1.0' -H 'Authorization: Bearer MXg0ck1JTVF2eU9helRQM3haaWdERFBvaDA4YUF0VFlSQmpBeDhTLzJqRT06VFJVTkdRVU9DTkdVWUBHTUFJTC5DT006NjM4ODcwMTM1MzExOTU1ODU3OkNUS1Yy' -H 'Content-Type: application/json; charset=utf-8' -d '{
  "editmode": 1,
  "data": [
    {
      "TGTKCThue": null,
      "TGTKhac": null,
      "details": [
        {
          "data": [
            {
              "tsuat": "4",
              "stt": "1",
              "ma": "Tra sua",
              "ten": "Ly tr\u00E0 s\u1EEFa l\u00E0i",
              "mdvtinh": "Ly",
              "dvtinh": null,
              "dgia": 50000,
              "sluong": 1,
              "tlckhau": 0,
              "stckhau": 0,
              "thtien": 50000,
              "tthue": 2000,
              "tgtien": 52000,
              "kmai": 1,
              "lhhdthu": null,
              "skhung": null,
              "smay": null,
              "bksptvchuyen": null,
              "tnghang": null,
              "dcnghang": null,
              "mstnghang": null,
              "mddnghang": null
            }
          ]
        }
      ],
      "hoadon68_phi": null,
      "hoadon68_khac": null,
      "cctbao_id": "791f4ff1-ce04-4941-9ad6-5343401ca6c1",
      "hdon_id": null,
      "nlap": "2025-07-02",
      "sdhang": "",
      "khieu": "1C25MYY",
      "shdon": 0,
      "tthai": null,
      "tthdon": null,
      "mccqthue": null,
      "sbmat": null,
      "dvtte": "VND",
      "tgia": 1,
      "htttoan": "Ti\u1EC1n m\u1EB7t/Chuy\u1EC3n kho\u1EA3n",
      "sdtnban": null,
      "stknban": "",
      "tnhban": "",
      "mnmua": "",
      "mst": "",
      "tnmua": "",
      "email": "",
      "ten": "",
      "dchi": "",
      "stknmua": "",
      "sdtnmua": "",
      "tnhmua": "",
      "tchang": null,
      "mchang": null,
      "mdvqhnsach_mua": null,
      "shchieu": null,
      "tgtcthue": 50000,
      "tgtthue": 2000,
      "tgtttbso": 52000,
      "tgtttbso_last": 52000,
      "tkcktmn": 0,
      "tgtphi": 0,
      "mdvi": "TRUNGDEMO",
      "is_hdcma": 1,
      "hdon_id_old": null,
      "lhdclquan": null,
      "khmshdclquan": null,
      "khhdclquan": null,
      "shdclquan": null,
      "nlhdclquan": null
    }
  ]
}' 
[2025-07-02 04:55:32 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-02 04:58:50 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-02 04:58:50 INF] Generated cURL command for MobiFone CreateInvoice API: curl -X POST 'http://mobiinvoice.vn:9000/api/Invoice68/SaveListHoadon78' -H 'User-Agent: ZenInvoice/1.0' -H 'Authorization: Bearer MXg0ck1JTVF2eU9helRQM3haaWdERFBvaDA4YUF0VFlSQmpBeDhTLzJqRT06VFJVTkdRVU9DTkdVWUBHTUFJTC5DT006NjM4ODcwMTM1MzExOTU1ODU3OkNUS1Yy;CTKV2' -H 'Content-Type: application/json; charset=utf-8' -d '{
  "editmode": 1,
  "data": [
    {
      "TGTKCThue": null,
      "TGTKhac": null,
      "details": [
        {
          "data": [
            {
              "tsuat": "4",
              "stt": "1",
              "ma": "Tra sua",
              "ten": "Ly tr\u00E0 s\u1EEFa l\u00E0i",
              "mdvtinh": "Ly",
              "dvtinh": null,
              "dgia": 50000,
              "sluong": 1,
              "tlckhau": 0,
              "stckhau": 0,
              "thtien": 50000,
              "tthue": 2000,
              "tgtien": 52000,
              "kmai": 1,
              "lhhdthu": null,
              "skhung": null,
              "smay": null,
              "bksptvchuyen": null,
              "tnghang": null,
              "dcnghang": null,
              "mstnghang": null,
              "mddnghang": null
            }
          ]
        }
      ],
      "hoadon68_phi": null,
      "hoadon68_khac": null,
      "cctbao_id": "791f4ff1-ce04-4941-9ad6-5343401ca6c1",
      "hdon_id": null,
      "nlap": "2025-07-02",
      "sdhang": "",
      "khieu": "1C25MYY",
      "shdon": 0,
      "tthai": null,
      "tthdon": null,
      "mccqthue": null,
      "sbmat": null,
      "dvtte": "VND",
      "tgia": 1,
      "htttoan": "Ti\u1EC1n m\u1EB7t/Chuy\u1EC3n kho\u1EA3n",
      "sdtnban": null,
      "stknban": "",
      "tnhban": "",
      "mnmua": "",
      "mst": "",
      "tnmua": "",
      "email": "",
      "ten": "",
      "dchi": "",
      "stknmua": "",
      "sdtnmua": "",
      "tnhmua": "",
      "tchang": null,
      "mchang": null,
      "mdvqhnsach_mua": null,
      "shchieu": null,
      "tgtcthue": 50000,
      "tgtthue": 2000,
      "tgtttbso": 52000,
      "tgtttbso_last": 52000,
      "tkcktmn": 0,
      "tgtphi": 0,
      "mdvi": "TRUNGDEMO",
      "is_hdcma": 1,
      "hdon_id_old": null,
      "lhdclquan": null,
      "khmshdclquan": null,
      "khhdclquan": null,
      "shdclquan": null,
      "nlhdclquan": null
    }
  ]
}' 
[2025-07-02 04:58:50 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-02 05:02:50 INF] MobiFone CreateInvoice Response: "OK" - 2843 chars
[2025-07-02 05:02:50 INF] MobiFone CreateInvoice API call completed successfully
[2025-07-02 05:02:50 INF] Successfully created invoice with response: 1 items
[2025-07-02 05:10:12 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-02 05:10:12 INF] Generated cURL command for MobiFone CreateInvoice API: curl -X POST 'http://mobiinvoice.vn:9000/api/Invoice68/SaveListHoadon78' -H 'User-Agent: ZenInvoice/1.0' -H 'Authorization: Bearer MXg0ck1JTVF2eU9helRQM3haaWdERFBvaDA4YUF0VFlSQmpBeDhTLzJqRT06VFJVTkdRVU9DTkdVWUBHTUFJTC5DT006NjM4ODcwMTM1MzExOTU1ODU3OkNUS1Yy;CTKV2' -H 'Content-Type: application/json; charset=utf-8' -d '{
  "editmode": 1,
  "data": [
    {
      "TGTKCThue": null,
      "TGTKhac": null,
      "details": [
        {
          "data": [
            {
              "tsuat": "4",
              "stt": "1",
              "ma": "Tra sua",
              "ten": "Ly tr\u00E0 s\u1EEFa l\u00E0i",
              "mdvtinh": "Ly",
              "dvtinh": null,
              "dgia": 50000,
              "sluong": 1,
              "tlckhau": 0,
              "stckhau": 0,
              "thtien": 50000,
              "tthue": 2000,
              "tgtien": 52000,
              "kmai": 1,
              "lhhdthu": null,
              "skhung": null,
              "smay": null,
              "bksptvchuyen": null,
              "tnghang": null,
              "dcnghang": null,
              "mstnghang": null,
              "mddnghang": null
            }
          ]
        }
      ],
      "hoadon68_phi": null,
      "hoadon68_khac": null,
      "cctbao_id": "791f4ff1-ce04-4941-9ad6-5343401ca6c1",
      "hdon_id": null,
      "nlap": "2025-07-02",
      "sdhang": "",
      "khieu": "1C25MYY",
      "shdon": 0,
      "tthai": null,
      "tthdon": null,
      "mccqthue": null,
      "sbmat": null,
      "dvtte": "VND",
      "tgia": 1,
      "htttoan": "Ti\u1EC1n m\u1EB7t/Chuy\u1EC3n kho\u1EA3n",
      "sdtnban": null,
      "stknban": "",
      "tnhban": "",
      "mnmua": "",
      "mst": "",
      "tnmua": "",
      "email": "",
      "ten": "",
      "dchi": "",
      "stknmua": "",
      "sdtnmua": "",
      "tnhmua": "",
      "tchang": null,
      "mchang": null,
      "mdvqhnsach_mua": null,
      "shchieu": null,
      "tgtcthue": 50000,
      "tgtthue": 2000,
      "tgtttbso": 52000,
      "tgtttbso_last": 52000,
      "tkcktmn": 0,
      "tgtphi": 0,
      "mdvi": "TRUNGDEMO",
      "is_hdcma": 1,
      "hdon_id_old": null,
      "lhdclquan": null,
      "khmshdclquan": null,
      "khhdclquan": null,
      "shdclquan": null,
      "nlhdclquan": null
    }
  ]
}' 
[2025-07-02 05:10:12 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-02 06:45:07 INF] MobiFone CreateInvoice Response: "OK" - 2843 chars
[2025-07-02 06:45:07 INF] MobiFone CreateInvoice API call completed successfully
[2025-07-02 06:45:07 INF] Successfully created invoice with response: 1 items
[2025-07-02 08:02:36 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-02 08:02:36 INF] Generated cURL command for MobiFone CreateInvoice API: curl -X POST 'http://mobiinvoice.vn:9000/api/Invoice68/SaveListHoadon78' -H 'User-Agent: ZenInvoice/1.0' -H 'Authorization: Bearer MXg0ck1JTVF2eU9helRQM3haaWdERFBvaDA4YUF0VFlSQmpBeDhTLzJqRT06VFJVTkdRVU9DTkdVWUBHTUFJTC5DT006NjM4ODcwMTM1MzExOTU1ODU3OkNUS1Yy;CTKV2' -H 'Content-Type: application/json; charset=utf-8' -d '{
  "editmode": 1,
  "data": [
    {
      "TGTKCThue": null,
      "TGTKhac": null,
      "details": [
        {
          "data": [
            {
              "tsuat": "4",
              "stt": "1",
              "ma": "Tra sua",
              "ten": "Ly tr\u00E0 s\u1EEFa l\u00E0i",
              "mdvtinh": "Ly",
              "dvtinh": null,
              "dgia": 50000,
              "sluong": 1,
              "tlckhau": 0,
              "stckhau": 0,
              "thtien": 50000,
              "tthue": 2000,
              "tgtien": 52000,
              "kmai": 1,
              "lhhdthu": null,
              "skhung": null,
              "smay": null,
              "bksptvchuyen": null,
              "tnghang": null,
              "dcnghang": null,
              "mstnghang": null,
              "mddnghang": null
            }
          ]
        }
      ],
      "hoadon68_phi": null,
      "hoadon68_khac": null,
      "cctbao_id": "791f4ff1-ce04-4941-9ad6-5343401ca6c1",
      "hdon_id": null,
      "nlap": "2025-07-02",
      "sdhang": "",
      "khieu": "1C25MYY",
      "shdon": 0,
      "tthai": null,
      "tthdon": null,
      "mccqthue": null,
      "sbmat": null,
      "dvtte": "VND",
      "tgia": 1,
      "htttoan": "Ti\u1EC1n m\u1EB7t/Chuy\u1EC3n kho\u1EA3n",
      "sdtnban": null,
      "stknban": "",
      "tnhban": "",
      "mnmua": "",
      "mst": "",
      "tnmua": "",
      "email": "",
      "ten": "",
      "dchi": "",
      "stknmua": "",
      "sdtnmua": "",
      "tnhmua": "",
      "tchang": null,
      "mchang": null,
      "mdvqhnsach_mua": null,
      "shchieu": null,
      "tgtcthue": 50000,
      "tgtthue": 2000,
      "tgtttbso": 52000,
      "tgtttbso_last": 52000,
      "tkcktmn": 0,
      "tgtphi": 0,
      "mdvi": "TRUNGDEMO",
      "is_hdcma": 1,
      "hdon_id_old": null,
      "lhdclquan": null,
      "khmshdclquan": null,
      "khhdclquan": null,
      "shdclquan": null,
      "nlhdclquan": null
    }
  ]
}' 
[2025-07-02 08:02:36 INF] Calling MobiFone CreateInvoice API - EditMode: 1
