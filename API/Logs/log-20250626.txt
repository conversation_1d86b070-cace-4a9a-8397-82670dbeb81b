[2025-06-26 00:11:56 ERR] ❌ Unhandled exception. TraceId: 0HNDK1D7JVU6U:00000013
System.NullReferenceException: Object reference not set to an instance of an object.
   at Applications.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Applications/Behaviors/ValidationBehavior.cs:line 42
   at API.Controllers.MobiFoneInvoiceController.GetHistoryInvoiceAsync(String id, String token, String maDvcs) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Controllers/MobiFoneInvoiceController.cs:line 393
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at API.Middlewares.SerilogEnrichMiddleware.Invoke(HttpContext context) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/SerilogEnrichMiddleware.cs:line 24
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at ExceptionHandlingMiddleware.Invoke(HttpContext context) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/ExceptionHandlingMiddleware.cs:line 25
[2025-06-26 00:12:49 INF] GetHistoryInvoice for invoice ID: 4838d6a5-c32a-442f-9fc4-3771186b097d
[2025-06-26 00:12:49 INF] Calling MobiFone GetHistoryInvoice API for invoice: 4838d6a5-c32a-442f-9fc4-3771186b097d
[2025-06-26 00:12:49 INF] MobiFone GetHistoryInvoice Response: "OK" - 1070 chars
[2025-06-26 00:12:49 INF] MobiFone GetHistoryInvoice API call completed successfully
[2025-06-26 00:12:49 INF] Successfully retrieved history for invoice 4838d6a5-c32a-442f-9fc4-3771186b097d with 4 records
