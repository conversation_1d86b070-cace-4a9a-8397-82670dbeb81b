[2025-06-18 12:02:27 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 12:02:28 INF] 📩 SMS gửi đến **********, Success=false, UseCase=AccountCreationConfirmation, TraceId=0HNDE4KJFS1GS:********
[2025-06-18 12:02:53 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 12:02:53 INF] 📩 SMS gửi đến **********, Success=false, UseCase=AccountCreationConfirmation, TraceId=0HNDE4KJFS1GS:********
[2025-06-18 12:03:05 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 12:03:27 ERR] ❌ Failed to send confirmation SMS to **********
System.Net.Http.HttpRequestException: An error occurred while sending the request.
 ---> System.IO.IOException: Unable to read data from the transport connection: An existing connection was forcibly closed by the remote host..
 ---> System.Net.Sockets.SocketException (10054): An existing connection was forcibly closed by the remote host.
   --- End of inner exception stack trace ---
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource<System.Int32>.GetResult(Int16 token)
   at System.Net.Http.HttpConnection.<EnsureReadAheadTaskHasStarted>g__ReadAheadWithZeroByteReadAsync|40_0()
   at System.Net.Http.HttpConnection.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnection.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at Infrastructure.Services.MBFSmsBranchnameService.SendSmsInternalAsync(String phone, String message, String unicode, Boolean isRetry, String useCase) in C:\Users\<USER>\source\repos\ZenPay-ZenShop-API\Infrastructure\Services\MBFSmsBranchnameService.cs:line 74
   at Infrastructure.Services.MBFSmsBranchnameService.SendAccountCreationUnicodeSMSConfirmationAsync(AccountCreationSMSConfirmation req) in C:\Users\<USER>\source\repos\ZenPay-ZenShop-API\Infrastructure\Services\MBFSmsBranchnameService.cs:line 192
[2025-06-18 12:03:32 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 12:03:33 INF] 📩 SMS gửi đến **********, Success=false, UseCase=AccountCreationConfirmation, TraceId=0HNDE4KJFS1GS:********
[2025-06-18 12:05:12 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 12:05:13 INF] 📩 SMS gửi đến **********, Success=false, UseCase=AccountCreationConfirmation, TraceId=0HNDE4M9BRS92:********
[2025-06-18 12:05:27 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 12:05:27 INF] 📩 SMS gửi đến **********, Success=true, UseCase=AccountCreationConfirmation, TraceId=0HNDE4M9BRS92:********
[2025-06-18 12:11:34 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 12:11:35 INF] 📩 SMS gửi đến **********, Success=false, UseCase=AccountCreationConfirmation, TraceId=0HNDE4PUQJ7C1:********
[2025-06-18 12:24:31 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 12:30:02 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 12:30:03 INF] 📩 SMS gửi đến **********, Success=false, UseCase=AccountCreationConfirmation, TraceId=0HNDE545UURNQ:********
[2025-06-18 12:30:28 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 12:33:39 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 12:35:20 INF] 📩 SMS gửi đến **********, Success=false, UseCase=AccountCreationConfirmation, TraceId=0HNDE56C37FLG:********
[2025-06-18 12:38:51 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 12:46:23 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 12:49:11 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 12:52:15 INF] 📩 SMS gửi đến **********, Success=false, UseCase=AccountCreationConfirmation, TraceId=0HNDE5F6EVF3O:********
[2025-06-18 12:52:28 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 12:52:28 INF] 📩 SMS gửi đến **********, Success=true, UseCase=AccountCreationConfirmation, TraceId=0HNDE5F6EVF3O:********
[2025-06-18 12:55:15 INF] 📩 Sending OTP SMS to **********
[2025-06-18 12:55:15 INF] 📩 SMS gửi đến **********, Success=true, UseCase=AccountRegistrationOtp, TraceId=0HNDE5HUQ1JPT:********
[2025-06-18 12:55:40 INF] 📩 Sending OTP SMS to **********
[2025-06-18 12:55:40 INF] 📩 SMS gửi đến **********, Success=true, UseCase=AccountRegistrationOtp, TraceId=0HNDE5HUQ1JPT:********
[2025-06-18 12:56:49 INF] 📩 Sending OTP SMS to **********
[2025-06-18 12:56:49 INF] 📩 SMS gửi đến **********, Success=true, UseCase=AccountRegistrationOtp, TraceId=0HNDE5HUQ1JPT:0000000B
[2025-06-18 12:57:15 INF] 📩 Sending OTP SMS to **********
[2025-06-18 12:57:15 INF] 📩 SMS gửi đến **********, Success=true, UseCase=AccountRegistrationOtp, TraceId=0HNDE5HUQ1JPT:0000000D
[2025-06-18 12:58:43 INF] 📩 Sending OTP SMS to **********
[2025-06-18 12:58:43 INF] 📩 SMS gửi đến **********, Success=false, UseCase=AccountRegistrationOtp, TraceId=0HNDE5HUQ1JPT:0000000F
[2025-06-18 13:00:28 INF] 📩 Sending OTP SMS to **********
[2025-06-18 13:00:30 INF] 📩 SMS gửi đến **********, Success=false, UseCase=AccountRegistrationOtp, TraceId=0HNDE5HUQ1JPT:********
[2025-06-18 13:09:51 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 13:09:52 INF] 📩 SMS gửi đến **********, Success=false, UseCase=AccountCreationConfirmation, TraceId=0HNDE5HUQ1JPT:********
[2025-06-18 13:10:47 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 13:12:41 INF] 📩 SMS gửi đến **********, Success=false, UseCase=AccountCreationConfirmation, TraceId=0HNDE5HUQ1JPT:********
[2025-06-18 13:12:52 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 13:12:52 INF] 📩 SMS gửi đến **********, Success=false, UseCase=AccountCreationConfirmation, TraceId=0HNDE5HUQ1JPT:********
[2025-06-18 13:13:37 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 13:13:38 INF] 📩 SMS gửi đến **********, Success=true, UseCase=AccountCreationConfirmation, TraceId=0HNDE5HUQ1JPT:********
[2025-06-18 13:14:10 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 13:14:10 INF] 📩 SMS gửi đến **********, Success=true, UseCase=AccountCreationConfirmation, TraceId=0HNDE5HUQ1JPT:0000001B
[2025-06-18 13:14:52 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 13:14:53 INF] 📩 SMS gửi đến **********, Success=false, UseCase=AccountCreationConfirmation, TraceId=0HNDE5HUQ1JPT:0000001D
[2025-06-18 13:31:31 ERR] ❌ Unhandled exception. TraceId: 0HNDE66R2VJ4D:********
System.IndexOutOfRangeException: Index was outside the bounds of the array.
   at Shared.Utils.StringUtils.RemoveVietnameseAccent(String input) in C:\Users\<USER>\source\repos\ZenPay-ZenShop-API\Shared\Utils\StringUtils.cs:line 96
   at Applications.DTOs.AccountCreationSMSConfirmation.ToNonUnicodeConfirmationMessage() in C:\Users\<USER>\source\repos\ZenPay-ZenShop-API\Applications\DTOs\AccountCreationSMSConfirmation.cs:line 26
   at Infrastructure.Services.MBFSmsBranchnameService.SendAccountCreationNonUnicodeSMSConfirmationAsync(AccountCreationSMSConfirmation req) in C:\Users\<USER>\source\repos\ZenPay-ZenShop-API\Infrastructure\Services\MBFSmsBranchnameService.cs:line 152
   at Applications.Features.Notifications.Handlers.SendAccountCreationSMSConfirmationHandler.Handle(SendAccountCreationSMSConfirmationCommand request, CancellationToken cancellationToken) in C:\Users\<USER>\source\repos\ZenPay-ZenShop-API\Applications\Features\Notifications\Handlers\SendAccountCreationSMSConfirmationHandler.cs:line 25
   at Applications.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in C:\Users\<USER>\source\repos\ZenPay-ZenShop-API\Applications\Behaviors\ValidationBehavior.cs:line 22
   at API.Controllers.NotificationController.SendAccountCreationSMSConfirmationAsync(SMSAccountCreationConfirmationRequestDto request) in C:\Users\<USER>\source\repos\ZenPay-ZenShop-API\API\Controllers\NotificationController.cs:line 57
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at API.Middlewares.SerilogEnrichMiddleware.Invoke(HttpContext context) in C:\Users\<USER>\source\repos\ZenPay-ZenShop-API\API\Middlewares\SerilogEnrichMiddleware.cs:line 24
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at ExceptionHandlingMiddleware.Invoke(HttpContext context) in C:\Users\<USER>\source\repos\ZenPay-ZenShop-API\API\Middlewares\ExceptionHandlingMiddleware.cs:line 25
[2025-06-18 13:36:05 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 13:36:06 INF] 📩 SMS gửi đến **********, Success=false, UseCase=AccountCreationConfirmation, TraceId=0HNDE692I28DG:********
[2025-06-18 13:36:53 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 13:37:45 INF] 📩 SMS gửi đến **********, Success=false, UseCase=AccountCreationConfirmation, TraceId=0HNDE692I28DG:********
[2025-06-18 13:38:21 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 13:38:21 INF] 📩 SMS gửi đến **********, Success=true, UseCase=AccountCreationConfirmation, TraceId=0HNDE692I28DG:0000000B
[2025-06-18 13:42:14 INF] 📩 Sending confirmation SMS to **********
[2025-06-18 13:42:14 INF] 📩 SMS gửi đến **********, Success=true, UseCase=AccountCreationConfirmation, TraceId=0HNDE6CRLDIK4:********
[2025-06-18 20:29:14 INF] 🔐 [Login] Login attempt for ClientId: string
[2025-06-18 20:29:14 WRN] ❌ [Login] ClientId not found - string
[2025-06-18 20:29:14 WRN] ❌ Đăng nhập thất bại. ClientId: string
