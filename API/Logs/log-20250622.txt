[2025-06-22 09:44:23 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-22 09:44:23 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-22 09:44:29 INF] MobiFone CreateInvoice Response: "OK" - 2836 chars
[2025-06-22 09:44:29 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-22 09:44:29 INF] Successfully created invoice with response: 1 items
[2025-06-22 09:44:58 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-22 09:44:58 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-22 09:45:01 INF] MobiFone CreateInvoice Response: "OK" - 2835 chars
[2025-06-22 09:45:01 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-22 09:45:01 INF] Successfully created invoice with response: 1 items
[2025-06-22 09:45:06 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-22 09:45:06 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-22 09:45:10 INF] MobiFone CreateInvoice Response: "OK" - 2836 chars
[2025-06-22 09:45:10 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-22 09:45:10 INF] Successfully created invoice with response: 1 items
[2025-06-22 09:45:58 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-22 09:45:58 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-22 09:46:01 INF] MobiFone CreateInvoice Response: "OK" - 2836 chars
[2025-06-22 09:46:01 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-22 09:46:01 INF] Successfully created invoice with response: 1 items
[2025-06-22 10:59:21 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-22 10:59:21 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-22 10:59:21 INF] MobiFone CreateInvoice Response: "Unauthorized" - 61 chars
[2025-06-22 10:59:21 WRN] MobiFone CreateInvoice API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-06-22 10:59:21 ERR] Error occurred while creating invoice in MobiFone Invoice API: MobiFone CreateInvoice API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone CreateInvoice API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 89
   at Infrastructure.Services.MobiFoneInvoiceService.CreateInvoiceAsync(SaveListHoadon78Request request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 254
[2025-06-22 11:00:21 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-22 11:00:21 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-22 11:00:41 INF] MobiFone CreateInvoice Response: "Unauthorized" - 61 chars
[2025-06-22 11:00:41 WRN] MobiFone CreateInvoice API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-06-22 11:00:41 ERR] Error occurred while creating invoice in MobiFone Invoice API: MobiFone CreateInvoice API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone CreateInvoice API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 89
   at Infrastructure.Services.MobiFoneInvoiceService.CreateInvoiceAsync(SaveListHoadon78Request request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 254
[2025-06-22 11:01:17 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-22 11:01:17 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-22 11:01:45 INF] MobiFone CreateInvoice Response: "Unauthorized" - 61 chars
[2025-06-22 11:01:45 WRN] MobiFone CreateInvoice API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-06-22 11:01:45 ERR] Error occurred while creating invoice in MobiFone Invoice API: MobiFone CreateInvoice API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone CreateInvoice API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 89
   at Infrastructure.Services.MobiFoneInvoiceService.CreateInvoiceAsync(SaveListHoadon78Request request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 254
[2025-06-22 11:01:46 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-22 11:01:46 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-22 12:21:53 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-22 12:21:53 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-22 12:22:01 INF] MobiFone CreateInvoice Response: "Unauthorized" - 61 chars
[2025-06-22 12:22:01 WRN] MobiFone CreateInvoice API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-06-22 12:22:01 ERR] Error occurred while creating invoice in MobiFone Invoice API: MobiFone CreateInvoice API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone CreateInvoice API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 89
   at Infrastructure.Services.MobiFoneInvoiceService.CreateInvoiceAsync(SaveListHoadon78Request request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 254
[2025-06-22 12:22:14 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-22 12:22:14 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-22 12:22:24 INF] MobiFone CreateInvoice Response: "OK" - 2836 chars
[2025-06-22 12:22:24 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-22 12:22:24 INF] Successfully created invoice with response: 1 items
