[2025-06-27 06:30:15 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-27 06:30:15 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-27 06:30:15 INF] MobiFone CreateInvoice Response: "Unauthorized" - 61 chars
[2025-06-27 06:30:15 WRN] MobiFone CreateInvoice API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-06-27 06:30:15 ERR] Error occurred while creating invoice in MobiFone Invoice API: MobiFone CreateInvoice API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone CreateInvoice API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 101
   at Infrastructure.Services.MobiFoneInvoiceService.CreateInvoiceAsync(SaveListHoadon78Request request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 309
[2025-06-27 06:30:47 INF] Attempting to login to MobiFone Invoice API for user: <EMAIL>
[2025-06-27 06:30:47 INF] Calling MobiFone Login API for Username: <EMAIL> - Environment: Test
[2025-06-27 06:30:48 INF] MobiFone Login Response: "OK" - 343 chars
[2025-06-27 06:30:48 INF] MobiFone Login API call completed successfully
[2025-06-27 06:30:59 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-27 06:30:59 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-27 06:31:03 INF] MobiFone CreateInvoice Response: "OK" - 2843 chars
[2025-06-27 06:31:03 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-27 06:31:03 INF] Successfully created invoice with response: 1 items
[2025-06-27 06:38:31 INF] SignInvoiceCertFile68 with Data count: 1
[2025-06-27 06:38:31 INF] Calling MobiFone SignInvoiceCertFile68 API - Data count: 1
[2025-06-27 06:38:31 INF] MobiFone SignInvoiceCertFile68 Response: "Unauthorized" - 61 chars
[2025-06-27 06:38:31 WRN] MobiFone SignInvoiceCertFile68 API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-06-27 06:38:31 ERR] Error occurred while executing SignInvoiceCertFile68 in MobiFone Invoice API: MobiFone SignInvoiceCertFile68 API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone SignInvoiceCertFile68 API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 101
   at Infrastructure.Services.MobiFoneInvoiceService.SignInvoiceCertFile68Async(SignInvoiceCertFile68Request request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 812
[2025-06-27 06:38:33 INF] SignInvoiceCertFile68 with Data count: 1
[2025-06-27 06:38:33 INF] Calling MobiFone SignInvoiceCertFile68 API - Data count: 1
[2025-06-27 06:38:33 INF] MobiFone SignInvoiceCertFile68 Response: "Unauthorized" - 61 chars
[2025-06-27 06:38:33 WRN] MobiFone SignInvoiceCertFile68 API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-06-27 06:38:33 ERR] Error occurred while executing SignInvoiceCertFile68 in MobiFone Invoice API: MobiFone SignInvoiceCertFile68 API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone SignInvoiceCertFile68 API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 101
   at Infrastructure.Services.MobiFoneInvoiceService.SignInvoiceCertFile68Async(SignInvoiceCertFile68Request request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 812
[2025-06-27 06:38:46 INF] SignInvoiceCertFile68 with Data count: 1
[2025-06-27 06:38:46 INF] Calling MobiFone SignInvoiceCertFile68 API - Data count: 1
[2025-06-27 06:38:53 INF] MobiFone SignInvoiceCertFile68 Response: "OK" - 90 chars
[2025-06-27 06:38:53 INF] MobiFone SignInvoiceCertFile68 API call completed successfully
[2025-06-27 06:38:53 INF] Successfully executed SignInvoiceCertFile68 with status: Đã ký
[2025-06-27 06:43:28 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-27 06:43:28 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-27 06:43:31 INF] MobiFone CreateInvoice Response: "OK" - 2843 chars
[2025-06-27 06:43:31 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-27 06:43:31 INF] Successfully created invoice with response: 1 items
[2025-06-27 06:57:49 ERR] ❌ Unhandled exception. TraceId: 0HNDL15APUM5N:00000009
System.NullReferenceException: Object reference not set to an instance of an object.
   at Applications.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Applications/Behaviors/ValidationBehavior.cs:line 42
   at API.Controllers.MobiFoneInvoiceController.SaveAndSignHoadon78Async(SaveAndSignHoadon78Request request, String token, String maDvcs) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Controllers/MobiFoneInvoiceController.cs:line 289
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at API.Middlewares.SerilogEnrichMiddleware.Invoke(HttpContext context) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/SerilogEnrichMiddleware.cs:line 24
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at ExceptionHandlingMiddleware.Invoke(HttpContext context) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/ExceptionHandlingMiddleware.cs:line 25
[2025-06-27 06:59:10 ERR] ❌ Unhandled exception. TraceId: 0HNDL15APUM5P:00000001
System.NullReferenceException: Object reference not set to an instance of an object.
   at Applications.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Applications/Behaviors/ValidationBehavior.cs:line 42
   at API.Controllers.MobiFoneInvoiceController.SaveAndSignHoadon78Async(SaveAndSignHoadon78Request request, String token, String maDvcs) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Controllers/MobiFoneInvoiceController.cs:line 289
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at API.Middlewares.SerilogEnrichMiddleware.Invoke(HttpContext context) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/SerilogEnrichMiddleware.cs:line 24
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at ExceptionHandlingMiddleware.Invoke(HttpContext context) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/ExceptionHandlingMiddleware.cs:line 25
[2025-06-27 06:59:46 ERR] ❌ Unhandled exception. TraceId: 0HNDL15APUM5P:00000002
System.NullReferenceException: Object reference not set to an instance of an object.
   at Applications.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Applications/Behaviors/ValidationBehavior.cs:line 42
   at API.Controllers.MobiFoneInvoiceController.SaveAndSignHoadon78Async(SaveAndSignHoadon78Request request, String token, String maDvcs) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Controllers/MobiFoneInvoiceController.cs:line 289
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at API.Middlewares.SerilogEnrichMiddleware.Invoke(HttpContext context) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/SerilogEnrichMiddleware.cs:line 24
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at ExceptionHandlingMiddleware.Invoke(HttpContext context) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/ExceptionHandlingMiddleware.cs:line 25
[2025-06-27 07:00:15 ERR] ❌ Unhandled exception. TraceId: 0HNDL15APUM5P:00000003
System.NullReferenceException: Object reference not set to an instance of an object.
   at Applications.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Applications/Behaviors/ValidationBehavior.cs:line 42
   at API.Controllers.MobiFoneInvoiceController.SaveAndSignHoadon78Async(SaveAndSignHoadon78Request request, String token, String maDvcs) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Controllers/MobiFoneInvoiceController.cs:line 289
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at API.Middlewares.SerilogEnrichMiddleware.Invoke(HttpContext context) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/SerilogEnrichMiddleware.cs:line 24
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at ExceptionHandlingMiddleware.Invoke(HttpContext context) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/ExceptionHandlingMiddleware.cs:line 25
[2025-06-27 07:01:00 ERR] ❌ Unhandled exception. TraceId: 0HNDL15APUM5P:00000004
System.NullReferenceException: Object reference not set to an instance of an object.
   at Applications.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Applications/Behaviors/ValidationBehavior.cs:line 42
   at API.Controllers.MobiFoneInvoiceController.SaveAndSignHoadon78Async(SaveAndSignHoadon78Request request, String token, String maDvcs) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Controllers/MobiFoneInvoiceController.cs:line 289
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at API.Middlewares.SerilogEnrichMiddleware.Invoke(HttpContext context) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/SerilogEnrichMiddleware.cs:line 24
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at ExceptionHandlingMiddleware.Invoke(HttpContext context) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/ExceptionHandlingMiddleware.cs:line 25
[2025-06-27 07:01:02 ERR] ❌ Unhandled exception. TraceId: 0HNDL15APUM5P:00000005
System.NullReferenceException: Object reference not set to an instance of an object.
   at Applications.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Applications/Behaviors/ValidationBehavior.cs:line 42
   at API.Controllers.MobiFoneInvoiceController.SaveAndSignHoadon78Async(SaveAndSignHoadon78Request request, String token, String maDvcs) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Controllers/MobiFoneInvoiceController.cs:line 289
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at API.Middlewares.SerilogEnrichMiddleware.Invoke(HttpContext context) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/SerilogEnrichMiddleware.cs:line 24
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at ExceptionHandlingMiddleware.Invoke(HttpContext context) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/ExceptionHandlingMiddleware.cs:line 25
[2025-06-27 07:01:02 ERR] ❌ Unhandled exception. TraceId: 0HNDL15APUM5P:00000006
System.NullReferenceException: Object reference not set to an instance of an object.
   at Applications.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Applications/Behaviors/ValidationBehavior.cs:line 42
   at API.Controllers.MobiFoneInvoiceController.SaveAndSignHoadon78Async(SaveAndSignHoadon78Request request, String token, String maDvcs) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Controllers/MobiFoneInvoiceController.cs:line 289
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at API.Middlewares.SerilogEnrichMiddleware.Invoke(HttpContext context) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/SerilogEnrichMiddleware.cs:line 24
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at ExceptionHandlingMiddleware.Invoke(HttpContext context) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/ExceptionHandlingMiddleware.cs:line 25
[2025-06-27 07:01:03 ERR] ❌ Unhandled exception. TraceId: 0HNDL15APUM5P:00000007
System.NullReferenceException: Object reference not set to an instance of an object.
   at Applications.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Applications/Behaviors/ValidationBehavior.cs:line 42
   at API.Controllers.MobiFoneInvoiceController.SaveAndSignHoadon78Async(SaveAndSignHoadon78Request request, String token, String maDvcs) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Controllers/MobiFoneInvoiceController.cs:line 289
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at API.Middlewares.SerilogEnrichMiddleware.Invoke(HttpContext context) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/SerilogEnrichMiddleware.cs:line 24
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at ExceptionHandlingMiddleware.Invoke(HttpContext context) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/ExceptionHandlingMiddleware.cs:line 25
[2025-06-27 07:01:19 ERR] ❌ Unhandled exception. TraceId: 0HNDL15APUM5N:0000000F
System.NullReferenceException: Object reference not set to an instance of an object.
   at Applications.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Applications/Behaviors/ValidationBehavior.cs:line 42
   at API.Controllers.MobiFoneInvoiceController.SaveAndSignHoadon78Async(SaveAndSignHoadon78Request request, String token, String maDvcs) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Controllers/MobiFoneInvoiceController.cs:line 289
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at API.Middlewares.SerilogEnrichMiddleware.Invoke(HttpContext context) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/SerilogEnrichMiddleware.cs:line 24
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at ExceptionHandlingMiddleware.Invoke(HttpContext context) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/ExceptionHandlingMiddleware.cs:line 25
[2025-06-27 07:02:11 INF] SaveAndSignHoadon78 with EditMode: 0, TypeCmd: 206, GuiCQT: Notsend, Data count: 1
[2025-06-27 07:02:12 INF] Calling MobiFone SaveAndSignHoadon78 API - EditMode: 0, TypeCmd: 206
[2025-06-27 07:02:12 INF] MobiFone SaveAndSignHoadon78 Response: "OK" - 81 chars
[2025-06-27 07:02:12 INF] MobiFone SaveAndSignHoadon78 API call completed successfully
[2025-06-27 07:02:12 INF] Successfully executed SaveAndSignHoadon78 with response: 1 items
[2025-06-27 07:02:59 INF] SaveAndSignHoadon78 with EditMode: 0, TypeCmd: 206, GuiCQT: Notsend, Data count: 1
[2025-06-27 07:02:59 INF] Calling MobiFone SaveAndSignHoadon78 API - EditMode: 0, TypeCmd: 206
[2025-06-27 07:03:59 INF] MobiFone SaveAndSignHoadon78 Response: "OK" - 81 chars
[2025-06-27 07:03:59 INF] MobiFone SaveAndSignHoadon78 API call completed successfully
[2025-06-27 07:03:59 INF] Successfully executed SaveAndSignHoadon78 with response: 1 items
[2025-06-27 07:04:03 INF] SaveAndSignHoadon78 with EditMode: 0, TypeCmd: 206, GuiCQT: Notsend, Data count: 1
[2025-06-27 07:04:03 INF] Calling MobiFone SaveAndSignHoadon78 API - EditMode: 0, TypeCmd: 206
[2025-06-27 07:04:54 INF] MobiFone SaveAndSignHoadon78 Response: "OK" - 81 chars
[2025-06-27 07:04:54 INF] MobiFone SaveAndSignHoadon78 API call completed successfully
[2025-06-27 07:04:54 INF] Successfully executed SaveAndSignHoadon78 with response: 1 items
[2025-06-27 07:04:54 INF] SaveAndSignHoadon78 with EditMode: 0, TypeCmd: 206, GuiCQT: Notsend, Data count: 1
[2025-06-27 07:04:54 INF] Calling MobiFone SaveAndSignHoadon78 API - EditMode: 0, TypeCmd: 206
[2025-06-27 07:06:06 INF] MobiFone SaveAndSignHoadon78 Response: "OK" - 81 chars
[2025-06-27 07:06:06 INF] MobiFone SaveAndSignHoadon78 API call completed successfully
[2025-06-27 07:06:06 INF] Successfully executed SaveAndSignHoadon78 with response: 1 items
[2025-06-27 07:06:06 INF] SaveAndSignHoadon78 with EditMode: 0, TypeCmd: 206, GuiCQT: Notsend, Data count: 1
[2025-06-27 07:06:06 INF] Calling MobiFone SaveAndSignHoadon78 API - EditMode: 0, TypeCmd: 206
[2025-06-27 07:06:12 INF] MobiFone SaveAndSignHoadon78 Response: "OK" - 81 chars
[2025-06-27 07:06:12 INF] MobiFone SaveAndSignHoadon78 API call completed successfully
[2025-06-27 07:06:12 INF] Successfully executed SaveAndSignHoadon78 with response: 1 items
[2025-06-27 07:06:16 INF] SaveAndSignHoadon78 with EditMode: 0, TypeCmd: 206, GuiCQT: Notsend, Data count: 1
[2025-06-27 07:06:16 INF] Calling MobiFone SaveAndSignHoadon78 API - EditMode: 0, TypeCmd: 206
[2025-06-27 07:06:20 INF] MobiFone SaveAndSignHoadon78 Response: "OK" - 81 chars
[2025-06-27 07:06:20 INF] MobiFone SaveAndSignHoadon78 API call completed successfully
[2025-06-27 07:06:20 INF] Successfully executed SaveAndSignHoadon78 with response: 1 items
[2025-06-27 07:07:02 INF] SaveAndSignHoadon78 with EditMode: 1, TypeCmd: 206, GuiCQT: Notsend, Data count: 1
[2025-06-27 07:07:02 INF] Calling MobiFone SaveAndSignHoadon78 API - EditMode: 1, TypeCmd: 206
[2025-06-27 07:07:15 INF] MobiFone SaveAndSignHoadon78 Response: "OK" - 2971 chars
[2025-06-27 07:07:15 INF] MobiFone SaveAndSignHoadon78 API call completed successfully
[2025-06-27 07:07:15 INF] Successfully executed SaveAndSignHoadon78 with response: 1 items
[2025-06-27 07:08:32 INF] SaveAndSignHoadon78 with EditMode: 1, TypeCmd: 206, GuiCQT: , Data count: 1
[2025-06-27 07:08:32 INF] Calling MobiFone SaveAndSignHoadon78 API - EditMode: 1, TypeCmd: 206
[2025-06-27 07:08:44 INF] MobiFone SaveAndSignHoadon78 Response: "OK" - 2971 chars
[2025-06-27 07:08:44 INF] MobiFone SaveAndSignHoadon78 API call completed successfully
[2025-06-27 07:08:44 INF] Successfully executed SaveAndSignHoadon78 with response: 1 items
[2025-06-27 07:09:04 INF] SaveAndSignHoadon78 with EditMode: 1, TypeCmd: 206, GuiCQT: , Data count: 1
[2025-06-27 07:09:04 INF] Calling MobiFone SaveAndSignHoadon78 API - EditMode: 1, TypeCmd: 206
[2025-06-27 07:09:12 INF] MobiFone SaveAndSignHoadon78 Response: "OK" - 2971 chars
[2025-06-27 07:09:12 INF] MobiFone SaveAndSignHoadon78 API call completed successfully
[2025-06-27 07:09:12 INF] Successfully executed SaveAndSignHoadon78 with response: 1 items
[2025-06-27 07:09:52 INF] SaveAndSignHoadon78 with EditMode: 1, TypeCmd: 206, GuiCQT: Notsend, Data count: 1
[2025-06-27 07:09:52 INF] Calling MobiFone SaveAndSignHoadon78 API - EditMode: 1, TypeCmd: 206
[2025-06-27 07:10:01 INF] MobiFone SaveAndSignHoadon78 Response: "OK" - 2971 chars
[2025-06-27 07:10:01 INF] MobiFone SaveAndSignHoadon78 API call completed successfully
[2025-06-27 07:10:01 INF] Successfully executed SaveAndSignHoadon78 with response: 1 items
[2025-06-27 20:08:35 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-27 20:08:35 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-27 20:08:39 INF] MobiFone CreateInvoice Response: "OK" - 2843 chars
[2025-06-27 20:08:39 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-27 20:08:39 INF] Successfully created invoice with response: 1 items
[2025-06-27 20:11:15 INF] SignInvoiceCertFile68 with Data count: 1
[2025-06-27 20:11:15 INF] Calling MobiFone SignInvoiceCertFile68 API - Data count: 1
[2025-06-27 20:11:18 INF] MobiFone SignInvoiceCertFile68 Response: "OK" - 88 chars
[2025-06-27 20:11:18 INF] MobiFone SignInvoiceCertFile68 API call completed successfully
[2025-06-27 20:11:18 INF] Successfully executed SignInvoiceCertFile68 with status: null
[2025-06-27 20:14:17 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-27 20:14:17 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-27 20:14:18 INF] MobiFone CreateInvoice Response: "OK" - 47 chars
[2025-06-27 20:14:18 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-27 20:14:18 INF] Successfully created invoice with response: 1 items
[2025-06-27 20:14:47 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-27 20:14:47 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-27 20:14:51 INF] MobiFone CreateInvoice Response: "OK" - 2843 chars
[2025-06-27 20:14:51 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-27 20:14:51 INF] Successfully created invoice with response: 1 items
[2025-06-27 20:22:12 INF] Getting data references with RefId: RF00059
[2025-06-27 20:22:12 INF] Calling MobiFone GetDataReferences API - RefId: RF00059
[2025-06-27 20:22:12 INF] MobiFone GetDataReferences Response: "Unauthorized" - 61 chars
[2025-06-27 20:22:12 WRN] MobiFone GetDataReferences API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-06-27 20:22:12 ERR] Error occurred while getting data references from MobiFone Invoice API: MobiFone GetDataReferences API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone GetDataReferences API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 101
   at Infrastructure.Services.MobiFoneInvoiceService.GetDataReferencesAsync(GetDataReferencesRequest request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 219
[2025-06-27 20:22:33 INF] Getting list certificates file 68
[2025-06-27 20:22:33 INF] Calling MobiFone GetListCertificatesFile68 API
[2025-06-27 20:22:33 INF] MobiFone GetListCertificatesFile68 Response: "Unauthorized" - 61 chars
[2025-06-27 20:22:33 WRN] MobiFone GetListCertificatesFile68 API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-06-27 20:22:34 ERR] Error occurred while getting certificates from MobiFone Invoice API: MobiFone GetListCertificatesFile68 API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone GetListCertificatesFile68 API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 101
   at Infrastructure.Services.MobiFoneInvoiceService.GetListCertificatesFile68Async(GetListCertificatesFile68Request request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 262
[2025-06-27 20:22:38 INF] GetHistoryInvoice for invoice ID: 4838d6a5-c32a-442f-9fc4-3771186b097d
[2025-06-27 20:22:38 INF] Calling MobiFone GetHistoryInvoice API for invoice: 4838d6a5-c32a-442f-9fc4-3771186b097d
[2025-06-27 20:22:38 INF] MobiFone GetHistoryInvoice Response: "Unauthorized" - 61 chars
[2025-06-27 20:22:38 WRN] MobiFone GetHistoryInvoice API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-06-27 20:22:38 ERR] Error occurred while getting history for invoice 4838d6a5-c32a-442f-9fc4-3771186b097d in MobiFone Invoice API: MobiFone GetHistoryInvoice API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone GetHistoryInvoice API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 101
   at Infrastructure.Services.MobiFoneInvoiceService.GetHistoryInvoiceAsync(String id, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 952
[2025-06-27 20:22:49 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-27 20:22:49 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-27 20:22:54 INF] MobiFone CreateInvoice Response: "OK" - 2843 chars
[2025-06-27 20:22:54 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-27 20:22:54 INF] Successfully created invoice with response: 1 items
[2025-06-27 20:23:05 INF] GetHistoryInvoice for invoice ID: 4838d6a5-c32a-442f-9fc4-3771186b097d
[2025-06-27 20:23:05 INF] Calling MobiFone GetHistoryInvoice API for invoice: 4838d6a5-c32a-442f-9fc4-3771186b097d
[2025-06-27 20:23:05 INF] MobiFone GetHistoryInvoice Response: "OK" - 1070 chars
[2025-06-27 20:23:05 INF] MobiFone GetHistoryInvoice API call completed successfully
[2025-06-27 20:23:05 INF] Successfully retrieved history for invoice 4838d6a5-c32a-442f-9fc4-3771186b097d with 4 records
[2025-06-27 20:23:24 INF] GetHistoryInvoice for invoice ID: 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5
[2025-06-27 20:23:24 INF] Calling MobiFone GetHistoryInvoice API for invoice: 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5
[2025-06-27 20:23:25 INF] MobiFone GetHistoryInvoice Response: "OK" - 2 chars
[2025-06-27 20:23:25 INF] MobiFone GetHistoryInvoice API call completed successfully
[2025-06-27 20:23:25 INF] Successfully retrieved history for invoice 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5 with 0 records
[2025-06-27 20:23:35 INF] GetHistoryInvoice for invoice ID: 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5
[2025-06-27 20:23:35 INF] Calling MobiFone GetHistoryInvoice API for invoice: 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5
[2025-06-27 20:23:35 INF] MobiFone GetHistoryInvoice Response: "OK" - 2 chars
[2025-06-27 20:23:35 INF] MobiFone GetHistoryInvoice API call completed successfully
[2025-06-27 20:23:35 INF] Successfully retrieved history for invoice 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5 with 0 records
[2025-06-27 20:23:54 INF] GetHistoryInvoice for invoice ID: 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5
[2025-06-27 20:23:54 INF] Calling MobiFone GetHistoryInvoice API for invoice: 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5
[2025-06-27 20:23:54 INF] MobiFone GetHistoryInvoice Response: "OK" - 2 chars
[2025-06-27 20:23:54 INF] MobiFone GetHistoryInvoice API call completed successfully
[2025-06-27 20:23:54 INF] Successfully retrieved history for invoice 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5 with 0 records
[2025-06-27 20:28:04 INF] SignInvoiceCertFile68 with Data count: 1
[2025-06-27 20:28:04 INF] Calling MobiFone SignInvoiceCertFile68 API - Data count: 1
[2025-06-27 20:28:10 INF] MobiFone SignInvoiceCertFile68 Response: "OK" - 90 chars
[2025-06-27 20:28:10 INF] MobiFone SignInvoiceCertFile68 API call completed successfully
[2025-06-27 20:28:10 INF] Successfully executed SignInvoiceCertFile68 with status: Đã ký
[2025-06-27 20:28:41 INF] GetHistoryInvoice for invoice ID: 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5
[2025-06-27 20:28:41 INF] Calling MobiFone GetHistoryInvoice API for invoice: 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5
[2025-06-27 20:28:41 INF] MobiFone GetHistoryInvoice Response: "OK" - 2 chars
[2025-06-27 20:28:41 INF] MobiFone GetHistoryInvoice API call completed successfully
[2025-06-27 20:28:41 INF] Successfully retrieved history for invoice 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5 with 0 records
[2025-06-27 20:28:42 INF] GetHistoryInvoice for invoice ID: 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5
[2025-06-27 20:28:42 INF] Calling MobiFone GetHistoryInvoice API for invoice: 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5
[2025-06-27 20:28:42 INF] MobiFone GetHistoryInvoice Response: "OK" - 2 chars
[2025-06-27 20:28:42 INF] MobiFone GetHistoryInvoice API call completed successfully
[2025-06-27 20:28:42 INF] Successfully retrieved history for invoice 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5 with 0 records
[2025-06-27 20:28:43 INF] GetHistoryInvoice for invoice ID: 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5
[2025-06-27 20:28:43 INF] Calling MobiFone GetHistoryInvoice API for invoice: 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5
[2025-06-27 20:28:43 INF] MobiFone GetHistoryInvoice Response: "OK" - 2 chars
[2025-06-27 20:28:43 INF] MobiFone GetHistoryInvoice API call completed successfully
[2025-06-27 20:28:43 INF] Successfully retrieved history for invoice 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5 with 0 records
[2025-06-27 20:28:51 INF] GetHistoryInvoice for invoice ID: 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5
[2025-06-27 20:28:51 INF] Calling MobiFone GetHistoryInvoice API for invoice: 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5
[2025-06-27 20:28:51 INF] MobiFone GetHistoryInvoice Response: "OK" - 2 chars
[2025-06-27 20:28:51 INF] MobiFone GetHistoryInvoice API call completed successfully
[2025-06-27 20:28:51 INF] Successfully retrieved history for invoice 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5 with 0 records
[2025-06-27 20:29:05 INF] SendInvoiceToCQT68 with Invoice count: 1, TypeCmd: 206
[2025-06-27 20:29:05 INF] Calling MobiFone SendInvoiceToCQT68 API - Invoice count: 1
[2025-06-27 20:29:05 INF] MobiFone SendInvoiceToCQT68 Response: "Unauthorized" - 61 chars
[2025-06-27 20:29:05 WRN] MobiFone SendInvoiceToCQT68 API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-06-27 20:29:05 ERR] Error occurred while executing SendInvoiceToCQT68 in MobiFone Invoice API: MobiFone SendInvoiceToCQT68 API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone SendInvoiceToCQT68 API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 101
   at Infrastructure.Services.MobiFoneInvoiceService.SendInvoiceToCQT68Async(SendInvoiceToCQT68Request request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 860
[2025-06-27 20:29:06 INF] SendInvoiceToCQT68 with Invoice count: 1, TypeCmd: 206
[2025-06-27 20:29:06 INF] Calling MobiFone SendInvoiceToCQT68 API - Invoice count: 1
[2025-06-27 20:29:06 INF] MobiFone SendInvoiceToCQT68 Response: "Unauthorized" - 61 chars
[2025-06-27 20:29:06 WRN] MobiFone SendInvoiceToCQT68 API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-06-27 20:29:06 ERR] Error occurred while executing SendInvoiceToCQT68 in MobiFone Invoice API: MobiFone SendInvoiceToCQT68 API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone SendInvoiceToCQT68 API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 101
   at Infrastructure.Services.MobiFoneInvoiceService.SendInvoiceToCQT68Async(SendInvoiceToCQT68Request request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 860
[2025-06-27 20:29:18 INF] SendInvoiceToCQT68 with Invoice count: 1, TypeCmd: 206
[2025-06-27 20:29:18 INF] Calling MobiFone SendInvoiceToCQT68 API - Invoice count: 1
[2025-06-27 20:29:19 INF] MobiFone SendInvoiceToCQT68 Response: "OK" - 58 chars
[2025-06-27 20:29:19 INF] MobiFone SendInvoiceToCQT68 API call completed successfully
[2025-06-27 20:29:19 INF] Successfully executed SendInvoiceToCQT68 with status: Đã gửi hóa đơn tới Cơ quan thuế
[2025-06-27 20:29:26 INF] GetHistoryInvoice for invoice ID: 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5
[2025-06-27 20:29:26 INF] Calling MobiFone GetHistoryInvoice API for invoice: 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5
[2025-06-27 20:29:26 INF] MobiFone GetHistoryInvoice Response: "OK" - 237 chars
[2025-06-27 20:29:26 INF] MobiFone GetHistoryInvoice API call completed successfully
[2025-06-27 20:29:26 INF] Successfully retrieved history for invoice 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5 with 1 records
[2025-06-27 20:39:03 INF] SaveAndSignHoadon78 with EditMode: 1, TypeCmd: 206, GuiCQT: Notsend, Data count: 1
[2025-06-27 20:39:03 INF] Calling MobiFone SaveAndSignHoadon78 API - EditMode: 1, TypeCmd: 206
[2025-06-27 20:39:04 INF] MobiFone SaveAndSignHoadon78 Response: "OK" - 87 chars
[2025-06-27 20:39:04 INF] MobiFone SaveAndSignHoadon78 API call completed successfully
[2025-06-27 20:39:04 INF] Successfully executed SaveAndSignHoadon78 with response: 1 items
[2025-06-27 20:39:22 INF] SaveAndSignHoadon78 with EditMode: 1, TypeCmd: 206, GuiCQT: Notsend, Data count: 1
[2025-06-27 20:39:22 INF] Calling MobiFone SaveAndSignHoadon78 API - EditMode: 1, TypeCmd: 206
[2025-06-27 20:39:23 INF] MobiFone SaveAndSignHoadon78 Response: "OK" - 87 chars
[2025-06-27 20:39:23 INF] MobiFone SaveAndSignHoadon78 API call completed successfully
[2025-06-27 20:39:23 INF] Successfully executed SaveAndSignHoadon78 with response: 1 items
[2025-06-27 20:40:39 INF] SaveAndSignHoadon78 with EditMode: 1, TypeCmd: 206, GuiCQT: Notsend, Data count: 1
[2025-06-27 20:40:39 INF] Calling MobiFone SaveAndSignHoadon78 API - EditMode: 1, TypeCmd: 206
[2025-06-27 20:40:40 INF] MobiFone SaveAndSignHoadon78 Response: "OK" - 87 chars
[2025-06-27 20:40:40 INF] MobiFone SaveAndSignHoadon78 API call completed successfully
[2025-06-27 20:40:40 INF] Successfully executed SaveAndSignHoadon78 with response: 1 items
[2025-06-27 20:41:31 INF] SaveAndSignHoadon78 with EditMode: 1, TypeCmd: 206, GuiCQT: Notsend, Data count: 1
[2025-06-27 20:41:31 INF] Calling MobiFone SaveAndSignHoadon78 API - EditMode: 1, TypeCmd: 206
[2025-06-27 20:41:47 INF] MobiFone SaveAndSignHoadon78 Response: "OK" - 87 chars
[2025-06-27 20:41:47 INF] MobiFone SaveAndSignHoadon78 API call completed successfully
[2025-06-27 20:41:47 INF] Successfully executed SaveAndSignHoadon78 with response: 1 items
[2025-06-27 20:41:53 INF] SaveAndSignHoadon78 with EditMode: 1, TypeCmd: 206, GuiCQT: Notsend, Data count: 1
[2025-06-27 20:41:53 INF] Calling MobiFone SaveAndSignHoadon78 API - EditMode: 1, TypeCmd: 206
[2025-06-27 20:42:09 INF] MobiFone SaveAndSignHoadon78 Response: "OK" - 2970 chars
[2025-06-27 20:42:09 INF] MobiFone SaveAndSignHoadon78 API call completed successfully
[2025-06-27 20:42:09 INF] Successfully executed SaveAndSignHoadon78 with response: 1 items
[2025-06-27 20:49:12 INF] GetHistoryInvoice for invoice ID: 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5
[2025-06-27 20:49:12 INF] Calling MobiFone GetHistoryInvoice API for invoice: 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5
[2025-06-27 20:49:12 INF] MobiFone GetHistoryInvoice Response: "Unauthorized" - 61 chars
[2025-06-27 20:49:12 WRN] MobiFone GetHistoryInvoice API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-06-27 20:49:12 ERR] Error occurred while getting history for invoice 1c4f2911-c75f-4907-a9b9-9d2bc6b7b3f5 in MobiFone Invoice API: MobiFone GetHistoryInvoice API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone GetHistoryInvoice API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 101
   at Infrastructure.Services.MobiFoneInvoiceService.GetHistoryInvoiceAsync(String id, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 952
[2025-06-27 20:49:16 INF] GetHistoryInvoice for invoice ID: 135af16d-419e-4445-bc12-08151928df6c
[2025-06-27 20:49:16 INF] Calling MobiFone GetHistoryInvoice API for invoice: 135af16d-419e-4445-bc12-08151928df6c
[2025-06-27 20:49:16 INF] MobiFone GetHistoryInvoice Response: "OK" - 2 chars
[2025-06-27 20:49:16 INF] MobiFone GetHistoryInvoice API call completed successfully
[2025-06-27 20:49:16 INF] Successfully retrieved history for invoice 135af16d-419e-4445-bc12-08151928df6c with 0 records
[2025-06-27 20:49:36 INF] SendInvoiceToCQT68 with Invoice count: 1, TypeCmd: 206
[2025-06-27 20:49:36 INF] Calling MobiFone SendInvoiceToCQT68 API - Invoice count: 1
[2025-06-27 20:49:38 INF] MobiFone SendInvoiceToCQT68 Response: "OK" - 58 chars
[2025-06-27 20:49:38 INF] MobiFone SendInvoiceToCQT68 API call completed successfully
[2025-06-27 20:49:38 INF] Successfully executed SendInvoiceToCQT68 with status: Đã gửi hóa đơn tới Cơ quan thuế
[2025-06-27 20:49:43 INF] GetHistoryInvoice for invoice ID: 135af16d-419e-4445-bc12-08151928df6c
[2025-06-27 20:49:43 INF] Calling MobiFone GetHistoryInvoice API for invoice: 135af16d-419e-4445-bc12-08151928df6c
[2025-06-27 20:49:43 INF] MobiFone GetHistoryInvoice Response: "OK" - 237 chars
[2025-06-27 20:49:43 INF] MobiFone GetHistoryInvoice API call completed successfully
[2025-06-27 20:49:43 INF] Successfully retrieved history for invoice 135af16d-419e-4445-bc12-08151928df6c with 1 records
