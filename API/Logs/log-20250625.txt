[2025-06-25 21:01:47 INF] Getting data references with RefId: 213
[2025-06-25 21:01:47 INF] Calling MobiFone GetDataReferences API - RefId: 213
[2025-06-25 21:01:48 INF] MobiFone GetDataReferences Response: "Unauthorized" - 61 chars
[2025-06-25 21:01:48 WRN] MobiFone GetDataReferences API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-06-25 21:01:48 ERR] Error occurred while getting data references from MobiFone Invoice API: MobiFone GetDataReferences API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone GetDataReferences API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 100
   at Infrastructure.Services.MobiFoneInvoiceService.GetDataReferencesAsync(GetDataReferencesRequest request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 218
[2025-06-25 21:02:50 INF] Getting data references with RefId: 213
[2025-06-25 21:02:50 INF] Calling MobiFone GetDataReferences API - RefId: 213
[2025-06-25 21:02:50 INF] MobiFone GetDataReferences Response: "Unauthorized" - 61 chars
[2025-06-25 21:02:50 WRN] MobiFone GetDataReferences API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-06-25 21:02:50 ERR] Error occurred while getting data references from MobiFone Invoice API: MobiFone GetDataReferences API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone GetDataReferences API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 100
   at Infrastructure.Services.MobiFoneInvoiceService.GetDataReferencesAsync(GetDataReferencesRequest request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 218
[2025-06-25 21:02:54 INF] Getting data references with RefId: 213
[2025-06-25 21:02:54 INF] Calling MobiFone GetDataReferences API - RefId: 213
[2025-06-25 21:02:54 INF] MobiFone GetDataReferences Response: "Unauthorized" - 61 chars
[2025-06-25 21:02:54 WRN] MobiFone GetDataReferences API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-06-25 21:02:54 ERR] Error occurred while getting data references from MobiFone Invoice API: MobiFone GetDataReferences API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone GetDataReferences API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 100
   at Infrastructure.Services.MobiFoneInvoiceService.GetDataReferencesAsync(GetDataReferencesRequest request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 218
[2025-06-25 21:02:59 INF] Attempting to login to MobiFone Invoice API for user: <EMAIL>
[2025-06-25 21:02:59 INF] Calling MobiFone Login API for Username: <EMAIL> - Environment: Test
[2025-06-25 21:02:59 INF] MobiFone Login Response: "OK" - 343 chars
[2025-06-25 21:02:59 INF] MobiFone Login API call completed successfully
[2025-06-25 21:03:19 INF] Getting data references with RefId: 213
[2025-06-25 21:03:19 INF] Calling MobiFone GetDataReferences API - RefId: 213
[2025-06-25 21:03:19 INF] MobiFone GetDataReferences Response: "OK" - 4 chars
[2025-06-25 21:03:19 ERR] Failed to deserialize MobiFone GetDataReferences response - result is null
[2025-06-25 21:03:19 ERR] Error occurred while getting data references from MobiFone Invoice API: Không thể chuyển đổi dữ liệu từ MobiFone GetDataReferences API
Applications.Exceptions.MobiFoneApiDeserializationException: Không thể chuyển đổi dữ liệu từ MobiFone GetDataReferences API
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 109
   at Infrastructure.Services.MobiFoneInvoiceService.GetDataReferencesAsync(GetDataReferencesRequest request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 218
[2025-06-25 21:03:23 INF] Getting data references with RefId: 213
[2025-06-25 21:03:23 INF] Calling MobiFone GetDataReferences API - RefId: 213
[2025-06-25 21:03:23 INF] MobiFone GetDataReferences Response: "OK" - 4 chars
[2025-06-25 21:03:23 ERR] Failed to deserialize MobiFone GetDataReferences response - result is null
[2025-06-25 21:03:23 ERR] Error occurred while getting data references from MobiFone Invoice API: Không thể chuyển đổi dữ liệu từ MobiFone GetDataReferences API
Applications.Exceptions.MobiFoneApiDeserializationException: Không thể chuyển đổi dữ liệu từ MobiFone GetDataReferences API
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 109
   at Infrastructure.Services.MobiFoneInvoiceService.GetDataReferencesAsync(GetDataReferencesRequest request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 218
[2025-06-25 21:03:42 INF] Getting data references with RefId: 213
[2025-06-25 21:03:42 INF] Calling MobiFone GetDataReferences API - RefId: 213
[2025-06-25 21:03:42 INF] MobiFone GetDataReferences Response: "OK" - 4 chars
[2025-06-25 21:03:42 ERR] Failed to deserialize MobiFone GetDataReferences response - result is null
[2025-06-25 21:03:42 ERR] Error occurred while getting data references from MobiFone Invoice API: Không thể chuyển đổi dữ liệu từ MobiFone GetDataReferences API
Applications.Exceptions.MobiFoneApiDeserializationException: Không thể chuyển đổi dữ liệu từ MobiFone GetDataReferences API
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 109
   at Infrastructure.Services.MobiFoneInvoiceService.GetDataReferencesAsync(GetDataReferencesRequest request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 218
[2025-06-25 21:03:50 INF] Getting data references with RefId: null
[2025-06-25 21:03:50 INF] Calling MobiFone GetDataReferences API - RefId: null
[2025-06-25 21:03:50 INF] MobiFone GetDataReferences Response: "OK" - 4 chars
[2025-06-25 21:03:50 ERR] Failed to deserialize MobiFone GetDataReferences response - result is null
[2025-06-25 21:03:50 ERR] Error occurred while getting data references from MobiFone Invoice API: Không thể chuyển đổi dữ liệu từ MobiFone GetDataReferences API
Applications.Exceptions.MobiFoneApiDeserializationException: Không thể chuyển đổi dữ liệu từ MobiFone GetDataReferences API
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 109
   at Infrastructure.Services.MobiFoneInvoiceService.GetDataReferencesAsync(GetDataReferencesRequest request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 218
[2025-06-25 21:03:52 INF] Getting data references with RefId: null
[2025-06-25 21:03:52 INF] Calling MobiFone GetDataReferences API - RefId: null
[2025-06-25 21:03:52 INF] MobiFone GetDataReferences Response: "OK" - 4 chars
[2025-06-25 21:03:52 ERR] Failed to deserialize MobiFone GetDataReferences response - result is null
[2025-06-25 21:03:52 ERR] Error occurred while getting data references from MobiFone Invoice API: Không thể chuyển đổi dữ liệu từ MobiFone GetDataReferences API
Applications.Exceptions.MobiFoneApiDeserializationException: Không thể chuyển đổi dữ liệu từ MobiFone GetDataReferences API
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 109
   at Infrastructure.Services.MobiFoneInvoiceService.GetDataReferencesAsync(GetDataReferencesRequest request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 218
[2025-06-25 21:03:56 INF] Getting data references with RefId: 213
[2025-06-25 21:03:56 INF] Calling MobiFone GetDataReferences API - RefId: 213
[2025-06-25 21:03:56 INF] MobiFone GetDataReferences Response: "OK" - 4 chars
[2025-06-25 21:03:56 ERR] Failed to deserialize MobiFone GetDataReferences response - result is null
[2025-06-25 21:03:56 ERR] Error occurred while getting data references from MobiFone Invoice API: Không thể chuyển đổi dữ liệu từ MobiFone GetDataReferences API
Applications.Exceptions.MobiFoneApiDeserializationException: Không thể chuyển đổi dữ liệu từ MobiFone GetDataReferences API
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 109
   at Infrastructure.Services.MobiFoneInvoiceService.GetDataReferencesAsync(GetDataReferencesRequest request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 218
[2025-06-25 21:03:57 INF] Getting data references with RefId: 213
[2025-06-25 21:03:57 INF] Calling MobiFone GetDataReferences API - RefId: 213
[2025-06-25 21:03:57 INF] MobiFone GetDataReferences Response: "OK" - 4 chars
[2025-06-25 21:03:57 ERR] Failed to deserialize MobiFone GetDataReferences response - result is null
[2025-06-25 21:03:57 ERR] Error occurred while getting data references from MobiFone Invoice API: Không thể chuyển đổi dữ liệu từ MobiFone GetDataReferences API
Applications.Exceptions.MobiFoneApiDeserializationException: Không thể chuyển đổi dữ liệu từ MobiFone GetDataReferences API
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 109
   at Infrastructure.Services.MobiFoneInvoiceService.GetDataReferencesAsync(GetDataReferencesRequest request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 218
[2025-06-25 21:04:31 INF] Getting data references with RefId: RF00059
[2025-06-25 21:04:31 INF] Calling MobiFone GetDataReferences API - RefId: RF00059
[2025-06-25 21:04:31 INF] MobiFone GetDataReferences Response: "OK" - 87237 chars
[2025-06-25 21:04:31 INF] MobiFone GetDataReferences API call completed successfully
[2025-06-25 21:04:31 INF] Successfully retrieved 147 invoice templates
[2025-06-25 21:14:44 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-25 21:14:44 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-25 21:14:44 INF] MobiFone CreateInvoice Response: "OK" - 86 chars
[2025-06-25 21:14:44 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-25 21:14:44 INF] Successfully created invoice with response: 1 items
[2025-06-25 21:16:08 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-25 21:16:08 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-25 21:16:08 INF] MobiFone CreateInvoice Response: "OK" - 86 chars
[2025-06-25 21:16:08 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-25 21:16:08 INF] Successfully created invoice with response: 1 items
[2025-06-25 21:16:19 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-25 21:16:19 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-25 21:16:19 INF] MobiFone CreateInvoice Response: "OK" - 86 chars
[2025-06-25 21:16:19 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-25 21:16:19 INF] Successfully created invoice with response: 1 items
[2025-06-25 21:16:20 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-25 21:16:20 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-25 21:16:20 INF] MobiFone CreateInvoice Response: "OK" - 86 chars
[2025-06-25 21:16:20 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-25 21:16:20 INF] Successfully created invoice with response: 1 items
[2025-06-25 21:16:40 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-25 21:16:40 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-25 21:16:40 INF] MobiFone CreateInvoice Response: "OK" - 86 chars
[2025-06-25 21:16:40 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-25 21:16:40 INF] Successfully created invoice with response: 1 items
[2025-06-25 21:16:41 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-25 21:16:41 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-25 21:16:41 INF] MobiFone CreateInvoice Response: "OK" - 86 chars
[2025-06-25 21:16:41 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-25 21:16:41 INF] Successfully created invoice with response: 1 items
[2025-06-25 21:16:52 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-25 21:16:52 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-25 21:16:52 INF] MobiFone CreateInvoice Response: "OK" - 86 chars
[2025-06-25 21:16:52 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-25 21:16:52 INF] Successfully created invoice with response: 1 items
[2025-06-25 21:16:56 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-25 21:16:56 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-25 21:16:56 INF] MobiFone CreateInvoice Response: "OK" - 86 chars
[2025-06-25 21:16:56 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-25 21:16:56 INF] Successfully created invoice with response: 1 items
[2025-06-25 21:17:23 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-25 21:17:23 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-25 21:17:23 INF] MobiFone CreateInvoice Response: "OK" - 47 chars
[2025-06-25 21:17:23 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-25 21:17:23 INF] Successfully created invoice with response: 1 items
[2025-06-25 21:17:25 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-25 21:17:25 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-25 21:17:26 INF] MobiFone CreateInvoice Response: "OK" - 47 chars
[2025-06-25 21:17:26 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-25 21:17:26 INF] Successfully created invoice with response: 1 items
[2025-06-25 21:17:43 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-25 21:17:43 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-25 21:19:50 INF] MobiFone CreateInvoice Response: "OK" - 47 chars
[2025-06-25 21:19:50 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-25 21:19:50 INF] Successfully created invoice with response: 1 items
[2025-06-25 21:19:56 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-25 21:19:56 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-25 21:19:57 INF] MobiFone CreateInvoice Response: "OK" - 47 chars
[2025-06-25 21:19:57 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-25 21:19:57 INF] Successfully created invoice with response: 1 items
[2025-06-25 21:20:00 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-25 21:20:00 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-25 21:20:00 INF] MobiFone CreateInvoice Response: "OK" - 47 chars
[2025-06-25 21:20:00 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-25 21:20:00 INF] Successfully created invoice with response: 1 items
[2025-06-25 21:20:05 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-25 21:20:05 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-25 21:20:20 INF] MobiFone CreateInvoice Response: "OK" - 47 chars
[2025-06-25 21:20:20 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-25 21:20:20 INF] Successfully created invoice with response: 1 items
[2025-06-25 21:20:22 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-25 21:20:22 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-25 21:20:23 INF] MobiFone CreateInvoice Response: "OK" - 47 chars
[2025-06-25 21:20:23 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-25 21:20:23 INF] Successfully created invoice with response: 1 items
[2025-06-25 21:21:25 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-25 21:21:25 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-25 21:21:26 INF] MobiFone CreateInvoice Response: "OK" - 58 chars
[2025-06-25 21:21:26 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-25 21:21:26 INF] Successfully created invoice with response: 1 items
[2025-06-25 21:21:30 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-25 21:21:30 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-25 21:22:17 INF] MobiFone CreateInvoice Response: "OK" - 58 chars
[2025-06-25 21:22:17 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-25 21:22:17 INF] Successfully created invoice with response: 1 items
[2025-06-25 21:24:36 INF] Getting data references with RefId: RF00059
[2025-06-25 21:24:36 INF] Calling MobiFone GetDataReferences API - RefId: RF00059
[2025-06-25 21:24:46 INF] MobiFone GetDataReferences Response: "OK" - 87237 chars
[2025-06-25 21:24:46 INF] MobiFone GetDataReferences API call completed successfully
[2025-06-25 21:24:46 INF] Successfully retrieved 147 invoice templates
[2025-06-25 21:26:07 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-25 21:26:08 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-25 21:27:11 INF] MobiFone CreateInvoice Response: "OK" - 58 chars
[2025-06-25 21:27:12 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-25 21:27:12 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-25 21:27:12 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-25 21:27:12 INF] Successfully created invoice with response: 1 items
[2025-06-25 21:27:19 INF] MobiFone CreateInvoice Response: "OK" - 2843 chars
[2025-06-25 21:27:19 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-25 21:27:19 INF] Successfully created invoice with response: 1 items
[2025-06-25 21:29:32 ERR] ❌ Unhandled exception. TraceId: 0HNDJTTDMEI2S:00000003
System.NullReferenceException: Object reference not set to an instance of an object.
   at Applications.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Applications/Behaviors/ValidationBehavior.cs:line 42
   at API.Controllers.MobiFoneInvoiceController.SignInvoiceCertFile68Async(SignInvoiceCertFile68Request request, String token, String maDvcs) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Controllers/MobiFoneInvoiceController.cs:line 315
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at API.Middlewares.SerilogEnrichMiddleware.Invoke(HttpContext context) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/SerilogEnrichMiddleware.cs:line 24
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at ExceptionHandlingMiddleware.Invoke(HttpContext context) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/ExceptionHandlingMiddleware.cs:line 25
[2025-06-25 21:31:35 INF] SignInvoiceCertFile68 with Data count: 1
[2025-06-25 21:31:35 INF] Calling MobiFone SignInvoiceCertFile68 API - Data count: 1
[2025-06-25 21:31:44 INF] MobiFone SignInvoiceCertFile68 Response: "OK" - 90 chars
[2025-06-25 21:31:44 INF] MobiFone SignInvoiceCertFile68 API call completed successfully
[2025-06-25 21:31:44 INF] Successfully executed SignInvoiceCertFile68 with status: Đã ký
[2025-06-25 21:33:08 ERR] ❌ Unhandled exception. TraceId: 0HNDJTTDMEI2V:00000003
System.NullReferenceException: Object reference not set to an instance of an object.
   at Applications.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Applications/Behaviors/ValidationBehavior.cs:line 42
   at API.Controllers.MobiFoneInvoiceController.SendInvoiceToCQT68Async(SendInvoiceToCQT68Request request, String token, String maDvcs) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Controllers/MobiFoneInvoiceController.cs:line 341
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at API.Middlewares.SerilogEnrichMiddleware.Invoke(HttpContext context) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/SerilogEnrichMiddleware.cs:line 24
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at ExceptionHandlingMiddleware.Invoke(HttpContext context) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/ExceptionHandlingMiddleware.cs:line 25
[2025-06-25 21:34:34 INF] SendInvoiceToCQT68 with Invoice count: 1, TypeCmd: 206
[2025-06-25 21:34:34 INF] Calling MobiFone SendInvoiceToCQT68 API - Invoice count: 1
[2025-06-25 21:34:39 INF] MobiFone SendInvoiceToCQT68 Response: "OK" - 49 chars
[2025-06-25 21:34:39 INF] MobiFone SendInvoiceToCQT68 API call completed successfully
[2025-06-25 21:34:39 INF] Successfully executed SendInvoiceToCQT68 with status: null
[2025-06-25 21:39:01 INF] SendInvoiceToCQT68 with Invoice count: 1, TypeCmd: 206
[2025-06-25 21:39:01 INF] Calling MobiFone SendInvoiceToCQT68 API - Invoice count: 1
[2025-06-25 21:39:06 INF] MobiFone SendInvoiceToCQT68 Response: "OK" - 58 chars
[2025-06-25 21:39:06 INF] MobiFone SendInvoiceToCQT68 API call completed successfully
[2025-06-25 21:39:06 INF] Successfully executed SendInvoiceToCQT68 with status: Đã gửi hóa đơn tới Cơ quan thuế
