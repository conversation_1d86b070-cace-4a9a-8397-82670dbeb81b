[2025-06-30 21:37:56 INF] Attempting to login to MobiFone Invoice API for user: <EMAIL>
[2025-06-30 21:37:56 INF] Calling MobiFone Login API for Username: <EMAIL> - Environment: Test
[2025-06-30 21:37:56 INF] MobiFone Login Response: "OK" - 343 chars
[2025-06-30 21:37:56 INF] MobiFone Login API call completed successfully
[2025-06-30 21:39:31 ERR] Unexpected exception in TimingPipeFlusher.FlushAsync.
System.IO.IOException: The encryption operation failed, see inner exception.
 ---> System.ComponentModel.Win32Exception (14): Bad address
   --- End of inner exception stack trace ---
   at System.Net.Security.SslStream.WriteSingleChunk[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.ConcurrentPipeWriter.FlushAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.TimingPipeFlusher.FlushAsync(MinDataRate minRate, Int64 count, IHttpOutputAborter outputAborter, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2FrameWriter.WriteGoAwayAsync(Int32 lastStreamId, Http2ErrorCode errorCode)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.UpdateConnectionState()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.ProcessRequestsAsync[TContext](IHttpApplication`1 application)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecuteFromThreadPool(Thread threadPoolThread)
   at System.Threading.ThreadPoolWorkQueue.Dispatch()
   at System.Threading.PortableThreadPool.WorkerThread.WorkerThreadStart()
   at System.Threading.Thread.StartCallback()
--- End of stack trace from previous location ---
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.ConcurrentPipeWriter.FlushAsyncAwaited(ValueTask`1 flushTask, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.TimingPipeFlusher.TimeFlushAsyncAwaited(ValueTask`1 pipeFlushTask, MinDataRate minRate, IHttpOutputAborter outputAborter, CancellationToken cancellationToken)
[2025-06-30 21:39:31 ERR] Unhandled exception while processing 0HNDNSE2T8L6C.
System.IO.IOException: The encryption operation failed, see inner exception.
 ---> System.ComponentModel.Win32Exception (14): Bad address
   --- End of inner exception stack trace ---
   at System.Net.Security.SslStream.WriteSingleChunk[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Threading.Tasks.Task`1.TrySetResult(TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.HttpConnection.ProcessRequestsAsync[TContext](IHttpApplication`1 httpApplication)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Threading.Tasks.Task`1.TrySetResult(TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.ProcessRequestsAsync[TContext](IHttpApplication`1 application)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecuteFromThreadPool(Thread threadPoolThread)
   at System.Threading.ThreadPoolWorkQueue.Dispatch()
   at System.Threading.PortableThreadPool.WorkerThread.WorkerThreadStart()
   at System.Threading.Thread.StartCallback()
--- End of stack trace from previous location ---
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.KestrelConnection`1.ExecuteAsync()
[2025-06-30 21:39:32 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-30 21:39:32 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-30 21:39:34 INF] MobiFone CreateInvoice Response: "OK" - 87 chars
[2025-06-30 21:39:34 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-30 21:39:34 INF] Successfully created invoice with response: 1 items
[2025-06-30 21:39:59 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-30 21:39:59 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-30 21:40:01 INF] MobiFone CreateInvoice Response: "OK" - 87 chars
[2025-06-30 21:40:01 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-30 21:40:01 INF] Successfully created invoice with response: 1 items
[2025-06-30 21:41:21 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-30 21:41:21 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-30 21:41:30 INF] MobiFone CreateInvoice Response: "OK" - 87 chars
[2025-06-30 21:41:30 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-30 21:41:30 INF] Successfully created invoice with response: 1 items
[2025-06-30 21:41:42 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-06-30 21:41:42 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-06-30 21:41:46 INF] MobiFone CreateInvoice Response: "OK" - 2843 chars
[2025-06-30 21:41:46 INF] MobiFone CreateInvoice API call completed successfully
[2025-06-30 21:41:46 INF] Successfully created invoice with response: 1 items
[2025-06-30 22:13:14 INF] GetHistoryInvoice for invoice ID: efdfd473-cc54-44d9-a605-817634527877
[2025-06-30 22:13:14 INF] Calling MobiFone GetHistoryInvoice API for invoice: efdfd473-cc54-44d9-a605-817634527877
[2025-06-30 22:13:15 INF] MobiFone GetHistoryInvoice Response: "Unauthorized" - 61 chars
[2025-06-30 22:13:15 WRN] MobiFone GetHistoryInvoice API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-06-30 22:13:15 ERR] Error occurred while getting history for invoice efdfd473-cc54-44d9-a605-817634527877 in MobiFone Invoice API: MobiFone GetHistoryInvoice API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone GetHistoryInvoice API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 109
   at Infrastructure.Services.MobiFoneInvoiceService.GetHistoryInvoiceAsync(String id, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 960
[2025-06-30 22:13:28 INF] GetHistoryInvoice for invoice ID: efdfd473-cc54-44d9-a605-817634527877
[2025-06-30 22:13:28 INF] Calling MobiFone GetHistoryInvoice API for invoice: efdfd473-cc54-44d9-a605-817634527877
[2025-06-30 22:13:28 INF] MobiFone GetHistoryInvoice Response: "OK" - 2 chars
[2025-06-30 22:13:28 INF] MobiFone GetHistoryInvoice API call completed successfully
[2025-06-30 22:13:28 INF] Successfully retrieved history for invoice efdfd473-cc54-44d9-a605-817634527877 with 0 records
[2025-06-30 22:15:40 INF] GetInvoiceById for invoice ID: efdfd473-cc54-44d9-a605-817634527877
[2025-06-30 22:15:40 INF] MobiFone GetInvoiceById Response: "Unauthorized" - 61 chars
[2025-06-30 22:15:40 WRN] MobiFone GetInvoiceById API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-06-30 22:15:40 ERR] Error occurred while getting invoice by ID efdfd473-cc54-44d9-a605-817634527877 in MobiFone Invoice API: MobiFone GetInvoiceById API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone GetInvoiceById API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 109
   at Infrastructure.Services.MobiFoneInvoiceService.GetInvoiceByIdAsync(GetInvoiceByIdRequest request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 1247
[2025-06-30 22:16:33 INF] GetInvoiceById for invoice ID: efdfd473-cc54-44d9-a605-817634527877
[2025-06-30 22:16:35 INF] MobiFone GetInvoiceById Response: "OK" - 3421 chars
[2025-06-30 22:16:35 INF] MobiFone GetInvoiceById API call completed successfully
[2025-06-30 22:16:35 INF] Successfully retrieved invoice details for efdfd473-cc54-44d9-a605-817634527877
[2025-06-30 22:16:45 INF] GetInvoiceById for invoice ID: efdfd473-cc54-44d9-a605-817634527877
