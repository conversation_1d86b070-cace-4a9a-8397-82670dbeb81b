{"ConnectionStrings": {"DefaultConnection": "Server=************;User Id=mekongtech;Password=password@postgres;Port=5432;Database=ZEN-INVOICE;Pooling=true"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "MobiFoneInvoice": {"Environment": "Test", "ProductionBaseUrl": "", "TestBaseUrl": "http://mobiinvoice.vn:9000", "TaxCode": "0123456789", "Username": "<EMAIL>", "Password": "Ru51(WMQQ5", "TimeoutSeconds": 30}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File", "Serilog.Sinks.Seq"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "Enrich": ["FromLogContext", "WithThreadId", "WithEnvironmentUserName"], "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "Logs/log-.txt", "rollingInterval": "Day", "shared": true, "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {TraceId}{NewLine}{Exception}"}}, {"Name": "Seq", "Args": {"serverUrl": "http://localhost:5341"}}]}}