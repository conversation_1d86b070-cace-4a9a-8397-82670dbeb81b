﻿using System.Diagnostics;

namespace API.Middlewares
{
    public class TraceIdMiddleware
    {
        private readonly RequestDelegate _next;

        public TraceIdMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task Invoke(HttpContext context)
        {
            // Nếu chưa có Activity, tạo mới Activity cho TraceId
            if (Activity.Current == null)
            {
                var activity = new Activity("IncomingRequest");
                activity.Start();
            }

            // Tiếp tục x<PERSON> lý request
            await _next(context);
        }
    }
}
