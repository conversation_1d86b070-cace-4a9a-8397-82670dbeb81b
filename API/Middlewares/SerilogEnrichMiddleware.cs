﻿namespace API.Middlewares
{
    public class SerilogEnrichMiddleware
    {
        private readonly RequestDelegate _next;

        public SerilogEnrichMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task Invoke(HttpContext context)
        {
            var traceId = context.TraceIdentifier;

            // Optional: extract userId from authenticated user
            var userId = context.User?.Identity?.IsAuthenticated == true
                ? context.User.FindFirst("sub")?.Value
                : null;

            using (Serilog.Context.LogContext.PushProperty("TraceId", traceId))
            using (userId != null ? Serilog.Context.LogContext.PushProperty("UserId", userId) : null)
            {
                await _next(context);
            }
        }
    }
}
