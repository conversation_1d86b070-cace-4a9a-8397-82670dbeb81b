{
  "ConnectionStrings": {
    "DefaultConnection": "Server=14.225.217.6;User Id=mekongtech;Password=password@postgres;Port=5432;Database=ZEN-INVOICE;Pooling=true"
  },

  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },

  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "Microsoft.AspNetCore": "Warning"
      }
    },
    "Enrich": [ "FromLogContext", "WithMachineName", "WithThreadId" ],
    "WriteTo": [
      { "Name": "Console" },
      {
        "Name": "File",
        "Args": {
          "path": "Logs/log-.txt",
          "rollingInterval": "Day",
          "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"
        }
      }
    ]
  },

  "JwtSettings": {
    "Secret": "YourVeryStrongSecretKeyHere1234567890",
    "Issuer": "ZenShopAPI",
    "Audience": "ZenShopClient",
    "ExpiryInMinutes": 720
  },
  "NapasConfiguration": {
    "BaseUrl": "https://apg-stg.napas.com.vn",
    "TokenEndpoint": "/apg/oauth2/token",
    "InvestigationEndpoint": "/apg/investigation",
    "NotificationEndpoint": "/apg/notification",
    "ReconciliationEndpoint": "/apg/reconciliation",
    "ClientKey": "USER971220",
    "ClientSecret": "b3huAXe6bVStMfTBspgbGerXBy7t4SF7",
    "ClientCertPath": "/etc/ssl/zenpay-tingting/mtls/napas-signed/ach-client-apg-ZENPAY.pfx",
    "PrivateKeyPemPath": "/etc/ssl/zenpay-tingting/mtls/client-privatekey.key",
    "PublicKeyPemPath": "/etc/ssl/zenpay-tingting/mtls/napas-signed/ach-client-apg-ZENPAY.cer",
    "ClientCertPassword": "ZenPay",
    //"NapasPayloadCertPath": "/etc/ssl/zenpay-tingting/payload/napas-ca.crt",
    "NapasPayloadCertPath": "C:\\Users\\<USER>\\Music\\zenpay-tingting\\payload\\napas-ca.crt",
    "HeaderRequestSenderId": "970421",
    "HeaderRequestReceiverId": "971133"

  },
  // Cấu trúc định danh giữa NAPAS và ZenPay-TingTing
  "MerchantMappingConfig": {
    "Bank": "NP",
    "MasterMerchant": "HD",
    "Merchant": "MZP",
    "Branch": "ZTT",
    "Cashier": "01"
  },
  "MBFSMSConfig": {
    "BaseUrl": "http://210.211.109.118",
    "SendSMSPath": "/apibrandname/ApiSendSms",
    "CheckBalancePath": "/apibrandname/CheckBalance",
    "Sendby": "brand", // Mã định danh ZenPay trong TCPTML của NP
    "Password": "ZenPay@@321!@#", // Merchant của ZenPay
    "Brandname": "ZENPAY", // Chi nhánh,điểm bán của cửa hàng,Chú ý số tài khoản thực sự nhận tiền có thể nằm ở mức Merchant hoặc mức Branch.
    "Username": "ZenPay",
    ///Unicode : 2 là gửi không dấu, = 1 hoặc giá trị "khác" "là","có" "dấu.","unicode": "1" //
    "Unicode": "1"
  },
  "AllowedHosts": "*"
}
