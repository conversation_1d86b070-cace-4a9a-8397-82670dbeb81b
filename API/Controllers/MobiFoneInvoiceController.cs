using Applications.DTOs.MobiFoneInvoice.GetDataReferences;
using Applications.DTOs.MobiFoneInvoice.GetListCertificatesFile68;
using Applications.DTOs.MobiFoneInvoice.Login;
using Applications.DTOs.MobiFoneInvoice.CreateInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateSalesInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreatePublicAssetSalesInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateNationalReserveSalesInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateOtherInvoice.Raws;
using Applications.Features.MobiFoneInvoice.Commands;
using Applications.DTOs.MobiFoneInvoice.CreatePXKVCNBInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreatePXKDLInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateInvoiceMTT.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateSalesInvoiceMTT.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateOtherInvoiceMTT.Raws;
using Applications.DTOs.MobiFoneInvoice.SaveAndSignHoadon78.Raws;
using Applications.DTOs.MobiFoneInvoice.SignInvoiceCertFile68.Raws;
using Applications.DTOs.MobiFoneInvoice.SendInvoiceToCQT68.Raws;
using Applications.DTOs.MobiFoneInvoice.SignAndSendInvoiceToCQT68.Raws;
using Applications.DTOs.MobiFoneInvoice.SendInvoiceByEmail.Raws;
using Applications.DTOs.MobiFoneInvoice.DownloadInvoicePDF.Raws;
using Applications.DTOs.MobiFoneInvoice.PrintMultipleInvoices.Raws;
using Applications.DTOs.MobiFoneInvoice.DeleteUnsignedInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CancelInvoiceWithoutCode.Raws;
using Applications.DTOs.MobiFoneInvoice.GetInvoiceById.Raws;
using Applications.DTOs.MobiFoneInvoice.GetHoadonFkey.Raws;
using Applications.DTOs.MobiFoneInvoice.GetInvoiceByTimeAndUnit.Raws;
using Applications.Features.MobiFoneInvoice.Queries;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers;

/// <summary>
/// Controller cho tích hợp MobiFone Invoice API
/// </summary>
// [Route("api/[controller]")]
// [ApiController]
public class MobiFoneInvoiceController(IMediator mediator) : BaseApiController
{

    /// <summary>
    /// Đăng nhập vào hệ thống MobiFone Invoice
    /// </summary>
    /// <param name="request">Thông tin đăng nhập</param>
    /// <returns>Token và thông tin user</returns>
    [HttpPost("login")]
    public async Task<IActionResult> LoginAsync([FromBody] LoginRequest request)
    {
        var command = new LoginCommand(request);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Lấy thông tin dải ký hiệu mẫu số hóa đơn
    /// </summary>
    /// <param name="request">Thông tin request</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Danh sách thông tin dải ký hiệu</returns>
    [HttpGet("data-references")]
    public async Task<IActionResult> GetDataReferencesAsync(
        [FromQuery] GetDataReferencesRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var query = new GetDataReferencesQuery(request, token, maDvcs);
        var result = await mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Lấy thông tin CKS (chứng thư số)
    /// </summary>
    /// <param name="request">Thông tin request</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Danh sách thông tin CKS</returns>
    [HttpGet("certificates")]
    public async Task<IActionResult> GetListCertificatesFile68Async(
        [FromQuery] GetListCertificatesFile68Request request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var query = new GetListCertificatesFile68Query(request, token, maDvcs);
        var result = await mediator.Send(query);
        return Ok(result);
    }

    #region Tạo mới hóa đơn quy trình thường (tất cả hình thức HĐ)

    /// <summary>
    /// a. Hóa đơn Giá trị gia tăng
    /// </summary>
    /// <param name="request">Thông tin hóa đơn cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin hóa đơn đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("tao-moi-hoa-don-gtgt-quy-trinh-thuong")]
    public async Task<IActionResult> TaoMoiHoaDonGTGTQuyTrinhThuongAsync(
        [FromBody] SaveListHoadon78Request request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new TaoMoiHoaDonGTGTQuyTrinhThuongCommand(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// b. Tạo mới Hóa đơn Bán hàng
    /// </summary>
    /// <param name="request">Thông tin hóa đơn bán hàng cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin hóa đơn bán hàng đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("tao-moi-hoa-don-ban-hang-quy-trinh-thuong")]
    public async Task<IActionResult> TaoMoiHoaDonBanHangQuyTrinhThuongAsync(
        [FromBody] SaveListHoadonBanHangRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new TaoMoiHoaDonBanHangQuyTrinhThuongCommand(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// c. Tạo mới Hóa đơn Bán tài sản công
    /// </summary>
    /// <param name="request">Thông tin hóa đơn bán tài sản công cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin hóa đơn bán tài sản công đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("tao-moi-hoa-don-ban-tai-san-cong-quy-trinh-thuong")]
    public async Task<IActionResult> TaoMoiHoaDonBanTaiSanCongQuyTrinhThuongAsync(
        [FromBody] SaveListHoadonBanTaiSanCongRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new TaoMoiHoaDonBanTaiSanCongQuyTrinhThuongCommand(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// d. Tạo mới Hóa đơn Bán hàng dự trữ quốc gia
    /// </summary>
    /// <param name="request">Thông tin hóa đơn bán hàng dự trữ quốc gia cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin hóa đơn bán hàng dự trữ quốc gia đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("tao-moi-hoa-don-ban-hang-du-tru-quoc-gia-quy-trinh-thuong")]
    public async Task<IActionResult> TaoMoiHoaDonBanHangDuTruQuocGiaQuyTrinhThuongAsync(
        [FromBody] SaveListHoadonBanHangDuTruQuocGiaRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new TaoMoiHoaDonBanHangDuTruQuocGiaQuyTrinhThuongCommand(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// e. Tạo mới Hóa đơn khác (Tem, vé, thẻ, phiếu...)
    /// </summary>
    /// <param name="request">Thông tin hóa đơn khác cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin hóa đơn khác đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("tao-moi-hoa-don-khac-quy-trinh-thuong")]
    public async Task<IActionResult> TaoMoiHoaDonKhacQuyTrinhThuongAsync(
        [FromBody] SaveListHoaDonKhacRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new TaoMoiHoaDonKhacQuyTrinhThuongCommand(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// g. Tạo mới Phiếu xuất kho kiêm vận chuyển nội bộ (PXKVCNB)
    /// </summary>
    /// <param name="request">Thông tin phiếu xuất kho kiêm vận chuyển nội bộ cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin phiếu xuất kho kiêm vận chuyển nội bộ đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("tao-moi-phieu-xuat-kho-kiem-van-chuyen-noi-bo-quy-trinh-thuong")]
    public async Task<IActionResult> TaoMoiPhieuXuatKhoKiemVanChuyenNoiBoQuyTrinhThuongAsync(
        [FromBody] SaveListHoadonPXKVCNBRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new TaoMoiHoaDonPXKVCNBQuyTrinhThuongCommand(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// h. Tạo mới Phiếu xuất kho hàng gửi bán đại lý (PXKDL)
    /// </summary>
    /// <param name="request">Thông tin phiếu xuất kho hàng gửi bán đại lý cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin phiếu xuất kho hàng gửi bán đại lý đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("tao-moi-phieu-xuat-kho-hang-gui-ban-dai-ly-quy-trinh-thuong")]
    public async Task<IActionResult> TaoMoiPhieuXuatKhoHangGuiBanDaiLyQuyTrinhThuongAsync(
        [FromBody] SaveListHoadonPXKDLRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new TaoMoiHoaDonPXKDLQuyTrinhThuongCommand(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    #endregion

    #region Tạo mới hóa đơn máy tính tiền sinh mã (SaveListHoadon78MTT)

    /// <summary>
    /// a. Hóa đơn Giá trị gia tăng máy tính tiền sinh mã
    /// </summary>
    /// <param name="request">Thông tin hóa đơn cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin hóa đơn đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("tao-moi-hoa-don-gtgt-may-tinh-tien-sinh-ma")]
    public async Task<IActionResult> TaoMoiHoaDonGTGTMayTinhTienSinhMaAsync(
        [FromBody] SaveListHoadon78MTTRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new TaoMoiHoaDonGTGTMayTinhTienSinhMaCommand(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// b. Hóa đơn Bán hàng máy tính tiền sinh mã
    /// </summary>
    /// <param name="request">Thông tin hóa đơn bán hàng cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin hóa đơn bán hàng đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("tao-moi-hoa-don-ban-hang-may-tinh-tien-sinh-ma")]
    public async Task<IActionResult> TaoMoiHoaDonBanHangMayTinhTienSinhMaAsync(
        [FromBody] SaveListHoadonBanHangMTTRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new TaoMoiHoaDonBanHangMayTinhTienSinhMaCommand(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// c. Hóa đơn khác (Tem, vé, thẻ, phiếu...) máy tính tiền sinh mã
    /// </summary>
    /// <param name="request">Thông tin hóa đơn khác cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin hóa đơn khác đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("tao-moi-hoa-don-khac-may-tinh-tien-sinh-ma")]
    public async Task<IActionResult> TaoMoiHoaDonKhacMayTinhTienSinhMaAsync(
        [FromBody] SaveListHoaDonKhacMTTRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new TaoMoiHoaDonKhacMayTinhTienSinhMaCommand(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    #endregion

    #region API 4.4: Save And Sign Hoadon78

    /// <summary>
    /// API 4.4: Hàm tạo mới và ký gửi hóa đơn bằng HSM nhà cung cấp khác, file mềm, Sim PKI
    /// URL: {{base_url}}/api/Invoice68/SaveAndSignHoadon78
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// </summary>
    /// <param name="request">Thông tin hóa đơn cần tạo và ký gửi (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin hóa đơn đã tạo và ký gửi (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("save-and-sign-hoadon78")]
    public async Task<IActionResult> SaveAndSignHoadon78Async(
        [FromBody] SaveAndSignHoadon78Request request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new SaveAndSignHoadon78Command(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    #endregion

    #region API 4.5: Sign Invoice Cert File 68

    /// <summary>
    /// API 4.5: Ký chờ xử lý hóa đơn (bằng file mềm, SIM)
    /// URL: {{base_url}}/api/Invoice68/SignInvoiceCertFile68
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: Hàm này cho phép ký hóa đơn nhưng chưa gửi hóa đơn lên Cơ quan Thuế
    /// </summary>
    /// <param name="request">Thông tin hóa đơn cần ký (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin kết quả ký hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("sign-invoice-cert-file68")]
    public async Task<IActionResult> SignInvoiceCertFile68Async(
        [FromBody] SignInvoiceCertFile68Request request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new SignInvoiceCertFile68Command(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    #endregion

    #region API 4.6: Send Invoice To CQT 68

    /// <summary>
    /// API 4.6: Gửi hóa đơn đã ký lên Cơ quan thuế
    /// URL: {{base_url}}/api/Invoice68/SendInvoiceToCQT68
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: Hàm này cho phép gửi hóa đơn đã được ký tới Cơ quan Thuế
    /// </summary>
    /// <param name="request">Thông tin hóa đơn cần gửi CQT (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin kết quả gửi hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("send-invoice-to-cqt68")]
    public async Task<IActionResult> SendInvoiceToCQT68Async(
        [FromBody] SendInvoiceToCQT68Request request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new SendInvoiceToCQT68Command(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    #endregion

    #region API 4.7: Sign And Send Invoice To CQT 68

    /// <summary>
    /// API 4.7: Ký và gửi hóa đơn tới CQT (Chỉ dành cho file mềm, SIM)
    /// URL: {{base_url}}/api/Invoice68/SignInvoiceCertFile68
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: Hàm này cho phép ký và gửi hóa đơn lên Cơ quan thuế trong cùng một API
    /// </summary>
    /// <param name="request">Thông tin hóa đơn cần ký và gửi CQT (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin kết quả ký và gửi hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("sign-and-send-invoice-to-cqt68")]
    public async Task<IActionResult> SignAndSendInvoiceToCQT68Async(
        [FromBody] SignAndSendInvoiceToCQT68Request request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new SignAndSendInvoiceToCQT68Command(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    #endregion

    #region API 4.20: Get History Invoice

    /// <summary>
    /// API 4.20: Lấy danh sách lịch sử hóa đơn theo ID
    /// URL: {{base_url}}/api/Invoice68/GetHistoryInvoice?id={hdon_id}
    /// Method: GET
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: Hàm này cho phép lấy toàn bộ lịch sử truyền nhận dữ liệu của hóa đơn với Tổng cục thuế
    /// </summary>
    /// <param name="id">ID của hóa đơn cần lấy lịch sử</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Danh sách lịch sử hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpGet("get-history-invoice")]
    public async Task<IActionResult> GetHistoryInvoiceAsync(
        [FromQuery] string id,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var query = new GetHistoryInvoiceQuery(id, token, maDvcs);
        var result = await mediator.Send(query);
        return Ok(result);
    }

    #endregion

    #region API 4.8: Send Invoice By Email

    /// <summary>
    /// API 4.8: Gửi mail phát hành hóa đơn cho người mua
    /// URL: {{base_url}}/api/Invoice68/AutoSendInvoiceByEmail
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: API này cho phép người dùng gửi email thông báo xuất hóa đơn cho người mua
    /// </summary>
    /// <param name="request">Thông tin gửi email hóa đơn (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Kết quả gửi email hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("send-invoice-by-email")]
    public async Task<IActionResult> SendInvoiceByEmailAsync(
        [FromBody] SendInvoiceByEmailRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new SendInvoiceByEmailCommand(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    #endregion

    #region API 4.9: Download Invoice PDF

    /// <summary>
    /// API 4.9: Tải hóa đơn File .PDF
    /// URL: {{base_url}}/api/Invoice68/inHoadon?id={hdon_id}&type=PDF&inchuyendoi=false
    /// Method: GET
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: Hàm này cho phép người dùng tải file hóa đơn dạng PDF về máy
    /// </summary>
    /// <param name="id">ID của hóa đơn cần tải về máy</param>
    /// <param name="type">Định dạng file (mặc định: PDF)</param>
    /// <param name="inchuyendoi">Tùy chọn in thể hiện hay in chuyển đổi (mặc định: false)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>File PDF dưới dạng byte array</returns>
    [HttpGet("download-invoice-pdf")]
    public async Task<IActionResult> DownloadInvoicePDFAsync(
        [FromQuery] string id,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs,
        [FromQuery] string type = "PDF",
        [FromQuery] bool inchuyendoi = false)
    {
        var request = new DownloadInvoicePDFRequest
        {
            id = id,
            type = type,
            inchuyendoi = inchuyendoi
        };
        var query = new DownloadInvoicePDFQuery(request, token, maDvcs);
        var result = await mediator.Send(query);

        if (result.Code == "000" && result.Data != null)
        {
            return File(result.Data, "application/pdf", $"invoice_{id}.pdf");
        }

        return Ok(result);
    }

    #endregion

    #region API 4.10: Print Multiple Invoices

    /// <summary>
    /// API 4.10: In nhiều hóa đơn
    /// URL: {{base_url}}/api/Invoice68/InDanhSachHoaDon
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: API này cho phép người dùng in nhiều hóa đơn dưới định dạng PDF
    /// </summary>
    /// <param name="request">Thông tin in nhiều hóa đơn (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>File PDF chứa nhiều hóa đơn dưới dạng byte array</returns>
    [HttpPost("print-multiple-invoices")]
    public async Task<IActionResult> PrintMultipleInvoicesAsync(
        [FromBody] PrintMultipleInvoicesRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new PrintMultipleInvoicesCommand(request, token, maDvcs);
        var result = await mediator.Send(command);

        if (result.Code == "000" && result.Data != null)
        {
            return File(result.Data, "application/pdf", "multiple_invoices.pdf");
        }

        return Ok(result);
    }

    #endregion

    #region API 4.11: Delete Unsigned Invoice

    /// <summary>
    /// API 4.11: Xóa hóa đơn chưa ký gửi
    /// URL: {{base_url}}/api/Invoice68/hoadonXoaNhieu
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: API này cho phép người dùng xóa hóa đơn chưa ký gửi
    /// </summary>
    /// <param name="request">Thông tin xóa hóa đơn (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Kết quả xóa hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("delete-unsigned-invoice")]
    public async Task<IActionResult> DeleteUnsignedInvoiceAsync(
        [FromBody] DeleteUnsignedInvoiceRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new DeleteUnsignedInvoiceCommand(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    #endregion

    #region API 4.12: Cancel Invoice Without Code

    /// <summary>
    /// API 4.12: Hủy hóa đơn không mã
    /// URL: {{base_url}}/api/Invoice68/uploadCanceledInv?id={}
    /// Method: GET
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: API này cho phép người dùng hủy các hóa đơn không mã đã lập nhưng chưa được gửi lên Cơ quan Thuế (CQT)
    /// </summary>
    /// <param name="id">ID của hóa đơn cần hủy</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Kết quả hủy hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpGet("cancel-invoice-without-code")]
    public async Task<IActionResult> CancelInvoiceWithoutCodeAsync(
        [FromQuery] string id,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var request = new CancelInvoiceWithoutCodeRequest { id = id };
        var command = new CancelInvoiceWithoutCodeCommand(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    #endregion

    #region API 4.13: Get Invoice By ID

    /// <summary>
    /// API 4.13: Lấy thông tin hóa đơn theo ID
    /// URL: {{base_url}}/api/Invoice68/GetById?id={}
    /// Method: GET
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: API này cho phép người dùng lấy toàn bộ thông tin của một hóa đơn dựa trên ID của hóa đơn đó
    /// </summary>
    /// <param name="id">ID của hóa đơn cần lấy thông tin</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin chi tiết hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpGet("get-invoice-by-id")]
    public async Task<IActionResult> GetInvoiceByIdAsync(
        [FromQuery] string id,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var request = new GetInvoiceByIdRequest { id = id };
        var query = new GetInvoiceByIdQuery(request, token, maDvcs);
        var result = await mediator.Send(query);
        return Ok(result);
    }

    #endregion

    #region API 4.14: Get Hoadon Fkey

    /// <summary>
    /// API 4.14: Lấy danh sách hóa đơn theo FKEY hoặc Khoảng thời gian
    /// URL: {{base_url}}/api/Invoice68/GetHoadonFkey
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: API này cho phép người dùng lấy toàn bộ danh sách thông tin của hóa đơn theo mã FKEY hoặc theo khoảng thời gian lập hóa đơn
    /// </summary>
    /// <param name="request">Thông tin lấy danh sách hóa đơn (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Danh sách hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("get-hoadon-fkey")]
    public async Task<IActionResult> GetHoadonFkeyAsync(
        [FromBody] GetHoadonFkeyRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var query = new GetHoadonFkeyQuery(request, token, maDvcs);
        var result = await mediator.Send(query);
        return Ok(result);
    }

    #endregion

    #region API 4.17: Get Invoice By Time And Unit

    /// <summary>
    /// API 4.17: Lấy danh sách hoá đơn theo thời gian, đơn vị và trạng thái
    /// URL: {{base_url}}/api/Invoice68/GetInvoiceByTimeAndUnit
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: Hàm này cho phép người dùng lấy danh sách hoá đơn thoả mãn khoảng thời gian cụ thể, đơn vị của người sử dụng và nằm trong các trạng thái cụ thể (Đã cấp mã, Chấp nhận TBSS, CQT đã nhận)
    /// </summary>
    /// <param name="request">Thông tin lấy danh sách hóa đơn theo thời gian và đơn vị (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Danh sách hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("get-invoice-by-time-and-unit")]
    public async Task<IActionResult> GetInvoiceByTimeAndUnitAsync(
        [FromBody] GetInvoiceByTimeAndUnitRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var query = new GetInvoiceByTimeAndUnitQuery(request, token, maDvcs);
        var result = await mediator.Send(query);
        return Ok(result);
    }

    #endregion
}
