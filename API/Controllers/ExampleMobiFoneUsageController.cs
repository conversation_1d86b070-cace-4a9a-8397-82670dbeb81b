// using Applications.DTOs.MobiFoneInvoice.GetDataReferences;
// using Applications.DTOs.MobiFoneInvoice.Login;
// using Applications.Interfaces.Services;
// using Microsoft.AspNetCore.Mvc;
//
// namespace API.Controllers;
//
// /// <summary>
// /// Example usage của MobiFone Invoice Service với HttpClient pattern
// /// </summary>
// [ApiController]
// [Route("api/[controller]")]
// public class ExampleMobiFoneUsageController : ControllerBase
// {
//     private readonly IMobiFoneInvoiceService _mobiFoneService;
//     private readonly ILogger<ExampleMobiFoneUsageController> _logger;
//
//     public ExampleMobiFoneUsageController(
//         IMobiFoneInvoiceService mobiFoneService,
//         ILogger<ExampleMobiFoneUsageController> logger)
//     {
//         _mobiFoneService = mobiFoneService;
//         _logger = logger;
//     }
//
//     /// <summary>
//     /// Example: Login và lấy data references trong một flow
//     /// </summary>
//     [HttpPost("demo-flow")]
//     public async Task<IActionResult> DemoFlowAsync([FromBody] LoginRequest loginRequest)
//     {
//         try
//         {
//             _logger.LogInformation("Starting MobiFone demo flow for user: {Username}", loginRequest.Username);
//
//             // Step 1: Login
//             var loginResult = await _mobiFoneService.LoginAsync(loginRequest);
//             
//             if (!loginResult.IsSuccess)
//             {
//                 _logger.LogError("Login failed: {Message}", loginResult.Message);
//                 return BadRequest(new { 
//                     error = "Login failed", 
//                     details = loginResult.Message,
//                     code = loginResult.Code
//                 });
//             }
//
//             var token = loginResult.Data!.Token;
//             var maDvcs = loginResult.Data.MaDvcs;
//             
//             _logger.LogInformation("Login successful. Token length: {TokenLength}, MaDvcs: {MaDvcs}", 
//                 token.Length, maDvcs);
//
//             // Step 2: Get Data References
//             var dataRequest = new GetDataReferencesRequest 
//             { 
//                 RefId = "RF00059",
//                 TaxCode = loginRequest.TaxCode // Optional for production
//             };
//
//             var dataResult = await _mobiFoneService.GetDataReferencesAsync(dataRequest, token, maDvcs);
//             
//             if (!dataResult.IsSuccess)
//             {
//                 _logger.LogError("GetDataReferences failed: {Message}", dataResult.Message);
//                 return BadRequest(new { 
//                     error = "GetDataReferences failed", 
//                     details = dataResult.Message,
//                     code = dataResult.Code
//                 });
//             }
//
//             _logger.LogInformation("Retrieved {Count} invoice templates", 
//                 dataResult.Data!.Data.Count);
//
//             // Return combined result
//             return Ok(new 
//             {
//                 login = loginResult,
//                 dataReferences = dataResult,
//                 summary = new 
//                 {
//                     success = true,
//                     invoiceTemplatesCount = dataResult.Data.Data.Count,
//                     firstTemplate = dataResult.Data.Data.FirstOrDefault()?.Khhdon
//                 }
//             });
//         }
//         catch (Exception ex)
//         {
//             _logger.LogError(ex, "Error in MobiFone demo flow");
//             return StatusCode(500, new { 
//                 error = "Internal server error", 
//                 details = ex.Message 
//             });
//         }
//     }
//
//     /// <summary>
//     /// Example: Test connection đơn giản
//     /// </summary>
//     [HttpPost("test-connection")]
//     public async Task<IActionResult> TestConnectionAsync()
//     {
//         var testLoginRequest = new LoginRequest
//         {
//             Username = "<EMAIL>",
//             Password = "Ru51(WMQQ5",
//             TaxCode = "0123456789"
//         };
//
//         var result = await _mobiFoneService.LoginAsync(testLoginRequest);
//         
//         return Ok(new 
//         {
//             success = result.IsSuccess,
//             message = result.Message,
//             code = result.Code,
//             hasToken = !string.IsNullOrEmpty(result.Data?.Token),
//             tokenLength = result.Data?.Token.Length ?? 0,
//             maDvcs = result.Data?.MaDvcs
//         });
//     }
// }
