﻿// using Applications.Features.ClientCredentials.Commands;
// using MediatR;
// using Microsoft.AspNetCore.Authorization;
// using Microsoft.AspNetCore.Mvc;
//
// namespace API.Controllers
// {
//     [Route("api/[controller]")]
//     [ApiController]
//     public class TokenController : ControllerBase
//     {
//         private readonly IMediator _mediator;
//
//         public TokenController(IMediator mediator)
//         {
//             _mediator = mediator;
//         }
//
//         [HttpPost("client-credential")]
//         [AllowAnonymous]
//         public async Task<IActionResult> GetToken([FromBody] ClientCredentialLoginCommand command)
//             => Ok(await _mediator.Send(command));
//     }
// }
