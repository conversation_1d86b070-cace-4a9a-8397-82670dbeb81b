﻿// using MediatR;
// using Microsoft.AspNetCore.Mvc;
// namespace API.Controllers
// {
//     [ApiController]
//     [Route("api/[controller]")]
//     public class PaymentController : ControllerBase
//     {
//         private readonly IMediator _mediator;
//         private readonly ILogger<PaymentController> _logger;
//
//         public PaymentController(ILogger<PaymentController> logger, IMediator mediator)
//         {
//             _logger = logger;
//             _mediator = mediator;
//         }
//
//         //[HttpPost("generate-qr")]
//         //public async Task<IActionResult> GenerateQr([FromBody] NapasQrGenReq req)
//         //{
//         //    var traceId = HttpContext.TraceIdentifier;
//         //    using (LogContext.PushProperty("TraceId", traceId))
//         //    {
//         //        _logger.LogInformation("🔁 [GenerateQr] Bắt đầu tạo QR. OrderCode: {OrderCode}, Amount: {Amount}, TraceId: {TraceId}",
//         //            req.OrderCode, req.Amount, traceId);
//
//         //        try
//         //        {
//         //            var command = new GenerateQrCommand { Request = req };
//         //            var result = await _mediator.Send(command);
//
//         //            if (result.IsSuccess)
//         //            {
//         //                _logger.LogInformation("✅ [GenerateQr] QR tạo thành công. F103: {F103}, TraceId: {TraceId}",
//         //                    req.MerchantCode + req.MerchantBranchCode + req.PaymentTerminal + req.OrderCode, traceId);
//
//         //                return Ok(result); // mã "00" sẽ tự được trả kèm
//         //            }
//         //            else
//         //            {
//         //                _logger.LogWarning("⚠️ [GenerateQr] QR tạo thất bại. Code: {Code}, Message: {Message}, TraceId: {TraceId}",
//         //                    result.Code, result.Message, traceId);
//
//         //                return StatusCode(MapHttpStatusCode(result.Code), result);
//         //            }
//         //        }
//         //        catch (Exception ex)
//         //        {
//         //            _logger.LogError(ex, "❌ [GenerateQr] Exception khi tạo QR. TraceId: {TraceId}", traceId);
//
//         //            var errorResponse = BaseResponse<string>.Error("Lỗi hệ thống khi tạo QR.", "500", traceId);
//         //            return StatusCode(500, errorResponse);
//         //        }
//         //    }
//         //}
//
//     }
// }
