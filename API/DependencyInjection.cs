﻿using Shared.Exceptions.Handler;

namespace API;

public static class DependencyInjection
{
    public static IServiceCollection AddApiServices(this IServiceCollection services)
    {
        // services.AddExceptionHandler<CustomExceptionHandler>();
        
        // services.AddCors(options =>
        // {
        //     options.AddPolicy(name: "CorsPolicy",
        //                     policy =>
        //                     {
        //                         policy.AllowAnyOrigin();
        //                         policy.AllowAnyHeader();
        //                         policy.AllowAnyMethod();
        //                     });
        // });
        return services;
    }
}