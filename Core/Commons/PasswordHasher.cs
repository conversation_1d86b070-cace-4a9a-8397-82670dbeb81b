﻿namespace Core.Commons
{
    public static class PasswordHasher
    {
        // <PERSON><PERSON> hóa mật khẩu (hash)
        public static string HashPassword(string password)
        {
            return BCrypt.Net.BCrypt.HashPassword(password);
        }

        // <PERSON><PERSON><PERSON> tra mật khẩu (verify)
        public static bool VerifyPassword(string password, string hashedPassword)
        {
            return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
        }
    }
}
